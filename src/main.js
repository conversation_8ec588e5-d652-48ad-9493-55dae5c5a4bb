import {createApp} from 'vue'

import {
    ActionBar,
    ActionBarButton,
    ActionBarIcon,
    AddressEdit,
    AddressList,
    Badge,
    Button,
    Card,
    Cascader,
    Cell,
    CellGroup,
    Checkbox,
    CheckboxGroup,
    ContactCard,
    DatePicker,
    Dialog,
    Divider,
    Empty,
    Field,
    Form,
    Icon,
    Image as VanImage,
    ImagePreview,
    List,
    Loading,
    Overlay,
    Picker,
    PickerGroup,
    Popover,
    Popup,
    PullRefresh,
    Radio,
    RadioGroup,
    Rate,
    Search,
    Skeleton,
    Slider,
    Stepper,
    SubmitBar,
    Swipe,
    SwipeCell,
    SwipeItem,
    Switch,
    Tab,
    Tabs,
    Tag,
    TimePicker,
    Toast,
    Uploader
} from 'vant'
import App from './App.vue'
import store from './store'
import router from './router'
import 'lib-flexible/flexible'
import 'vant/lib/index.css'; // 全局引入样式
import 'vant/es/toast/style'
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import "bpmn-js/dist/assets/diagram-js.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
import "bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css";
import MyPV from "./components/bpmnProcessDesigner/package/designer/index2";
import "./components/bpmnProcessDesigner/package/index";
import './permission'; // 权限
import ColorPicker from 'vue3-colorpicker'
import 'vue3-colorpicker/style.css';
import vkbeautify from 'vkbeautify';
// import hljs from 'highlight.js'
//样式文件，可选择node_modules-highlight.js-styles文件夹里其他高亮样式css
import 'highlight.js/styles/atom-one-dark.css'
// app.config.globalProperties.directive('highlight',function(el){  //自定义指令
//     let blocks = el.querySelectorAll('pre code');
//     blocks.forEach((block)=>{
//         hljs.highlightBlock(block)
//     })
// })
import x2js from 'x2js';

const app = createApp(App) // 创建实例

app.use(ColorPicker)

app.use(ElementPlus)

app.use(Avue);

app.config.globalProperties.$vkbeautify = vkbeautify;

app.config.globalProperties.$x2js = new x2js();  //创建x2js对象
// 全局过滤器
app.config.globalProperties.$filters = {
    prefix(url) {
        if (url && url.startsWith('http')) {
            return url
        } else {
            url = `http://backend-api-01.newbee.ltd${url}`
            return url
        }
    }
}

app.use(MyPV);

app.use(VanImage).use(ImagePreview).use(Uploader).use(Search).use(Cascader).use(Switch).use(Rate).use(Slider).use(TimePicker).use(RadioGroup).use(Radio).use(Badge).use(Picker).use(PickerGroup).use(DatePicker).use(Tag).use(Empty).use(ActionBarButton)
    .use(ActionBarIcon)
    .use(Popover)
    .use(ActionBar)
    .use(Divider)
    .use(Popup)
    .use(Overlay)
    .use(Loading)
    .use(Dialog)
    .use(Toast)
    .use(ContactCard)
    .use(Form)
    .use(AddressEdit)
    .use(AddressList)
    .use(Field)
    .use(CellGroup)
    .use(Cell)
    .use(SwipeCell)
    .use(Icon)
    .use(Stepper)
    .use(Card)
    .use(Button)
    .use(Swipe)
    .use(SwipeItem)
    .use(PullRefresh)
    .use(List)
    .use(Tab)
    .use(Tabs)
    .use(SubmitBar)
    .use(Checkbox)
    .use(CheckboxGroup)
    .use(Skeleton)

app.use(router)
app.use(store)

// import {
//     Calendar as MyCalendar,
// } from 'element-ui';
// import 'element-ui/lib/theme-chalk/calendar.css';
// app.use(MyCalendar);
app.mount('#app')

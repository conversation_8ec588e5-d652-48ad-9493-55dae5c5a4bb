import {createRouter, createWebHashHistory} from 'vue-router'

/**
 * 校历
 */
// import CALENDARROUTER from "@/views/calendar/router";
// import CALENDARVIEW from "@/views/calendar/views/View";

const router = createRouter({
    history: createWebHashHistory(), // hash模式：createWebHashHistory，history模式：createWebHistory
    routes: [
        // {
        //     path: '/calendar',
        //     component: CALENDARVIEW,
        //     children: CALENDARROUTER,
        //     meta: {
        //         index: 1
        //     }
        // },
        {
            path: '/choujiang',
            name: 'choujiang',
            component: () => import(/* webpackChunkName: "home" */ '@/views/choujiang/views/index.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/calendar',
            name: 'calendar',
            component: () => import(/* webpackChunkName: "home" */ '@/views/calendar/views/index.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/',
            redirect: '/mhhome'
        },
        {
            path: '/portal',
            redirect: '/mhhome'
        },
        {
            path: '/home',
            name: 'home',
            component: () => import(/* webpackChunkName: "home" */ '@/views/Home.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mhhome',
            name: 'mhhome',
            component: () => import(/* webpackChunkName: "home" */ '@/views/mhhome.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/dianhua',
            name: 'dianhua',
            component: () => import(/* webpackChunkName: "home" */ '@/views/dianhua.vue'),
            meta: {
                // index: 1
                index: 2
            }
        },
        {
            path: '/dianhua2',
            name: 'dianhua2',
            component: () => import(/* webpackChunkName: "home" */ '@/views/dianhua2.vue'),
            meta: {
                // index: 1
                index: 2
            }
        },
        {
            path: '/dianhua3',
            name: 'dianhua3',
            component: () => import(/* webpackChunkName: "home" */ '@/views/dianhua3.vue'),
            meta: {
                // index: 1
                index: 2
            }
        },
        {
            path: '/service',
            name: 'service',
            component: () => import(/* webpackChunkName: "home" */ '@/views/serviceindex.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/yingyong',
            name: 'yingyong',
            component: () => import(/* webpackChunkName: "home" */ '@/views/yingyong.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/zixun',
            name: 'zixun',
            component: () => import(/* webpackChunkName: "home" */ '@/views/zixun.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/wodexinxi-list',
            name: 'wodexinxi-list',
            component: () => import(/* webpackChunkName: "login" */ '@/views/DesktopModel/wodexinxiList.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/jichengxitong-list',
            name: 'jichengxitong-list',
            component: () => import(/* webpackChunkName: "login" */ '@/views/DesktopModel/list/jichengxitongList.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/news-list',
            name: 'news-list',
            component: () => import(/* webpackChunkName: "login" */ '@/views/DesktopModel/list/newsList'),
            meta: {
                index: 1
            }
        },
        {
            path: '/tongji-index',
            name: 'tongji-index',
            component: () => import(/* webpackChunkName: "login" */ '@/views/tongJi/index.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/new-tongji-index',
            name: 'new-tongji-index',
            component: () => import(/* webpackChunkName: "login" */ '@/views/tongJi/newindex.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/tongji-second-index',
            name: 'tongji-second-index',
            component: () => import(/* webpackChunkName: "login" */ '@/views/tongJi/secondIndex.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/new-tongji-second-index',
            name: 'new-tongji-second-index',
            component: () => import(/* webpackChunkName: "login" */ '@/views/tongJi/newcecondindex.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-service',
            name: 'mine-service',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineService.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/new-mine-service',
            name: 'new-mine-service',
            component: () => import(/* webpackChunkName: "login" */ '@/views/newMineService.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-service-details',
            name: 'mine-service-details',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineServiceDetails.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/mine-service-recourse',
            name: 'mine-service-recourse',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineServiceRecourse.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-service-taskFill',
            name: 'mine-service-taskFill',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineServiceTaskFill.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-service-task-detail',
            name: 'mine-service-task-detail',
            component: () => import(/* webpackChunkName: "login" */ '@/views/taskFill/detail.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-service-task-edit',
            name: 'mine-service-task-edit',
            component: () => import(/* webpackChunkName: "login" */ '@/views/taskFill/edit.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/new-detail',
            name: 'new-detail',
            component: () => import(/* webpackChunkName: "login" */ '@/views/NewDetail.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-dealt',
            name: 'mine-dealt',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineDealt.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-dealt-create',
            name: 'mine-dealt-create',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineDealtCreate.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-dealt-detail',
            name: 'mine-dealt-detail',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineDealtDetail.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-message-type',
            name: 'mine-message-type',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineMessageType.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/mine-message',
            name: 'mine-message',
            component: () => import(/* webpackChunkName: "login" */ '@/views/MineMessage.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/application',
            name: 'application',
            component: () => import(/* webpackChunkName: "login" */ '@/views/Application.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/search',
            name: 'search',
            component: () => import(/* webpackChunkName: "login" */ '@/views/Search.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import(/* webpackChunkName: "login" */ '@/views/Login.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/news-center',
            name: 'news-center',
            component: () => import(/* webpackChunkName: "login" */ '@/views/NewsCenter.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/chat',
            name: 'chat',
            component: () => import(/* webpackChunkName: "user" */ '@/views/chat.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/user',
            name: 'user',
            component: () => import(/* webpackChunkName: "user" */ '@/views/User.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/newuser',
            name: 'newuser',
            component: () => import(/* webpackChunkName: "user" */ '@/views/newUser.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/setting',
            name: 'setting',
            component: () => import(/* webpackChunkName: "setting" */ '@/views/Setting.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/kebiaodetail',
            name: 'kebiaodetail',
            component: () => import(/* webpackChunkName: "login" */ '@/views/DesktopModel/list/kebiaodetail'),
            meta: {
                index: 1
            }
        },
        {
            path: '/addressBbookView',
            name: 'addressBbookView',
            component: () => import(/* webpackChunkName: "login" */ '@/views/table'),
            meta: {
                index: 1
            }
        },
        {
            path: '/fileDownloadList',
            name: 'fileDownloadList',
            component: () => import(/* webpackChunkName: "login" */ '@/views/filetable'),
            meta: {
                index: 1
            }
        },
        {
            path: '/zxquestion',
            name: 'zxquestion',
            component: () => import(/* webpackChunkName: "login" */ '@/views/zxzx/question'),
            meta: {
                index: 1
            }
        },
        {
            path: '/zxdetail',
            name: 'zxdetail',
            component: () => import(/* webpackChunkName: "login" */ '@/views/zxzx/detail'),
            meta: {
                index: 1
            }
        },
        {
            path: '/zxreply',
            name: 'zxreply',
            component: () => import(/* webpackChunkName: "login" */ '@/views/zxzx/reply'),
            meta: {
                index: 1
            }
        },
        {
            path: '/userinfo',
            name: 'userinfo',
            component: () => import(/* webpackChunkName: "login" */ '@/views/unserinfo/index'),
            meta: {
                index: 1
            }
        },
        {
            path: '/userinfolist',
            name: 'userinfolist',
            component: () => import(/* webpackChunkName: "login" */ '@/views/unserinfo/list'),
            meta: {
                index: 1
            }
        },
        {
            path: '/userinfojbxxlist',
            name: 'userinfojbxxlist',
            component: () => import(/* webpackChunkName: "login" */ '@/views/unserinfo/jbxxlist'),
            meta: {
                index: 1
            }
        },
        {
            path: '/userinfojbxx',
            name: 'userinfojbxx',
            component: () => import(/* webpackChunkName: "login" */ '@/views/unserinfo/jbxx'),
            meta: {
                index: 1
            }
        },
    ]
})

export default router

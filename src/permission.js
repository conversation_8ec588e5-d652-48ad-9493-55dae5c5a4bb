/**
 * 全站权限配置
 *
 */
import router from './router/index'
// import store from './store'
// import 'nprogress/nprogress.css'
// import {validatenull} from '@/util/validate'
import {getToken} from '@/util/auth'
// import NProgress from 'nprogress' // progress bar
import { toLogin} from '@/util/oauth';
import {getStore, removeStore} from '@/util/store';
// import Cookies from 'js-cookie'
// var inFifteenMinutes = new Date(new Date().getTime() + 120 * 60 * 1000);
import {GetSysParamNoLogin} from "@/service/sysParam"; // progress bar style

// NProgress.configure({ showSpinner: false });
// const lockPage = store.getters.website.lockPage; //锁屏页
router.beforeEach((to, from, next) => {
  console.log(to)
  /*if (to.query.hasOwnProperty("uid") && to.query.hasOwnProperty("token")) {
    Cookies.set('uid', to.query.uid, { expires: inFifteenMinutes })
    Cookies.set('token', to.query.token, { expires: inFifteenMinutes })
  }*/
  // getPassToken(next);
  // setStore({name: "tokenFlag", content: 1})
  // const meta = to.meta || {};
  // const isMenu = meta.menu === undefined ? to.query.menu : meta.menu;
  // store.commit('SET_IS_MENU', isMenu === undefined);

  // if(to.path === '/') {
  //   console.log(123);

  //   next({path: "/portal"})
  // }
  //   if(to.path === '/dianhua' || to.path === '/dianhua2' || to.path === '/dianhua3'){
  //       next()
  //   }
   console.log(getToken())
  if (getToken()) {
      if (to.path === '/login') { //如果登录成功访问登录页跳转到主页
          next({ path: '/' })
      }
      next()
  } else {
    //判断是否需要认证，没有登录访问去登录页
    // console.log(`meta`,meta)
    // if (meta.isAuth === false) {
      if(to.path === '/dianhua' || to.path === '/dianhua2' || to.path === '/dianhua3'){
          next()
      }else {
          if(!getToken() && getStore({name: "user-info"})) {
              console.log("删除user-info");
              removeStore({name: "user-info"})
          }
          if (!getStore({ name: "user-info" })) {
              GetSysParamNoLogin({idOrNameOrType: 'isVisitor'}).then(res => {
                  if (res.data.info) {
                      console.log(`GetSysParamNoLogin`,res.data.info)
                      let isVisitor = res.data.info.value;
                      if (isVisitor === '否') {
                          //不允许访客
                          toLogin("/mhhome");

                          // GetSysParamNoLogin({idOrNameOrType: 'indexUrl'}).then(res => {
                          //   if (res.data.info) {
                          //     let url = res.data.info.value;
                          //     if (url === null || url === '') {
                          //       toLogin("/login");
                          //     } else {
                          //       window.location.href = url;
                          //     }
                          //   } else {
                          //     toLogin("/login");
                          //   }
                          // })
                      }else {

                          // let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
                          // if (flag){
                          //   GetSysParamNoLogin({idOrNameOrType: 'mobileHome'}).then(res => {
                          //     if (res.data.info) {
                          //       let url = res.data.info.value
                          //       window.location.href = url
                          //       console.log(`mobileHome======`,res.data.info)
                          //     }
                          //   })
                          // }
                          next()
                      }
                  }else {
                      toLogin("/mhhome");
                      // GetSysParamNoLogin({idOrNameOrType: 'indexUrl'}).then(res => {
                      //   if (res.data.info) {
                      //     console.log(2222222)
                      //     console.log(res.data.info)
                      //     let url = res.data.info.value;
                      //     if (url === null || url === '') {
                      //       toLogin("/home");
                      //     } else {
                      //       window.location.href = url;
                      //       next()
                      //     }
                      //   } else {
                      //     toLogin("/home");
                      //   }
                      // })
                  }
              }).catch(()=>{
                  toLogin("/mhhome");
                  // GetSysParamNoLogin({idOrNameOrType: 'indexUrl'}).then(res => {
                  //   if (res.data.info) {
                  //     let url = res.data.info.value;
                  //     if (url === null || url === '') {
                  //       toLogin("/login");
                  //     } else {
                  //       window.location.href = url;
                  //     }
                  //   } else {
                  //     toLogin("/login");
                  //   }
                  // })
              })
          }else {
              next()
          }
      }



    // } else {
    //   // window.location.href = "http://182.92.64.176/user/login?service=" + to.path
    //   // next('/login')
    //   toLogin("/portal")
    //
    // }
  }
})

// router.afterEach(() => {
//   NProgress.done();
//   let title = store.getters.tag.label;
//   let i18n = store.getters.tag.meta.i18n;
//   title = router.$avueRouter.generateTitle(title, i18n)
//   //根据当前的标签也获取label的值动态设置浏览器标题
//   router.$avueRouter.setTitle(title);
// });
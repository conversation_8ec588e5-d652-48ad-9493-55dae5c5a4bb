<template>
    <div id="app">
        <router-view class="router-view" v-slot="{ Component }">
            <div v-if="isNavBar" class="xfcbox" :style="{width: xfcwidth+'px',right: xfcright + 'px',}" @click="xfcclick">
                <img v-if="xfcwidth == 40" style="width: 40px;height: 40px;" :src="img">
                <van-icon v-if="xfcwidth===15" class="xfcicon" size="28px" name="arrow-left"
                          color="#00a5ec"/>
            </div>
            <transition :name="transitionName">
                <component :is="Component"/>
            </transition>
            <nav-bar v-if="isNavBar"/>
        </router-view>
    </div>
</template>

<script>
import {computed, onMounted, reactive, toRefs} from 'vue'
import {useRouter} from 'vue-router'
import {mapMutations, mapState, useStore} from "vuex";
// import {GetSysParamNoLogin} from '@/service/sysParam'
import {getCookies, setCookies, setLocal} from '@/common/js/utils'
//开发登录需要添加 login1
import {diyPost, user } from '@/service/home'
import AjaxApi from "@/utils/api";
import {GetSysParamNoLogin} from "@/service/sysParam";

export default {
        setup() {
            const store = useStore()
            const router = useRouter()
            const state = reactive({
                transitionName: 'slide-left',
                isNavBar: true,
                navNum: 1,
                routertoken: null,
                navbar: [],
                xfcwidth: 15,
                img: '',
                xfcright: 0
            })

            let storeStateFns = mapState(["navBar","navNum"])

            const storeState = {}
            Object.keys(storeStateFns).forEach(fnKey => {
                const fn = storeStateFns[fnKey].bind({$store: store})
                storeState[fnKey] = computed(fn)
            })
            // 1.手动的映射和绑定
            const mutations = mapMutations(["setUser", "setToken","setNavNum","setNavBar","setNavBarList","baseUrl"])
            const newMutations = {}
            Object.keys(mutations).forEach(key => {
                newMutations[key] = mutations[key].bind({$store: store})
            })
            const {setUser, setToken,setNavBar,setNavBarList} = newMutations



            router.beforeEach( async (to, from) => {
                state.isNavBar = (to.name && storeState['navBar'].value && storeState['navBar'].value.indexOf(to.name) === -1) ? false : true;
                if (to.query.isNavBar == "true") {
                    state.isNavBar = true
                }
                if(to.fullPath == '/dianhua' || to.fullPath == '/dianhua2' || to.fullPath == '/dianhua3'){
                    state.isNavBar = false
                }
                if(to.fullPath == '/home'){
                    state.isNavBar = false
                    diyPost(AjaxApi.navBar, {showLocale: 1}).then(response => {
                            state.setNavBarList = response.data.info;
                            let navBarUrls = []
                            state.navbar.forEach(item => {
                                if (state.userInfo.humanCode !== "syt_visitor") {
                                    if (item.dataSources && item.dataSources.indexOf('/') == 0) {
                                        diyPost(item.dataSources, {}).then(res => {
                                            if (res.data.code === '00000') {
                                                item.count = res.data.info;
                                            }
                                        })
                                    }
                                }
                                navBarUrls.push(item.url)
                            })
                            setNavBar(navBarUrls)
                            setNavBarList(state.setNavBarList)
                            state.isNavBar = true

                        }
                    )
                }
                if(to.fullPath == '/mhhome'){
                    state.isNavBar = false
                    await diyPost(AjaxApi.navBar, {showLocale: 3}).then(response => {
                            state.setNavBarList = response.data.info;
                            let navBarUrls = []
                            state.navbar.forEach(item => {
                                if (state.userInfo.humanCode !== "syt_visitor") {
                                    if (item.dataSources && item.dataSources.indexOf('/') == 0) {
                                        diyPost(item.dataSources, {}).then(res => {
                                            if (res.data.code === '00000') {
                                                item.count = res.data.info;
                                            }
                                        })
                                    }
                                }
                                navBarUrls.push(item.url)
                            })
                            setNavBar(navBarUrls)
                            setNavBarList(state.setNavBarList)
                            state.isNavBar = true
                        }
                    )
                }
                if(to.fullPath == '/service'){
                    state.isNavBar = false
                    diyPost(AjaxApi.navBar, {showLocale: 5}).then(response => {
                            state.setNavBarList = response.data.info;
                            let navBarUrls = []
                            state.navbar.forEach(item => {
                                if (state.userInfo.humanCode !== "syt_visitor") {
                                    if (item.dataSources && item.dataSources.indexOf('/') == 0) {
                                        diyPost(item.dataSources, {}).then(res => {
                                            if (res.data.code === '00000') {
                                                item.count = res.data.info;
                                            }
                                        })
                                    }
                                }
                                navBarUrls.push(item.url)
                            })
                            setNavBar(navBarUrls)
                            setNavBarList(state.setNavBarList)
                            state.isNavBar = true

                        }
                    )
                }
                if (to.query.token) {
                    state.routertoken = to.query.token
                    console.log(`state.routertoken222`,state.routertoken)
                    // const token = getCookies('token')
                    // console.log(`token`,token)
                    if (state.routertoken != null) {
                        setCookies('token', state.routertoken)
                        // const token = getCookies('token')
                        setLocal('token', state.routertoken)
                        setToken({token: state.routertoken})
                        state.loading = false
                        if (state.routertoken) {
                            state.isLogin = true
                            getUser();
                            // await init();
                        }
                    }else {
                        const token = getCookies('token')
                        setLocal('token', token)
                        setToken({token: token})
                        state.loading = false
                        if (token) {
                            state.isLogin = true
                            getUser();
                            // await init();
                        }
                    }
                }
                if (to.meta.index > from.meta.index) {
                    state.transitionName = 'slide-left' // 向左滑动
                } else if (to.meta.index < from.meta.index) {
                    // 由次级到主级
                    state.transitionName = 'slide-right'
                } else {
                    state.transitionName = ''   // 同级无过渡效果
                }
            })

            onMounted(async () => {
                GetSysParamNoLogin({
                    idOrNameOrType: 'chatImg'
                }).then(res => {
                    state.img = JSON.parse(res.data.info.img)[0].url;
                })
                // await GetSysParamNoLogin({idOrNameOrType: 'isgrey'}).then(res => {
                //       if(res.data.info){
                //           let val = res.data.info.value;
                //           if(val == '是'){
                //               document.documentElement.classList.add('mourning');
                //           }else {
                //               document.documentElement.classList.remove('mourning');
                //           }
                //       }else {
                //           document.documentElement.classList.remove('mourning');
                //       }
                //   })
                //开发环境登录
                // let setData = new FormData();
                // setData.append('username', 'sytadmin');
                // setData.append('password', 'sanyth123!');
                // setData.append('uid', 'b0baee9d279d34fa1dfd71aadb908c3f');
                // const {data} = await login1(setData)
                // console.log(`data===`,data)
                // setCookies('token', data.info.token)
                if (state.routertoken != null) {
                  setCookies('token', state.routertoken)
                  // const token = getCookies('token')
                  setLocal('token', state.routertoken)
                  setToken({token: state.routertoken})
                  state.loading = false
                  if (state.routertoken) {
                    state.isLogin = true
                    getUser();
                    // await init();
                  }
                }else {
                    console.log(`getCookies('token')`,getCookies('token'))
                  const token = getCookies('token')
                  setLocal('token', token)
                  setToken({token: token})
                  state.loading = false
                  if (token) {
                    state.isLogin = true
                    getUser();
                    // await init();
                  }
                }

            })

            const getUser = async () => {
                const {data} = await user({})
                if (data.code === "00000") {
                    state.userInfo = data.info
                    setUser(data.info)
                    setLocal('userInfo', JSON.stringify(data.info))
                }
            }

            const xfcclick = () => {
                if(state.xfcwidth == 15){
                    state.xfcright = 10
                    state.xfcwidth = 40
                }else {
                    router.push('/chat')
                    state.xfcwidth = 15
                    state.xfcright = 0
                }

            }

            return {
                ...toRefs(state),
                xfcclick
            }
        },

    }
</script>

<style lang="less">
    html, body {
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
    }

    #app {
        height: 100%;
        font-family: 'Avenir', Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        // text-align: center;
        color: #2c3e50;
    }


    // 全局样式增加
    @supports (bottom:env(safe-area-inset-bottom)) {
        #app {
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom);
            font-size: 14px;
        }
    }

    .router-view {
        width: 100%;
        height: auto;
        position: absolute;
        top: 0;
        bottom: 50px;
        margin: 0 auto;
        -webkit-overflow-scrolling: touch;
    }

    .slide-right-enter-active,
    .slide-right-leave-active,
    .slide-left-enter-active,
    .slide-left-leave-active {
        height: 100%;
        will-change: transform;
        transition: all 500ms;
        position: absolute;
        backface-visibility: hidden;
    }

    .slide-right-enter {
        opacity: 0;
        transform: translate3d(-100%, 0, 0);
    }

    .slide-right-leave-active {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
    }

    .slide-left-enter {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
    }

    .slide-left-leave-active {
        opacity: 0;
        transform: translate3d(-100%, 0, 0);
    }

    .van-badge--fixed {
        z-index: 1000;
    }

    .list-item-name {
        font-weight: bold;
    }

    .list-item-info {
        color: #666;
        font-size: 13px;
        line-height: 20px;
    }

    .item_right_top {
        float: right;
        font-size: 13px;
        color: #999;
    }

    .item_right_btn {
        position: absolute !important;
        right: 0;
        bottom: 0;
        font-size: 14px;
        padding: 0;
    }

    .van-submit-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 100;
        width: 100%;
        background-color: #fff;
        -webkit-user-select: none;
        user-select: none;
    }

    .van-button--default {
        border: 0px !important;
    }

    .van-button--default {
        border: var(--van-button-border-width) solid var(--van-button-default-border-color) !important;
    }

    .van-submit-bar-search {
        bottom: unset;
        left: unset;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar-search .van-button {
        margin: 5px;
        width: 88% !important;
        font-size: 16px;
    }
    /* 可以根据需要添加其他元素的样式，使其在灰色显示期间得到限制或变化 */
    .mourning {
      filter: grayscale(100%) !important; /* 将内容元素转为灰度图像 */
    }
    .floating-window {
        position: fixed;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        width: 200px;
        background-color: #f4f4f4;
        padding: 20px;
        border: 1px solid #ddd;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        transition: transform 0.3s ease;
        z-index: 99999;
    }

    .floating-window.hidden {
        transform: translateY(-50%) translateX(100%); /* 向右缩回 */
    }
    .xfcbox{
        //width: 50px;
        text-align: center;
        height: 40px;
        background-color: #fff;
        //border: 1px solid #00a5ec;
        border-radius: 20px;
        overflow: hidden;
        position: fixed;
        bottom: 90px;
        //right: 0;
        z-index: 9999;
    }
    .xfcicon{
        //color: rgb(0, 165, 236);
        //font-size: 32px;
        line-height: 40px !important;
        /* width: 20px; */
        /* margin-right: 9px; */
        position: fixed !important;
        bottom: 90px;
        right: -5px;
        z-index: 9999;
    }
</style>

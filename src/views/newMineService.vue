<template>
    <div>

        <div class="product-list-content">
            <header class="category-header wrap">
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
                <div class="header-right" @click="showSearchPopup=true">
                    <van-icon class="right-icon" :name="defaultSearchIcon" size="18"/>
                    <div class="right-div">
                        筛选
                    </div>
                </div>
            </header>
        </div>
        <div class="content">
            <div class="serviceWrap">
                <div v-for="service in SytServiceCenterListData" class="serviceContent"
                     :key="service.id">
                    <div class="service">
<!--                        <div @click="toService(service)" class="serviceName">{{service.name}}<br/></div>-->
                        <img @click="toService(service)" :src="baseUrl+'/file/view/'+service.icon" @error="handleImageError">
                        <div>
                            <p @click="toService(service)" class="serviceDiv" style="color: #000;margin-top: 7px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{{service.name}}</p>
                            <p @click="toService(service)" class="serviceDiv" style="color: #999;font-size: 12px;">访问数 &nbsp;&nbsp;{{service.visits}}</p>
<!--                            <span @click="toService(service)" class="serviceDiv">收藏数 &nbsp;&nbsp;{{service.collectNumber}}</span>-->
<!--                            <span @click="handleServiceCenterCollect(service)">-->
<!--                                <van-icon v-if="service.collect" name="star" color="#f11833"/>-->
<!--                                <van-icon v-else name="star-o"/>-->
<!--                            </span>-->
                        </div>
                    </div>
                </div>
                <!--                <van-empty v-if="SytServiceCenterListData.length===0" description="没有搜索到更多信息"/>-->
            </div>
        </div>
        <!--        <nav-bar/>-->
        <van-popup v-model:show="showSearchPopup" position="top" :style="{height:'auto',width:'100%'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <van-field label="服务类型" readonly/>
                    <van-field label="" readonly>
                        <template #input>
                            <div style="width: 100%;height: 100%;">
                                <van-button style="margin: 3px;width: 23%;float: left;" size="small"
                                            v-for="(service,index) in serviceList" :key="index"
                                            hairline
                                            :plain="service.value===linkType"
                                            :type="service.value===linkType?'primary':'default'"
                                            @click="handleClik1(service)">{{service.text}}
                                </van-button>
                            </div>
                        </template>
                    </van-field>
                    <van-field label="服务场景" readonly/>
                    <van-field label="" readonly>
                        <template #input>
                            <div style="width: 100%;height: 100%;">
                                <van-button style="margin: 3px;width: 23%;float: left;" size="small"
                                            v-for="(category,index) in categoryList" :key="index"
                                            hairline
                                            :plain="category.value===categoryId"
                                            :type="category.value===categoryId?'primary':'default'"
                                            @click="handleClik2(category)">{{category.text}}
                                </van-button>
                            </div>
                        </template>
                    </van-field>
                    <van-field label="所属部门" readonly/>
                    <van-field label="" readonly>
                        <template #input>
                            <div style="width: 100%;height: 100%;">
                                <van-button style="margin: 3px;width: 23%;float: left;" size="small"
                                            v-for="(org,index) in orgList" :key="index"
                                            hairline
                                            :plain="org.value===orgid"
                                            :type="org.value===orgid?'primary':'default'"
                                            @click="handleClik3(org)">{{org.text}}
                                </van-button>
                            </div>
                        </template>
                    </van-field>
                </van-cell-group>
            </div>
            <div class="van-submit-bar-search">
                <van-button type="primary" round size="small" @click="handleSearch"> 搜&nbsp;&nbsp;索
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
import AjaxApi from "@/utils/api";
import {GetService, diyPost, hitCount} from '@/service/home'
import {GetRoleInfo} from "@/service/settings";
import {mapState} from "vuex";
// import navBar from '@/components/NavBar'
import {useRoute, useRouter} from 'vue-router'
import {nextTick, onMounted, reactive, toRefs, watch} from "vue";

import {showFailToast, showSuccessToast} from 'vant';
import {getActiveDefinitionId} from "@/service/bpmProcessDefinition";

export default {

    setup() {
        const router = useRouter()
        const route = useRoute()
        const state = reactive({
            SytServiceCenterListData: [],
            searchName: null,
            showSearchPopup: false,
            defaultIcon: require("../assets/isnull.png"),
            serviceList: [
                {text: "全部", value: "all"},
                {text: "流程服务", value: "0"},
                {text: "应用服务", value: "1"},
                {text: "预约资源", value: "2"},
                {text: "填报任务", value: "3"},
            ],
            categoryList: [],
            orgList: [],
            linkType: null,//服务类型
            categoryId: null,//服务场景
            orgid: null,//所属部门
            roleList: null,//所属部门

        })
        onMounted(async () => {
            // let param = {
            //     orgid: props.orgid
            // }
            if(route.query.orgid){
                state.orgid = route.query.orgid;
            }
            GetSytServiceCenterList();
            GetSytDataCategoryList();
            queryOrgDataInSC();
            getRoleInfo();
        })
        watch(() => state.searchName, () => {
            // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
            GetSytServiceCenterList()
        })

        const goBack = () => {
            router.go(-1)
        }
        const getRoleInfo = () => {
            GetRoleInfo().then((res) => {
                state.roleList = res.data.info;
            });
        }

        const handleImageError = (event) => {
            event.target.src = state.defaultIcon;
        }

        // 访问次数
        const setHitCount = (hitData) => {
            hitCount(hitData).then(res => {
                console.log(res)
            })
        }

        const toService = (service) => {
            nextTick(() => {
                let role = []
                if (service.roleId) {
                    service.roleId.forEach(item => {
                        state.roleList.forEach(val => {
                            if (item == val.id) {
                                role.push(val.rolename)
                            }
                        })
                    })
                }
                console.log(`toService`,service,state.roleList)
                if(service.popWindow == '否'){
                    let hitData = {
                        id: service.id
                    };
                    if (service.linkType == '0') {
                        getActiveDefinitionId({processDefinitionKey: service.processDefinitionId}).then(res => {
                            if (res.data.code == "00000") {
                                setHitCount(hitData)
                                router.push({
                                    path: "/mine-dealt-create",
                                    query: {
                                        id: res.data.info, name: service.name,
                                        activeTab: service.linkType,
                                        backUrl: '/new-mine-service',
                                        // backId: state.currentId,
                                        // backName: state.name,
                                    }
                                })
                            } else {
                                showFailToast(res.data.info);
                            }
                        })
                    } else if (service.linkType == '1') {
                        // 访问次数
                        setHitCount(hitData)
                        let routeData = service.link
                        // window.open(routeData, '_blank')
                        window.location.href = routeData

                    } else if (service.linkType == '2') {
                        // 访问次数
                        setHitCount(hitData)
                        router.push({path: "/mine-service-recourse", query: {id: service.resourceId, name: service.name}})
                    } else if (service.linkType == '3') {
                        // 访问次数
                        setHitCount(hitData)
                        router.push({path: "/mine-service-taskFill", query: {taskId: service.taskId, name: service.name}})
                    }
                }else {
                    let linkTypeName = service.linkType ? state.serviceList.find(item => {
                        return item.value === service.linkType;
                    })['text'] : '';
                    service.linkTypeName = linkTypeName
                    let categoryName = service.categoryId ? state.categoryList.find(item => {
                        return item.value === service.categoryId;
                    })['text'] : '';
                    service.categoryName = categoryName
                    service.rolename = role.join()
                    router.push({
                        path: 'mine-service-details',
                        query: {
                            isNavBar: false,
                            service: JSON.stringify(service),
                        }
                    });
                }

            })
        }

        const GetSytDataCategoryList = () => {
            let json = {
                model: "wodefuwu"
            }
            diyPost(AjaxApi.GetSytDataCategoryList, json).then(res => {
                if (res.status === 200 && res.data.code === "00000") {
                    let rData = res.data.info;
                    let newArray = [{
                        text: '全部',
                        value: 'all',
                    }]
                    if (rData.length > 0) {
                        rData.forEach(e => {
                            newArray.push({
                                text: e.name,
                                value: e.id,
                            })
                        })
                        state.categoryList = newArray;
                    }
                }
            })
        }
        const queryOrgDataInSC = () => {
            let json = {}
            diyPost(AjaxApi.queryOrgDataInSC, json).then(res => {
                if (res.status === 200 && res.data.code === "00000") {
                    let rData = res.data.info;
                    let newArray = [{
                        text: '全部',
                        value: 'all',
                    }]
                    if (rData.length > 0) {
                        rData.forEach(e => {
                            newArray.push({
                                text: e.orgname,
                                value: e.id,
                            })
                        })
                        state.orgList = newArray;
                    }
                }
            })
        }
        const handleServiceCenterCollect = (service) => {
            let json = {
                id: service.id,
            }
            diyPost(AjaxApi.sytServiceCenterCollectOperation, json).then(res => {
                if (res.status === 200 && res.data.code === "00000") {
                    showSuccessToast(res.data.info);
                }
            }).catch(error => {
                console.log(error);
            }).finally(() => {
                GetSytServiceCenterList();
            })
        }
        const GetSytServiceCenterList = () => {
            let json = {
                tab: "all",
                showType: "mobile"
            }
            if (state.searchName) json.name = state.searchName
            if (state.linkType) json.linkType = state.linkType === 'all' ? '' : state.linkType
            if (state.categoryId) json.categoryId = state.categoryId === 'all' ? '' : state.categoryId
            if (state.orgid) json.orgid = state.orgid === 'all' ? '' : state.orgid
            console.log(`state.orgid`,state.orgid)
            GetService(json).then(res => {
                if (res.status === 200 && res.data.code === "00000") {
                    state.SytServiceCenterListData = res.data.info
                }
            })
        }

        const handleClik1 = (model) => {
            state.linkType = model.value
        }
        const handleClik2 = (model) => {
            state.categoryId = model.value
        }
        const handleClik3 = (model) => {
            state.orgid = model.value
        }
        const handleSearch = () => {
            GetSytServiceCenterList()
            state.showSearchPopup = false;
        }
        return {
            ...toRefs(state),
            goBack,
            toService,
            handleSearch,
            handleClik1,
            handleClik2,
            handleClik3,
            handleServiceCenterCollect,
            handleImageError
        }
    },
    components: {
        // navBar,
    },
    data() {
        return {};
    },
    computed: {
        ...mapState(["baseUrl", "defaultSearchIcon"]),
    },
};
</script>

<style lang="less" scoped>
@import '../common/style/mixin';

.product-list-content {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: #fff;

    .category-header {
        .fj();
        width: 100%;
        height: 50px;
        line-height: 50px;
        padding: 0 15px;
        .boxSizing();
        font-size: 15px;
        color: #656771;
        z-index: 10000;

        &.active {
            background: @primary;
        }

        .icon-left {
            font-size: 25px;
            font-weight: bold;
        }

        .header-search {
            display: flex;
            width: 88%;
            height: 20px;
            line-height: 20px;
            margin: 10px 0;
            padding: 5px 0;
            color: #232326;
            background: #F7F7F7;
            .borderRadius(20px);

            .nbSearch {
                padding: 0 5px 0 20px;
                font-size: 17px;
            }

            .search-title {
                font-size: 14px;
                color: #666;
                background: #F7F7F7;
            }
        }

        .icon-More {
            font-size: 20px;
        }

        .header-right {
            display: flex;
            width: 16%;
            margin-left: 20px;

            .right-icon {
                margin-top: 15px;
            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
    }
}

.content {
    height: calc(~"(100vh - 97px)");
    overflow: hidden;
    overflow-y: scroll;
    margin-top: 42px;
}

.serviceWrap {
    padding: 7px 3px;
    background: #fff;
    margin-top: 8px;
    overflow-y: auto;
}

.serviceContent {
    width: 46.5%;
    //height: 87px;
    padding: 5.5px;
    float: left;
}

.service {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 5px;
}

.serviceContent:nth-of-type(6n+1) .service {
    background: #f8f9fe;
}

.serviceContent:nth-of-type(6n+2) .service {
    background: #effffa;
}

.serviceContent:nth-of-type(6n+3) .service {
    background: #fffaf7;
}

.serviceContent:nth-of-type(6n+4) .service {
    background: #fff6f7;
}

.serviceContent:nth-of-type(6n+5) .service {
    background: #f9f8ff;
}

.serviceContent:nth-of-type(6n+6) .service {
    background: #f3ffff;
}

.serviceName {
    font-size: 0.37333rem;
    line-height: 0.64rem;
    font-weight: bold;
    margin: 3px 3px 3px 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.service img {
    float: left;
    width: 40px;
    height: 40px;
    margin-top: 5px;
    margin-left: 10px;
    margin-right: 8px;
}

.service .van-icon {
    float: right;
    margin-right: 8px;
}

.service p {
    margin-top: 5px;
    margin-bottom: 5px;
    color: #666;
    font-size: 0.34667rem;
    line-height: 0.4rem;
}

.serviceDiv {
    font-size: 14px;
    color: #999;
    margin-top: 5px;
}
::v-deep .van-button__text{
    //overflow: hidden;
    //text-overflow: ellipsis;
    //display: -webkit-box;
    //text-align: center;
    //-webkit-line-clamp: 2;
    //-webkit-box-orient: vertical;
    //white-space: normal;
    //line-height: 32px;
    //overflow: hidden;
    //white-space: nowrap; /* 不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* 不换行 */
    flex: 1;
    //text-overflow: ellipsis;
}
</style>

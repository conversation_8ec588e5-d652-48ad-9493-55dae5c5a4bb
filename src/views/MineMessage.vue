<template>
    <section>
        <div class="product-list-content">
            <header class="category-header wrap">
                <i class="nbicon nbfanhui" @click="goBack"></i>
                <span style="width: 90px;padding: 0 6px;overflow: hidden;font-weight: bold;">{{currentType}}</span>
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
            </header>
            <van-tabs v-model:active="active" color="#48a3ea" title-active-color="#48a3ea"
                      @click-tab="changeTab">
                <van-tab v-for="tab in serviceStatus" :key="tab.value" :title="tab.text">
                </van-tab>
            </van-tabs>
        </div>

        <div class="content">
            <list :url="url" :queryObj="queryObj">
                <template v-slot:default="slotProps">
                    <div v-for="(item,index) in slotProps.list" :key="index" @click="handleClick(item)">
                        <div style="text-align: center;margin: 10px;color: #666;
                         font-size: 14px;
                         line-height: 20px;"><span>{{dateFormat_date(item.sendtime)}}</span></div>
                        <van-card>
                            <template #title>
                                <span class="van-card__title">
<!--                                    [{{item.type}}]-->
                                    {{item.title}}</span>
                                <span style="float: right;" v-if="item.status!==1">
                                    <van-icon name="ellipsis" color="red"/>
                                </span>
                            </template>
                            <template #desc>
                                <div class="list-item-info" v-if="item.taskName">{{item.taskName}}</div>
                                <div class="list-item-info" v-html="item.content"></div>
                            </template>
                        </van-card>
                    </div>
                </template>
            </list>
        </div>

        <van-popup v-model:show="detailShow" position="center"
                   :style="{height: '80%',width:'96%',overflow:'hidden'}">
            <div>
                <h2 class="popupH2">{{ curDetail.title }}</h2>
                <p class="popupP">
                    发布时间：{{ dateFormat_date(curDetail.sendtime) }}</p>
                <div v-html="curDetail.content" class="popupDiv"></div>
            </div>
        </van-popup>
        <div v-if="popupClose" @click="detailShowFn()">
            <div class="shuxian"></div>
            <van-icon class="clickClose" name="close"/>
        </div>
    </section>
</template>

<script>
    import {diyPost} from '@/service/home'
    import List from "@/components/List";
    import AjaxApi from "@/utils/api";
    import Utils from "@/utils/momentWrap";
    import {mapState} from "vuex";
    import {useRoute, useRouter} from "vue-router";
    import {computed, onMounted, reactive, toRefs, watch} from "vue";
    import {queryOne} from "@/service/bpmProcessDefinition";
    import {showFailToast} from "vant";

    export default {
        components: {
            List,
        },
        setup() {
            const router = useRouter()
            const route = useRoute()
            const state = reactive({
                detailShow: false,
                popupClose: false,
                curDetail: null,
                queryObj: {},
                active: 0,
                serviceStatus: [
                    // {text: "全部", value: ""},
                    {text: "未读", value: "0"},
                    {text: "已读", value: "1"},
                    // {text: "未读", value: "0"},
                    {text: "全部", value: ""},
                ],

                url: AjaxApi.GetSytMessageSend,
                searchName: null,
                currentType: null,
            })
            onMounted(async () => {
                const {currentType, currentTab} = route.query
                state.currentType = currentType
                state.active = currentTab ? Number(currentTab) : 0
                state.queryObj = newQueryObj
            })

            watch(() => state.searchName, (newVal) => {
                if (newVal) state.queryObj = newQueryObj
            })
            const detailShowFn = () => {
                state.detailShow = false;
                state.popupClose = false;
            }
            const handleClick = (item) => {
                console.log(`handleClick`,item)
                diyPost(AjaxApi.MessageIsRead, {ids: item.id}).then(res => {
                        if (res.data.code === "00000") {
                            if (item.type === '抄送' || item.type === '催办') {
                                router.push({
                                    path: '/mine-dealt-detail',
                                    query: {
                                        currentId: item.processInstanceId,
                                        processDefinitionId: item.processDefinitionId,
                                        activeType: item.type,
                                        activeTab: state.active,
                                        backUrl: 'mine-message'
                                    }
                                });
                            } else if (item.type === '任务填报') {
                                queryOne({projectId: item.taskId}).then(response => {
                                    if (response.data.code == "00000") {
                                        if (response.data.info.project) {
                                            router.push({
                                                path: "/mine-service-task-edit",
                                                query: {
                                                    project: JSON.stringify(response.data.info.project),
                                                    name: item.title,
                                                    activeType: item.type,
                                                    activeTab: state.active,
                                                    backUrl: 'mine-message'
                                                }
                                            })
                                        }
                                    } else {
                                        showFailToast(response.data.info);
                                    }
                                })
                            } else if (item.type === '提醒' || item.type === '系统公告') {
                                state.curDetail = item;
                                state.detailShow = true;
                                state.popupClose = true;
                            }
                        }
                    }
                );
            }
            const goBack = () => {
                router.push({path: 'mine-message-type'})
            }

            const dateFormat_date = (o) => {
                return Utils.dateFormat_date(o);
            }
            const newQueryObj = computed(() => {
                return {
                    queryParam: {
                        type: state.currentType,
                        status: state.serviceStatus[state.active].value,
                        title: state.searchName,
                    }
                }
            })
            const changeTab = (tab) => {
                state.searchName = null;
                state.active = Number(tab.name)
                state.queryObj = newQueryObj
            }
            return {
                ...toRefs(state),
                goBack,
                handleClick,
                dateFormat_date,
                detailShowFn,
                changeTab,
            }
        },
        computed: {
            ...mapState(["baseUrl", "defaultSearchIcon"]),
        }
    }
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';

    .popupH2 {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        text-align: center;
    }

    .popupP {
        font-size: 14px;
        color: #999;
        margin-bottom: 20px;
        border-bottom: 1px solid #eeeeee;
        padding-bottom: 20px;
        text-align: center;
    }

    .popupDiv {
        padding: 0px 20px;
        height: 63vh;
        overflow-y: scroll;
    }

    .clickClose {
        font-size: 40px;
        position: fixed;
        left: calc(50% - 20px);
        top: 92%;
        color: #fff;
        z-index: 3000;
    }

    .shuxian {
        border-left: 2px solid #fff;
        height: 2.2%;
        width: 0px;
        position: fixed;
        left: calc(50% - 1px);
        top: 90%;
        color: #fff;
        z-index: 3000;
    }

    .product-list-content {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }

    .content {
        /*height: 100%;*/
        /*overflow: hidden;*/
        /*overflow-y: scroll;*/
        /*margin-top: 96px;*/
        background: #f7f8fa;
        height: calc(~"(100vh - 40px)");
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 96px;

    }

    .van-card {
        margin: 8px;
        background-color: #fff;
        margin-top: 0;
        font-size: var(--van-cell-font-size);
        height: 2rem;
    }

</style>

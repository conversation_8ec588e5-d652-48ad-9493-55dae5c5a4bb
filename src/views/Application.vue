<template>
    <section>
        <div class="product-list-content">
            <header class="category-header wrap">
                <i class="nbicon nbfanhui" @click="goBack"></i>
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
            </header>
        </div>
        <div class="content">
            <list :url="url" :queryObj="queryObj">
                <template v-slot:default="slotProps">
                    <div class="serviceContent">
                        <div class="service" v-for="(item,index) in slotProps.list" :key="index"
                             @click="showDetail(item)">
                            <img :src="item.imgUrl.url?(baseUrl+item.imgUrl.url):defaultIcon" alt="">
                            <p>{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</p>

                        </div>
                    </div>
                </template>
            </list>
        </div>
    </section>
</template>

<script>
    import {mapState} from "vuex";
    import List from "@/components/List";
    import AjaxApi from "@/utils/api";
    import {onMounted, reactive, toRefs} from "vue";
    import {useRouter} from "vue-router";

    export default {
        components: {
            List
        },
        setup() {
            const router = useRouter()
            const state = reactive({
                defaultIcon: require("../assets/isnull.png"),
                searchName: null,
                url: AjaxApi.appletList,
                queryObj: {},
            })
            onMounted(async () => {
                state.queryObj = {
                    queryParam: {
                        type: "应用平台"
                    }
                }
            })
            const showDetail = (app) => {
                if (app.redirectUrl) window.open(app.redirectUrl, '_blank')
            }

            const goBack = () => {
                router.go(-1)
            }

            return {
                ...toRefs(state),
                goBack,
                showDetail
            }
        },
        computed: {
            ...mapState(["baseUrl"]),
        },
    };
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';

    .product-list-content {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }


    .content {
        width: 100%;
        margin-top: 50px;
        background: #fff;
        position: relative;
    }

    .mineDataTitle {
        height: 45px;
        line-height: 45px;
        color: #333;
        padding-left: 4px;
        font-size: 15px;
        font-weight: bold;
        font-family: "微软雅黑";
        margin: 0px;
    }

    .serviceContent {
        width: 100%;
        height: 100px;
    }

    .service {
        float: left;
        width: 25%;
        height: 100%;
        text-align: center;
    }

    .service img {
        width: 36px;
        margin-top: 13px;
    }

    .service p {
        font-size: 14px;
        color: #333;
    }
</style>

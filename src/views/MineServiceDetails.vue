<template>
    <div>
        <s-header :name="'服务详情'" ></s-header>
        <div class="top" v-if="service">
            <div class="banner">
<!--                <span class="B_tiile">{{service.name}}</span>-->
                <p class="B_tiile">{{service.name}}</p>
                <p style="margin: 4px 0 0 0">
                    <span class="B_icon1"><van-icon name="eye-o" style="margin: 3px;"/>{{service.visits}}</span>
                    <span class="B_icon2"><van-icon name="star-o" style="margin: 3px;"/>{{service.collectNumber}}</span>
                </p>

                <span class="B_icon3" @click="handleServiceCenterCollect(service)">
                    <van-icon color="#FEC799" style="top: 5px;" name="bookmark" size="22"/>
                    <span style="color: #fff;">收藏</span>
                </span>
            </div>
        </div>
        <van-cell-group v-if="service">
            <van-cell title="服务类型" :value="service.linkTypeName"/>
            <van-cell title="服务场景" :value="service.categoryName"/>
            <van-cell title="是否推荐" :value="service.recommend"/>
            <van-cell title="授权角色" :value="service.rolename"/>
        </van-cell-group>
        <van-tabs v-model:active="activeName" color="#00bcd4">
            <van-tab title="服务说明" name="a">
                <div class="firstcontent">
                    {{service.remark}}
                </div>
            </van-tab>
            <van-tab title="意见反馈" name="b">
                <div class="firstcontent">
                    <van-field v-model="content"
                               style="border:1px solid #e8e8e8"
                               rows="6"
                               autosize
                               label=""
                               type="textarea"
                               maxlength="500"
                               placeholder="请输入意见反馈"
                               show-word-limit></van-field>
                    <van-button plain hairline type="primary" size="mini"
                                style="float: right;margin: 10px 5px;" @click="handleSubmit">提交
                    </van-button>
                </div>
            </van-tab>
        </van-tabs>
        <div class="van-submit-bar">
            <van-button type="primary" class="van-button" round size="small" @click="SCDetailProcess"> 开始办理
            </van-button>
        </div>
    </div>
</template>

<script>
    import sHeader from '@/components/SimpleHeader'
    import {useRoute, useRouter} from "vue-router";
    import {onMounted, reactive, toRefs} from "vue";
    import AjaxApi from "@/utils/api";
    import {diyPost, Tofeedback, hitCount} from '@/service/home'
    import {getActiveDefinitionId} from "@/service/bpmProcessDefinition";
    import {showSuccessToast, showFailToast} from 'vant';

    export default {
        components: {
            sHeader
        },
        setup() {
            const router = useRouter()
            const route = useRoute()
            const state = reactive({
                activeName: 'a',
                service: null,
                type: null,
                defaultIcon: require("../assets/isnull.png"),
                serviceData: null,
                content: null,
            })
            onMounted(async () => {
                const {service} = route.query
                state.service = JSON.parse(service)

                console.log(JSON.parse(service))
            })
            const handleServiceCenterCollect = () => {
                let json = {
                    id: state.service.id,
                }
                diyPost(AjaxApi.sytServiceCenterCollectOperation, json).then(res => {
                    if (res.status === 200 && res.data.code === "00000") {
                        showSuccessToast(res.data.info);
                        if (res.data.info === '已取消收藏') state.service.collectNumber -= 1
                        if (res.data.info === '收藏成功') state.service.collectNumber += 1

                    }
                }).catch(error => {
                    console.log(error);
                })
            }
            const handleSubmit = () => {
                if (!state.content) {
                    showFailToast("请输入反馈内容.");
                    return;
                }
                const json = {id: state.service.id, content: state.content};
                Tofeedback(json).then(res => {
                    if (res.data.code == "00000") {
                        showSuccessToast("反馈成功！");
                        state.content = null;
                    } else {
                        showFailToast("提交失败");
                    }
                });
            }

            // 访问次数
            const setHitCount = (hitData) => {
                hitCount(hitData).then(res => {
                    console.log(res)
                })
            }
            const SCDetailProcess = () => {
                let row = state.service
                // 服务访问参数
                let hitData = {
                    id: row.id
                };
                if (row.linkType == '0') {
                    getActiveDefinitionId({processDefinitionKey: row.processDefinitionId}).then(res => {
                        if (res.data.code == "00000") {
                            setHitCount(hitData)
                            router.push({
                                path: "/mine-dealt-create",
                                query: {
                                    id: res.data.info, name: row.name,
                                    activeTab: row.linkType,
                                    backUrl: '/new-mine-service',
                                    // backId: state.currentId,
                                    // backName: state.name,
                                }
                            })
                        } else {
                            showFailToast(res.data.info);
                        }
                    })
                } else if (row.linkType == '1') {
                    // 访问次数
                    setHitCount(hitData)
                    let routeData = row.link
                    // window.open(routeData, '_blank')
                    window.location.href = routeData

                } else if (row.linkType == '2') {
                    // 访问次数
                    setHitCount(hitData)
                    router.push({path: "/mine-service-recourse", query: {id: row.resourceId, name: row.name}})
                } else if (row.linkType == '3') {
                    // 访问次数
                    setHitCount(hitData)
                    router.push({path: "/mine-service-taskFill", query: {taskId: row.taskId, name: row.name}})
                }
            }
            return {
                ...toRefs(state),
                handleSubmit,
                SCDetailProcess,
                handleServiceCenterCollect
            }
        },
    };
</script>

<style lang="less">

    van-icon {
        margin-top: 2px;
    }

    .top {
        width: 100%;
        //height: 120px;
        background-image: url("../assets/service.png");
    }

    .banner {
        width: 100%;
        height: 100%;
        position: relative;
        padding-left: 20px;
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .B_tiile {
        //position: absolute;
        margin: 0;
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        width: 70%;
        //top: 30px;
        //left: 30px;
    }

    .B_icon1 {
        //position: absolute;
        color: #fff;
        //top: 70px;
        //left: 30px;
    }

    .B_icon2 {
        //position: absolute;
        color: #fff;
        //top: 70px;
        //left: 80px;
    }

    .B_icon3 {
        display: block;
        text-align: center;
        width: 90px;
        line-height: 30px;
        position: absolute;
        top: 30px;
        right: 20px;
        z-index: 9999;
    }

    ::v-deep .van-tab__pane {
        margin-top: 10px;
        background-color: #fff;
        /*height: 800px;*/
        width: 100%;
        padding: 10px 20px;
    }

    .firstcontent {
        color: #9e9e9e;
        margin: 15px;
        font-size: 14px;
        line-height: 25px;
    }

    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 88% !important;
        /*font-size: 16px;*/
    }
</style>

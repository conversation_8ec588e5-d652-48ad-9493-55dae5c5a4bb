<!-- src/views/Login.vue -->
<template>
    <div class="login-container">
<!--        <van-nav-bar title="修改手机号" />-->
        <div style="background-color: #00a5ec;height: 120px;" class="titleback">
            <p class="weui-form__tips" style="line-height: 120px;margin: 0 0 0 50px;color: white;font-size: 24px;font-weight: bold;">
                <!--                请输入姓名身份证号验证身份-->
                核对手机号
            </p>
        </div>
        <p class="weui-form__tips" style="margin: 20px;color: #9e9e9e;font-size: 14px;">
            1、如果系统预留的手机号与当前微信绑定的手机号一致，就直接点下一步；如果不一致，请务必修改并保存。<br><span style="color: red;">2、请确保是当前微信绑定的手机号。</span>
        </p>
        <div class="form-container">
<!--            <van-field-->
<!--                v-model="xm"-->
<!--                placeholder="姓名"-->
<!--                label="姓名"-->
<!--                readonly-->
<!--                required-->
<!--            />-->
            <van-field
                v-model="sjh"
                placeholder="你预留的手机号"
                label="你预留的手机号"
                label-width="150"
                readonly
            />
            <van-field
                v-model="xsjh"
                placeholder="新手机号"
                label="新手机号"
                label-width="150"
            />
<!--            <div class="captcha-container">-->
<!--                <van-field-->
<!--                    v-model="captchaInput"-->
<!--                    placeholder="请输入验证码"-->
<!--                    label="验证码"-->
<!--                    required-->
<!--                />-->
<!--                <img v-if="imgshow" :src="baseUrl+'platform/qywx/user/captcha'" alt="验证码" class="captcha-image" @click="refreshCaptcha" />-->
<!--            </div>-->
            <div style="text-align: center;margin-top: 20px;">
                <van-button style="width: 100px;margin-right: 20px;" type="primary" @click="handleSubmit('0')">下一步</van-button>
                <van-button style="width: 150px;" type="primary" :disabled="!xsjh" @click="handleSubmit('1')">保存手机号</van-button>
            </div>

            <van-toast v-model="errorMessage" type="fail" />
        </div>
    </div>
</template>

<script>
import {diyPost} from '@/service/home'
import AjaxApi from "@/service/dianhua";
import {mapState} from "vuex";
import {showToast} from "vant";

export default {
    data() {
        return {
            username: '',
            password: '',
            captchaInput: '',
            captchaUrl: '',
            errorMessage: '',
            xm: '',
            sfzh: '',
            sjh: '',
            xsjh: null,
            imgshow: true
        };
    },
    created() {
        this.refreshCaptcha()
    },
    computed: {
        ...mapState(["baseUrl"])
    },
    methods: {
        refreshCaptcha(){
            let json = {

            }
            diyPost(AjaxApi.getPhone,json).then(res => {
                console.log(res)
                if( res.data ){
                    this.sjh = res.data.data
                }
            })
        },
        handleSubmit(type) {
            if(type == '1'){
                diyPost(AjaxApi.phoneUpdate+`?newPhone=${this.xsjh}`).then(res => {
                    if( res.data.status ){
                        showToast({type: 'success', message:  '保存成功，请点击下一步。'});
                        this.$router.go(0)
                    }else {
                        showToast({type: 'warning', message:  res.data.msg});
                    }

                })
            }else {
                this.$router.push({path:'/dianhua3',})
            }

        },
    },
};
</script>

<style scoped>
.titleback{
    background-image: url("../assets/u555.png");
    background-repeat: no-repeat;
    background-size: 50% 100%;
    background-position: right;
}
.login-container {
    /*padding: 20px;*/
}
.form-container {
    margin: 20px;
}
.captcha-container {
    display: flex;
    align-items: center;
}
.captcha-image {
    margin-left: 10px;
    cursor: pointer;
    width: 100px;
    height: 40px;
    border: 1px solid #ccc;
}
</style>

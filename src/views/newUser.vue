<template>
    <div class="user-box">
        <van-skeleton title :avatar="true" :row="5" :loading="loading">
            <div class="user-info">
                <div class="info">
                    <img :src="avatarUrl?avatarUrl:''" @error="errorfun" />
                    <div class="user-desc">
                        <div style="font-weight: bold;font-size: 20px;margin-top: 10px;">{{ userInfo.humanName }}</div>
                        <div class="name">{{ userInfo.humanCode }}&nbsp;&nbsp;|&nbsp;&nbsp;{{userInfo.organizationnames }}</div>
                        <div class="vtag posrt" style="background-color: #E6C253!important;border-radius: 5px;padding-left: 5px;padding-right: 5px;">
                            <van-popover v-model:show="showPopover" :actions="actions" @select="onSelect">
                                <template #reference>
<!--                                    <van-tag style="background-color: #E6C253!important;" size="large">{{ currentRoleName}}&nbsp;-->
<!--                                        <van-icon name="sort" size="15"/>-->
<!--                                    </van-tag>-->
                                    {{ currentRoleName}}&nbsp;
                                    <van-icon name="sort" size="15"/>
                                </template>
                            </van-popover>
                        </div>
<!--                        <div @click="show = true" class="password" style="background-color: #E6C253!important;border-radius: 5px;padding-left: 5px;padding-right: 5px;">修改密码</div>-->
                    </div>
                </div>
            </div>
        </van-skeleton>
        <ul class="user-list">
            <li v-for="(nav,index) in navbar" :key="index" class="van-hairline--bottom" @click="goTo(nav.url)">
                <span style="font-weight: bold;"><van-icon :name="nav.iconClass" style="font-size: 16px;"/>&nbsp;&nbsp;{{nav.name}}</span>
                <span>
                    <span style="color: #999;margin-right: 5px;">{{ nav.remark }}</span>
                    <van-icon name="arrow"/>
                </span>

            </li>
            <!--            <li class="van-hairline&#45;&#45;bottom" @click="goTo('/setting')">-->
            <!--                <span>事务中心</span>-->
            <!--                <van-icon name="arrow"/>-->
            <!--            </li>-->
            <!--            <li class="van-hairline&#45;&#45;bottom" @click="goTo('/address', { from: 'mine' })">-->
            <!--                <span>应用中心</span>-->
            <!--                <van-icon name="arrow"/>-->
            <!--            </li>-->
        </ul>
        <van-popup v-model:show="show" position="bottom" teleport="body" >
            <van-field
                v-model="text"
                type="textarea"
                autosize
                readonly
                label="修改密码"
                :rules="[{ required: true, message: '请输入密码' }]"
                placeholder="请输入密码"
            />
            <van-field
                v-model="passobj.oldpassword"
                type="password"
                label="原密码"
                :rules="[{ required: true, message: '请输入原密码' }]"
                placeholder="请输入原密码"
            />
            <van-field
                v-model="passobj.newpassword"
                type="password"
                label="新密码"
                :rules="[{ required: true, message: '请输入10到16位数字加大小写字母' }]"
                placeholder="请输入10到16位数字加大小写字母"
            />
            <van-field
                style="margin-bottom: 55px;"
                type="password"
                v-model="passobj.newpasswords"
                label="确认密码"
                :rules="[{ required: true, message: '请输入确认密码' }]"
                placeholder="请输入确认密码"
            />
            <div class="van-submit-bar">
                <van-button round size="small" type="default" @click="onCancel">取消</van-button>
                <van-button round size="small" type="success" @click="onConfirm">确认</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
import {reactive, onMounted, toRefs} from 'vue'
import {getLocal,setLocal} from '@/common/js/utils'
import {useRouter} from 'vue-router'
import {diyPost} from "@/service/home";
import AjaxApi from "@/utils/api";
import {switchRole} from "@/service/user";
import {showToast} from "vant";

export default {
    setup() {
        const router = useRouter()
        const state = reactive({
            userInfo: null,
            text: '必须包含数字、小写字母、大写字母、特殊符号(~\'!@#$%^&*()-+_=:?.[]<>/,;{})，长度不少于8位',
            loading: true,
            showPopover: false,
            actions: [],
            currentRoleName: null,
            passwordRule: null,
            passobj: {},
            includeTip: null,
            show: false,
            navbar: [],
            avatarUrl: '',
            errorImg: require('@/assets/touxiang.png'),
        })

        onMounted(async () => {
            const userInfo = JSON.parse(getLocal('userInfo'))
            state.userInfo = userInfo
            console.log(userInfo)
            if (userInfo.roleList) {
                state.actions.push({'text': '修改密码'})

                userInfo.roleList.forEach(e => {
                    state.actions.push({'text': e})
                })
                state.currentRoleName = userInfo.roleName
                state.avatarUrl = '/file/view/' + (userInfo ? userInfo.humanCode : '');
                if(userInfo.sex == 'male' || userInfo.sex == '男'){
                    state.errorImg= require('@/assets/male.png')
                }else {
                    state.errorImg= require('@/assets/female.png')
                }
                console.log(`userInfo`,userInfo)
            }
            state.loading = false
            getPasswordStrategyModify()
            getNavBar()
        })
        const errorfun = (e) =>{
            let img = e.srcElement;
            img.src = state.errorImg;
            img.onerror = null; //防止闪图
        }

        const getNavBar = async () => {
            await diyPost(AjaxApi.navBar, {showLocale: 4}).then(response => {
                    state.navbar = response.data.info;
                    let navBarUrls = []
                    state.navbar.forEach(item => {
                        if (state.userInfo.humanCode !== "syt_visitor") {
                            if (item.dataSources && item.dataSources.indexOf('/') == 0) {
                                diyPost(item.dataSources, {}).then(res => {
                                    if (res.data.code === '00000') {
                                        item.count = res.data.info;
                                    }
                                })
                            }
                        }
                        navBarUrls.push(item.url)
                    })
                    console.log(navBarUrls)
                }
            )
        }

        const onSelect = (action) => {
            console.log(`action`,action)
            if(action.text == '修改密码'){
                state.show = true
            }else {
                switchRole({role: action.text}).then(res=>{
                    console.log(res)
                    if (res.data && res.data.code === "00000") {
                        let data = res.data
                        let list = []
                        data.info.roles.forEach(item =>{
                            list.push(item.rolename)
                        })
                        data.info.roleList = list
                        setLocal('userInfo', JSON.stringify(data.info))
                    }
                })
                state.currentRoleName = action.text
            }

        }
        const goBack = () => {
            router.go(-1)
        }

        const getPasswordStrategyModify = () => {
            let context = {
                code: "PASSWORD_STRATEGY_MODIFY"
            };
            diyPost(AjaxApi.getByCode, context).then(res => {
                console.log(`res`,res)
                if(res.data.code === "00000"){
                    state.passwordRule = res.data.info;
                    if(state.passwordRule.passRegExp){
                        state.includeTip = state.passwordRule.includeTip;
                    }
                }
            });
        }

        const onCancel = () => {
            state.show = false;
        }

        const onConfirm = async () => {
            if(state.passobj.newpassword!==state.passobj.newpasswords){
                return;
            }
            if(state.passwordRule){
                // 校验正则
                if(state.passwordRule.passRegExp){
                    let RE_PASS = new RegExp(state.passwordRule.passRegExp);
                    if (!RE_PASS.test(state.passobj.newpassword)) {
                        // Dialog.alert({
                        //     message: "密码强度不符：" + state.passwordRule.includeTip
                        // }).then(() => {
                        //     // on close
                        // });
                        // this.$modal.msgError("密码强度不符：" + this.passwordRule.includeTip);
                        showToast({type: 'fail', message: "密码强度不符：" + state.passwordRule.includeTip});

                        return;
                    }
                }
                // 密码长度限制
                if(!!state.passwordRule.passwordlength && state.passobj.newpassword.length<state.passwordRule.passwordlength){
                    // Dialog.alert({
                    //     message: "密码长度不少于" + state.passwordRule.passwordlength
                    // }).then(() => {
                    //     // on close
                    // });
                    // this.$modal.msgError("密码长度不少于" + this.passwordRule.passwordlength);
                    // done();
                    showToast({type: 'fail', message: "密码长度不少于" + state.passwordRule.passwordlength});

                    return;
                }
            }
            await diyPost(AjaxApi.changePasswd, state.passobj).then(response => {
                if (response.data.code == "00000") {
                    // Dialog.alert({
                    //     type: 'success',
                    //     title: '成功提示',
                    //     message: "修改成功, 重新登录"
                    // }).then(() => {
                    //     // on close
                    // });
                    showToast('修改成功');
                    state.passobj = {};
                    state.show = false;
                } else {
                    // Dialog.alert({
                    //     type: 'danger',
                    //     title: '失败提示',
                    //     message: '修改失败'
                    // }).then(() => {
                    //     // on close
                    // });
                    showToast({type: 'fail', message: '修改失败'});

                    state.show = false;
                }
            })
        }

        const goTo = (r, query) => {
            console.log(r,query,window.location.origin)
            // window.location.href = window.location.origin + "/#" + r
            router.push({path: r, query: query || {}})
        }

        return {
            ...toRefs(state),
            onSelect,
            goBack,
            goTo,
            errorfun,
            onCancel,
            onConfirm,
            getPasswordStrategyModify
        }
    }
}
</script>

<style lang="less" scoped>
@import '../common/style/mixin';

.user-box {
    .user-header {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 10000;
        .fj();
        .wh(100%, 44px);
        line-height: 44px;
        padding: 0 10px;
        .boxSizing();
        color: #252525;
        background: #fff;
        border-bottom: 1px solid #dcdcdc;

        .user-name {
            font-size: 14px;
        }
    }

    .user-info {
        width: 100%;
        /* margin: 10px; */
        height: 115px;
        background: #3875C6;

        .info {
            position: relative;
            display: flex;
            width: 100%;
            height: 100%;
            padding: 15px 20px 20px;
            .boxSizing();

            img {
                .wh(60px, 60px);
                border-radius: 50%;
                margin-top: 4px;
            }

            .user-desc {
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                line-height: 20px;
                font-size: 14px;
                color: #fff;
                .posrt{
                    position: absolute;
                    right: 20px;
                    top: 20px;
                    border-radius: 5px;
                }
                div {
                    color: #fff;
                    font-size: 14px;
                    padding: 2px 0;
                }
            }

            .account-setting {
                position: absolute;
                top: 10px;
                right: 20px;
                font-size: 13px;
                color: #fff;

                .van-icon-setting-o {
                    font-size: 16px;
                    vertical-align: -3px;
                    margin-right: 4px;
                }
            }
        }
    }

    .user-list {
        padding: 0 20px;
        margin-top: 20px;

        li {
            height: 40px;
            line-height: 40px;
            display: flex;
            justify-content: space-between;
            font-size: 14px;

            .van-icon-arrow {
                margin-top: 13px;
            }
        }
    }
    .password{
        position: absolute;
        right: 140px;
        top: 20px;
    }
}
.van-submit-bar {
    border-top: 1px solid #eee;
    box-shadow: 0 -2px 3px -1px #eee;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    display: flex;
    height: 50px;
}

.van-submit-bar .van-button {
    margin: 5px;
    width: 88% !important;
    /*font-size: 16px;*/
}
</style>

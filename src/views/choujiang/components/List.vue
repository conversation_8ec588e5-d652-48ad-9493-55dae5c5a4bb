<template>
    <van-list
            v-model="isLoading"
            :finished="finished"
            finished-text="没有更多了"
            @load="loadMore"
            :error.sync="error"
            error-text="请求失败，点击重新加载"
            :immediate-check="firstLoad">
        <slot v-bind:list="list2"></slot>
    </van-list>
</template>
<script>
    import http from "@/calendar/utils/http";

    export default {
        name: "Base_List",
        props: {
            url: String,
            queryObj: {
                type: Object,
                default() {
                    return {};
                }
            },
            queryJson: String,
            list: {
                type: Array,
                default() {
                    return [];
                }
            }
        },
        data() {
            return {
                pageIndex: -1,
                pageSize: 20,
                list2: [],
                isLoading: false,
                finished: false,
                error: null
            };
        },
        watch: {
            list(newV) {
                if (newV !== this.list2) {
                    this.list2 = newV;
                    this.count = null;
                }
            },
            queryObj() {
                this.onReload();
            },
            queryJson() {
                this.onReload();
            }
        },
        computed: {
            firstLoad() {
                return !!this.url;
            }
        },
        methods: {
            onReload() {
                this.finished = true;
                this.pageIndex = -1;
                this.pageSize = 20;
                this.list2 = [];
                this.$emit("update:list", this.list2);
                this.finished = false;
                this.isLoading = true;
                this.loadMore();
            },
            loadMore() {
                if (this.isLoading) {
                    this.pageIndex++;
                    let obj = this.queryObj;
                    if (this.queryJson) {
                        obj = JSON.parse(this.queryJson);
                    }
                    let newObj = Object.assign(
                        {
                            pageIndex: this.pageIndex,
                            pageSize: this.pageSize
                        },
                        obj
                    );
                    http.post(this.url, newObj).then(response => {
                        this.$emit("allData", response.data);
                        if (response.data.result) {
                            let pageData = response.data.result;
                            // console.log("222======loadMore success base-list", pageData);
                            if (
                                pageData.hasOwnProperty("totalPage") &&
                                pageData.hasOwnProperty("currentPage")
                            ) {
                                this.list2 = this.list2.concat(pageData.data);
                                this.$emit("update:list", this.list2);
                                this.$emit("update:pageData", pageData);
                                this.finished = pageData.totalPage <= pageData.currentPage;
                            } else {
                                this.error = true;
                            }
                        } else {
                            let pageData = response.data;
                            // console.log("=====", pageData);
                            if (
                                pageData.hasOwnProperty("totalPage") &&
                                pageData.hasOwnProperty("currentPage")
                            ) {
                                this.list2 = this.list2.concat(pageData.data);
                                this.$emit("update:list", this.list2);
                                this.$emit("update:pageData", pageData);
                                this.finished = pageData.totalPage <= pageData.currentPage;
                            } else {
                                this.error = true;
                            }
                        }
                    })
                        .catch(error => {
                            console.error(error);
                            this.error = true;
                        })
                        .finally(() => (this.isLoading = false));
                }
            }
        }
    };
</script>
<style scoped>
</style>

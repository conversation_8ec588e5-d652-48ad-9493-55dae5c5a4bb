import routerUtil from "@/calendar/utils/router";

export default {
    props: {
        type: String
    },
    methods: {
        to(path, query) {
            if (!query) {
                query = {};
            }
            if (!query.type)
                query.type = this.type;
            routerUtil.to(path, query);
        },
        replace(path, query) {
            if (!query) {
                query = {};
            }
            if (!query.type)
                query.type = this.type;
            routerUtil.replace(path, query);
        }
    }
}

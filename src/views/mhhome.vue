<template>
    <div>
        <div class="content-view">
            <div class="back-view"></div>
            <div v-for="(item,index) in blockslist" style="margin: 15px;border-radius: 10px;overflow: hidden;z-index: 999;position: relative;" :key="index">
                <component :isnew="true" :is="item.data.model" :item="item"></component>
            </div>
            <div class="good" style="margin-top: 60px">
                <header class="good-header"></header>
            </div>
        </div>
    </div>
</template>

<script>
import {reactive, onMounted, toRefs, nextTick} from 'vue'
import {useRouter} from 'vue-router'
// import swiper from '@/components/Swiper'
// import navBar from '@/components/NavBar'
import {
    // login1, user,
    getAllDesktop, getDesktopById,
} from '@/service/home'

import {
    getLocal,
    // setCookies
} from '@/common/js/utils'
import {
    mapState,
    // mapMutations
} from "vuex";
// import {useStore} from 'vuex'
// import {setLocal} from '@/common/js/utils'
import lunbotu from "@/views/DesktopModel/lunbotu.vue";
import {GetAppletList} from "@/service/applet";
import {closeToast, showLoadingToast} from "vant";
import {GetDataCategory, GetNewsList} from "@/service/portal";
import wodefuwu from "@/views/DesktopModel/wodefuwu";
import yingyongPT from "@/views/DesktopModel/yingyongPT";
import jichengxitong from "@/views/DesktopModel/jichengxitong";
import daibanshixiang from "@/views/DesktopModel/daibanshixiang";
import wodeliebiao from "@/views/DesktopModel/wodeliebiao";
import wodeshuju from "@/views/DesktopModel/wodeshuju";
import wodexinxi from "@/views/DesktopModel/wodexinxi";
import xinwen from "@/views/DesktopModel/xinwen";
// import xiaoxirenwu from "@/views/DesktopModel/xiaoxirenwu";
import qiantao from "@/views/DesktopModel/qiantao";
import xiaoli from "@/views/DesktopModel/xiaoli";
import kebiao from "@/views/DesktopModel/kebiao";
import gongzuotai from "@/views/DesktopModel/gongzuotai";
import fuwudating from "@/views/DesktopModel/fuwudating";
import tongzhigonggao from "@/views/DesktopModel/tongzhigonggao";

// import {diyPost} from "@/service/home";
// import AjaxApi from "@/utils/api";

export default {
    name: 'home',
    components: {
        // swiper,
        // navBar,
        lunbotu,
        wodefuwu,
        yingyongPT,
        jichengxitong,
        daibanshixiang,
        wodeliebiao,
        wodeshuju,
        wodexinxi,
        xinwen,
        // xiaoxirenwu,
        qiantao,
        xiaoli,
        kebiao,
        gongzuotai,
        fuwudating,
        tongzhigonggao
    },
    computed: {
        ...mapState(["baseUrl"])
    },
    setup() {
        // const store = useStore()
        const router = useRouter()
        const state = reactive({
            desktop: {},
            defaultIcon: require("../assets/isnull.png"),
            active: 0,
            images: [],
            xwlist: [],
            appletList: [],
            servicelist: [],
            appList: [],
            sytServiceCenterList: [],
            newsList: [],

            swiperList: [], // 轮播图列表
            isLogin: false, // 是否已登录
            headerScroll: false, // 滚动透明判断
            loading: true,
            userInfo: {},
            blockslist: [],
            categoryList: [],
            // navbar: [],
        })
        // // 1.手动的映射和绑定
        // const mutations = mapMutations(["setUser", "setToken"])
        // const newMutations = {}
        // Object.keys(mutations).forEach(key => {
        //     newMutations[key] = mutations[key].bind({$store: store})
        // })
        // const {setUser, setToken} = newMutations

        onMounted(async () => {
            // //开发环境登录
            // let setData = new FormData();
            // // setData.append('username', '0430');
            // setData.append('username', '10001');
            // setData.append('password', 'sanyth123!');
            // setData.append('uid', 'b0baee9d279d34fa1dfd71aadb908c3f');
            // const {data} = await login1(setData)
            // setCookies('token', data.info.token)
            //
            // const token = getCookies('token')
            // setLocal('token', token)
            // setToken({token: token})
            // state.loading = false
            // if (token) {
            //     state.isLogin = true
            //     getUser();
            //     await init();
            // }

            const token = getLocal('token')
            if (token) await init();
            await getData()
        })

        nextTick(() => {
            window.addEventListener('scroll', () => {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
                scrollTop > 100 ? state.headerScroll = true : state.headerScroll = false
            })
        })

        const getData = () => {
            GetAppletList({
                queryParam: {type: "应用平台"},
                page: 1,
                pageSize: 7
            }).then(res => {
                if (res.data.code === "00000") {
                    state.servicelist = res.data.info.records;
                }
            });
            GetDataCategory({model: 'xinwen'}).then(res => {
                state.categoryList = res.data.info;
                getxwdata({name:0})
                // state.categoryList.forEach(item=>{
                //     if(item.id == state.categoryId){
                //         state.category = item.name
                //     }
                // })
                console.log(`state.category`,res)
            });
            // GetUserData({
            //     categoryId: '',
            //     limit: 6
            // }).then(res => {
            //     if (res.data.code === "00000") {
            //         state.applist = res.data.info
            //     }
            // });
        }

        const getxwdata = (tab) => {
            const context = {
                page: 1,
                pageSize: 9,
                // pageSize: 1,
                queryParam: {
                    categoryId: state.categoryList[tab.name].id,
                },
            };
            GetNewsList(context).then(res => {
                state.xwlist = res.data.info.records;
            });
        }

        // const getUser = async () => {
        //     const {data} = await user({})
        //     if (data.code === "00000") {
        //         state.userInfo = data.info
        //         setUser(data.info)
        //         setLocal('userInfo', JSON.stringify(data.info))
        //
        //         // await getNavBar()
        //     }
        // }

        const init = async () => {
            let json = {
                type: '移动端门户',
            }
            const {data} = await getAllDesktop(json)
            let Desktoplist = [];
            let desktop = {}
            if (data.code === "00000") {
                Desktoplist = data.info
            }
            Desktoplist.forEach(item => {
                if (item.type == '移动端门户') desktop = item
            })
            state.desktop = desktop
            await GetDesktopById()

        }

        const goTo = (app) => {
            showLoadingToast({
                message: '正在加载...',
                forbidClick: true,
                duration: 0
            });
            if (app.redirectUrl && app.redirectUrl !== '') {
                if (state.userInfo) {
                    let openUrl = app.redirectUrl.replace("#{humancode}", state.userInfo.humanCode);
                    // window.open(openUrl, '_blank')
                    window.location.href = openUrl
                }
            } else {
                window.location.href = app.redirectUrl
            }
            setTimeout(()=>{
                closeToast()
            },2000)
        }

        const GetDesktopById = async () => {
            let Desktoplist = []
            const {data} = await getDesktopById({id: state.desktop.id})
            if (data.code === "00000") {
                Desktoplist = data.info[0].blocks
            }
            console.log(`getDesktopById`,data)
            for (var a = 0; a < Desktoplist.length; a++) {
                let list = Desktoplist[a];
                if (list.frontConfig !== null) {
                    let list2 = {
                        data: list
                    }
                    state.blockslist.push(list2);
                } else {
                    let list2 = {
                        data: list
                    }
                    state.blockslist.push(list2);
                }
            }
        }

        const handleServices = (tap) => {
            let path = '';
            switch (tap) {
                case 'fw':
                    path = 'mine-service'
                    break
                case 'sj':
                    path = 'mine-dealt'
                    break
                case 'yy':
                    path = 'application'
                    break
                case 'xw':
                    path = 'news-center'
                    break
            }
            router.push({path: path})
        }

        return {
            ...toRefs(state),
            handleServices,
            goTo,
            getData,
            getxwdata
        }
    },
}
</script>

<style lang="less" scoped>
@import '../common/style/mixin';
@import '../common/style/home';

.content-view {
    margin-bottom: 56px;
    padding: 50px 0 60px;
    //background-color: #F4F9FD;
    //height: 100%;
    background-color: #F4F9FD;
    //background-image: url("../assets/u164.png");
    //background-repeat: no-repeat;
    //background-size: 100%;
    //background: linear-gradient(180deg, #3396FA 50%, rgba(240, 253, 253, 1) 100%);
}
.back-view {
    //padding: 20px 0 60px;
    //background-color: #F4F9FD;
    height: 170px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-image: url("../assets/u164.png");
    background-repeat: no-repeat;
    background-size: 100%;
    z-index: 1;
    //background: linear-gradient(180deg, #3875C6 50%, rgba(240, 253, 253, 1) 100%);
}
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
}

.service-row {
    overflow: hidden;
    font-size: 12px;
}

.title {
    border-top: 1px solid #eee;
}

.title span {
    margin: 10px 4px 0 10px;
    display: inline-block;
    height: 10px;
    width: 3px;
    background: #1a9fe4;
}

.title p {
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    display: inline-block;
}

.index-sml {
    width: 25%;
    height: 60px;
    float: left;
    text-align: center;
    margin-bottom: 20px;
}

.index-sml img {
    width: 26px;
    height: 26px;
}

.index-sml p {
    margin-top: 10px;
}


.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    //height: 29px;
    //line-height: 29px;
    //padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}

</style>

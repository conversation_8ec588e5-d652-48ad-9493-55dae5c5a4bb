<template>
    <div class="product-list-wrap">
        <div class="product-list-content">
            <header class="category-header wrap">
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
                <div class="header-right" @click="showSearchPopup=true">
                    <van-icon class="right-icon" :name="defaultSearchIcon" size="18"/>
                    <div class="right-div">
                        筛选
                    </div>
                </div>
            </header>
            <van-tabs v-model:active="active" color="#48a3ea" title-active-color="#48a3ea"
                      @click-tab="changeTab">
                <van-tab v-for="tab in tabsOptions" :key="tab.id" :title="tab.text">
                </van-tab>
            </van-tabs>
        </div>
        <div class="content">
            <list style="margin-bottom: 60px;" :url="url" :queryObj="queryObj"
                  v-on:update:pageData="updatePageData($event)">
                <template v-slot:default="slotProps">
                    <div v-if="pageTotal>0" style="font-weight: bold;padding: 10px;margin-left: 5px;">
                        <span>总记录：{{pageTotal}}</span>
                    </div>
                    <van-cell :key="item.id" v-for="item in slotProps.list"
                              @click="handleOptDetail(item,active)">
                        <template v-if="active === 2">
                            <span class="item_right_top">
                                {{dateFormat_YMD(item.createTime)}}
                            </span>
                            <div style="display: flex;justify-content: space-between;" class="cell-class-name">
                                <div>
                                    <div class="list-item-name">{{ item.name }}
                                    </div>
                                    <div class="list-item-info">流程分类：{{item.category}}</div>
                                    <div class="list-item-info">当前节点：{{item.tasks?item.tasks[0].name:''}}</div>
                                    <div class="list-item-info">提交时间：{{dateFormat_date(item.createTime)}}</div>
                                    <div class="list-item-info">审批结果：{{setNewKeyVal(item.result,'BPM_PROCESS_INSTANCE_RESULT')[0]}}</div>

                                  <div class="item_right_btn">
                                        <van-tag plain
                                                 :color="setNewKeyVal(item.status,'BPM_PROCESS_INSTANCE_STATUS')[1]">
                                            {{setNewKeyVal(item.status,'BPM_PROCESS_INSTANCE_STATUS')[0]}}
                                        </van-tag>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-else-if="active === 4">
                            <div style="display: flex;justify-content: space-between;" class="cell-class-name">
                                <div>
                                    <div class="list-item-name">{{ item.name }}
                                    </div>
                                    <div class="list-item-info">
                                        分类：{{setNewKeyValTab4(item.categoryId,'fl')}}
                                    </div>
                                    <div class="list-item-info">任务时间：{{formatTime(item)}}</div>
                                    <div class="list-item-info">任务状态：{{formatStatus(item.runStatus)}}</div>
                                    <div class="list-item-info">任务方式：{{item.style===10?'表单+流程':'表单'}}</div>
                                    <div class="list-item-info">
                                        审核流程：{{setNewKeyValTab4(item.processDefinitionKey,'shlc')}}
                                    </div>
                                    <div class="list-item-info">填报状态：{{item.status==='F'?'未上报':'已上报'}}</div>
                                    <div class="list-item-info">已读状态：{{item.readStatus==='1'?'已读':'未读'}}</div>
                                    <div class="item_right_btn">
                                        <van-tag v-if="item.status==='F'&&item.runStatusPortal==='1'" plain
                                                 type="success" @click="taskreported(item)">
                                            上报
                                        </van-tag>
                                        <van-tag v-if="item.status==='T'" plain type="primary"
                                                 @click="taskhandleDetail(item)">
                                            查看
                                        </van-tag>
                                        <van-tag v-if="item.status==='T'&&item.runStatusPortal==='1'" plain
                                                 type="success">
                                            修改
                                        </van-tag>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-else-if="active === 6">
                            <div style="display: flex;justify-content: space-between;" class="cell-class-name">
                                <div>
                                    <div class="list-item-name">{{ item.name }}
                                    </div>
                                    <div class="list-item-info">发起人：{{item.startUserName}}</div>

                                    <div class="list-item-info">发起时间：{{dateFormat_date(item.createTime)}}</div>
                                    <div class="list-item-info">办结时间：{{dateFormat_date(item.endTime)}}</div>
                                    <div class="list-item-info">状态：{{setNewKeyVal(item.status,'BPM_PROCESS_INSTANCE_STATUS')[0]}}</div>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <span class="item_right_top">
                                {{dateFormat_YMD(item.createTime)}}
                            </span>
                            <div style="display: flex;justify-content: space-between" class="cell-class-name">
                                <div v-if="item.processInstance">
                                    <div class="list-item-name">{{ item.processInstance.startUserNickname }}提交了{{
                                        item.processInstance.name }}
                                    </div>
                                    <div class="list-item-info">当前节点：{{item.name}}</div>
                                    <div class="list-item-info">所属流程：{{item.processInstance.name}}</div>
                                    <div class="list-item-info">发起人：{{item.processInstance.startUserNickname}}</div>
                                    <div class="list-item-info">发起时间：{{dateFormat_date(item.createTime)}}</div>
                                </div>
                                <div class="item_right_btn">
                                    <van-tag v-if="active===0" plain
                                             :type="item.suspensionState===1?'success':'primary'">
                                        {{item.suspensionState===1?'激活':'挂起'}}
                                    </van-tag>
                                    <van-tag v-if="active===1 || active===3" plain
                                             :color="setNewKeyVal(item.result,'BPM_PROCESS_INSTANCE_RESULT')[1]">
                                        {{setNewKeyVal(item.result,'BPM_PROCESS_INSTANCE_RESULT')[0]}}
                                    </van-tag>
                                </div>
                            </div>
                        </template>
                    </van-cell>
                </template>
            </list>
        </div>
<!--        <nav-bar/>-->
        <van-popup v-model:show="showSearchPopup" position="top" :style="{height:'auto',width:'100%'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <template v-if="active !== 'self'">
                        <van-field label="当前节点" readonly/>
                        <van-field label="" v-model="name" placeholder="请输入当前节点"/>
                        <van-field label="所属流程" readonly/>
                        <van-field label="" v-model="processDefinitionName" placeholder="请输入所属流程"/>
                        <van-field label="发起时间" readonly/>
                        <van-field :model-value="showKjDate" placeholder="请选择发起时间"
                                   readonly
                                   clickable
                                   @click="showkssjPicker = true"/>
                        <van-popup v-model:show="showkssjPicker" position="top">
                            <van-picker-group v-slot: title="预约日期"
                                              :tabs="['开始日期', '结束日期']"
                                              @confirm="onConfirm"
                                              @cancel="onCancel">
                                <van-date-picker v-model="startEnd" :min-date="minDate"/>
                                <van-date-picker v-model="endDate" :min-date="minDate"/>
                            </van-picker-group>
                        </van-popup>
                    </template>
                    <template v-else>
                        <van-field label="流程名称" readonly/>
                        <van-field label="" placeholder="请输入流程名称"/>
                        <van-field label="流程分类" readonly/>
                        <van-field label="" readonly>
                            <template #input>
                                <div style="width: 100%;height: 100%;">
                                    <van-button style="margin: 3px;width: 23%;float: left;" size="small"
                                                v-for="(category,index) in serviceList" :key="index"
                                                hairline
                                                :plain="category.value===categoryId"
                                                :type="category.value===categoryId?'primary':'default'"
                                                @click="handleClik1(category)">{{category.text}}
                                    </van-button>
                                </div>
                            </template>
                        </van-field>
                        <van-field label="状态" readonly/>
                        <van-field label="" readonly>
                            <template #input>
                                <div style="width: 100%;height: 100%;">
                                    <van-button style="margin: 3px;width: 23%;float: left;" size="small"
                                                v-for="(org,index) in serviceStatus" :key="index"
                                                hairline
                                                :plain="org.value===orgid"
                                                :type="org.value===orgid?'primary':'default'"
                                                @click="handleClik2(org)">{{org.text}}
                                    </van-button>
                                </div>
                            </template>
                        </van-field>
                    </template>
                </van-cell-group>
            </div>
            <div class="van-submit-bar-search">
                <van-button type="primary" round size="small" @click="handleSearch"> 搜&nbsp;&nbsp;索
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    // import navBar from '@/components/NavBar'
    import List from "@/components/List";
    import AjaxApi from "@/utils/api";
    import AjaxApiPub from "@/service/publicResource";
    import {getProcessDefinitionList,} from "@/service/bpmProcessDefinition";
    import {queryOne} from "@/service/bpmProcessDefinition";
    import Utils from "@/utils/momentWrap";
    import {diyPost} from '@/service/home'
    import {useRoute, useRouter} from "vue-router";
    import {onMounted, reactive, toRefs} from "vue";
    import {mapState} from "vuex";
    import {showFailToast} from "vant";

    export default {
        components: {
            // navBar,
            List
        },
        computed: {
            ...mapState(["baseUrl", "defaultSearchIcon"]),
        },
        setup() {
            const router = useRouter()
            const route = useRoute()
            const state = reactive({
                minDate: new Date(1900, 0, 1),
                startEnd: [],
                endDate: [],
                processDefinitionId: null,
                activeTab: null,
                pageTotal: 0,

                active: Number(route.query.setNewOldActiveTab) ? Number(route.query.setNewOldActiveTab) : 0,  // 转换为number类型
                url: AjaxApi.todoPage,
                queryObj: {oldActiveTab: 'todo'},
                tabsOptions: [
                    {id: 'todo', text: '待办任务', value: 'todo'},
                    {id: 'done', text: '已办任务', value: 'done'},
                    {id: 'self', text: '我发起的', value: 'self'},
                    {id: 'ower', text: '我的转办', value: 'ower'},
                    {id: 'isStyle', text: '我的填报', value: 'isStyle'},
                    {id: 'make-copy', text: '抄送', value: 'make-copy'},
                    {id: 'wode', text: '我的数据', value: 'wode'},
                ],
                sytSysDictKeyVal: [],
                searchName: null,
                showSearchPopup: false,
                currentDate: new Date(),
                showKjDate: null,
                kssj: null,
                jssj: null,
                name: '',
                processDefinitionName: '',
                beginCreateTime: null,
                endCreateTime: null,
                showkssjPicker: false,
                showjssjPicker: false,
                serviceList: [
                    {text: "默认", value: "all"},
                    {text: "OA", value: "0"},
                ],
                serviceStatus: [
                    {text: "全部", value: "all"},
                    {text: "进行中", value: "0"},
                    {text: "已完成", value: "1"},
                ],
                categoryId: null,
                orgid: null,
                categoryIdsData: null,
                processlist: null,
                project: null,
            })
            onMounted(async () => {
                getSelectList()
                getSytSysDict()
                const {setNewOldActiveTab,queryParam} = route.query;
                // if(setNewOldActiveTab == 5){
                //     state.tabsOptions.push({id: 'make-copy', text: '抄送', value: 'make-copy'})
                // }
                console.log(`state.tabsOptions`,state.tabsOptions)
                if (setNewOldActiveTab) {
                    let tabType = state.tabsOptions[setNewOldActiveTab].value;
                    let param = null
                    if(queryParam){
                        param = JSON.parse(queryParam)
                    }
                    console.log(`tabType`,tabType)
                    console.log(`param`,param)
                    state.url = getStrUrl(tabType)
                    if (tabType === 'isStyle') {
                        state.queryObj = {
                            queryParam: {isStyle: "0,2"}
                        }
                    } else {
                        if(tabType == 'wode'){
                            if(queryParam){
                                state.queryObj = {
                                    name: param.processDefinitionName,
                                    processDefinitionId: param.processDefinitionId,
                                    result: param.result,
                                    scope: "personal",
                                    status: param.status
                                }
                            }
                        }else if(tabType == 'make-copy'){
                            if(queryParam){
                                state.queryObj = {
                                    processDefinitionId: param.processDefinitionId,
                                    processDefinitionName: param.processDefinitionName,
                                    queryParam:{
                                        processDefinitionId: param.processDefinitionId
                                    }
                                }
                            }
                        }else {
                            if(queryParam){
                                state.queryObj = {
                                    processDefinitionId: param.processDefinitionId,
                                    processDefinitionName: param.processDefinitionName
                                }
                            }else {
                                state.queryObj = {
                                    oldActiveTab: tabType
                                }
                            }
                            console.log(`state.queryObj`,state.queryObj)
                        }

                    }
                    console.log(`state.queryObj=====`,state.queryObj)
                }
            })

            const getSelectList = () => {
                diyPost(AjaxApiPub.sytCodeTbrwQueryList, {}).then(res => {
                    if (res.status === 200) {
                        state.categoryIdsData = res.data.info;
                    }
                })
                getProcessDefinitionList({suspensionState: 1}).then(res => {
                    if (res.data.code == "00000") {
                        if (res.data.code == "00000") {
                            state.processlist = res.data.info
                        }
                    }
                })
            }
            const onConfirm = () => {
                state.showKjDate = state.startEnd.join('/') + "-" + state.endDate.join('/');
                state.beginCreateTime = new Date(state.startEnd);
                state.endCreateTime = new Date(state.endDate);
                console.log(state.showKjDate)
                state.showkssjPicker = false
            };
            const onCancel = () => {
                state.showkssjPicker = false
            };
            const updatePageData = (e) => {
                state.pageTotal = e.total;
            }
            const formatTime = (val) => {
                let tmp = "";
                if (!!val.startTime && !!val.endTime) {
                    tmp += Utils.dateFormat_date(val.startTime);
                    tmp += " -- ";
                    tmp += Utils.dateFormat_date(val.endTime);
                } else {
                    tmp += "无限制";
                }
                return tmp;
            }
            const formatStatus = (newStr) => {
                let setVal = null;
                switch (newStr) {
                    case "0":
                        setVal = '已暂停';
                        break
                    case "1":
                        setVal = '进行中';
                        break
                    case "2":
                        setVal = '已结束';
                        break
                    case "3":
                        setVal = '未开始';
                        break
                }
                return setVal;
            }
            const getStrUrl = (newStr) => {
                let setUrl = null;
                switch (newStr) {
                    case "done":
                        setUrl = AjaxApi.donePage;
                        break
                    case "self":
                        setUrl = AjaxApi.myPage;
                        break
                    case "ower":
                        setUrl = AjaxApi.owerPage;
                        break
                    case "todo":
                        setUrl = AjaxApi.todoPage;
                        break
                    case "isStyle":
                        setUrl = AjaxApiPub.sytTaskProjectQueryPage;
                        break
                    case "make-copy":
                        setUrl = AjaxApi.makeCopy;
                        break
                    case "wode":
                        setUrl = AjaxApi.bpmProcessInstanceExt;
                        break
                }
                return setUrl;
            }
            const setNewKeyVal = (val, key) => {
                let newVal = []
                if (val && key && state.sytSysDictKeyVal) {
                    newVal = state.sytSysDictKeyVal.filter(item => {
                        return item['code'] === key && item["value"] === val.toString()
                    })
                }
                let newStr = newVal.length > 0 ? newVal[0].name : ''
                let tagColor = ""
                if (newStr) {
                    switch (newStr) {
                        case "进行中":
                            tagColor = "#1989fa"
                            break
                        case "已完成":
                            tagColor = "#07c160"
                            break
                        case "处理中":
                            tagColor = "#31af90"
                            break
                        case "通过":
                            tagColor = "#07c160"
                            break
                        case "不通过":
                            tagColor = "#DD2C2C"
                            break
                        case "已取消":
                            tagColor = "#909090"
                            break
                        case "驳回":
                            tagColor = "#ffe1e1"
                            break
                        case "委派":
                            tagColor = "#3396FA"
                            break
                        case "转办":
                            tagColor = "#76b806"
                            break
                    }
                }
                return [newStr, tagColor];
            }
            const getSytSysDict = () => {
                diyPost(AjaxApi.sytSysDict,).then(res => {
                    if (res.status === 200) {
                        state.sytSysDictKeyVal = res.data
                    }
                })
            }

            const handleOptDetail = (item, active) => {
                console.log(`item`,item)
                if (active !== 4) {
                    router.push({
                        path: '/mine-dealt-detail',
                        query: {
                            currentId: active === 2 || active === 6 ? item && item.id : item.processInstance.id,
                            processDefinitionId: active === 2 || active === 6 ? item && item.processDefinitionId : item.processInstance.processDefinitionId,
                            activeType: active,
                            currentStatus: item.status,
                            taskId: item.id
                        }
                    });
                }
            }
            const changeTab = (tab) => {
                console.log(`tab`,tab)
                state.pageTotal = 0;
                state.active = Number(tab.name)
                if (tab.name || state.active === 0) {
                    let tabType = state.tabsOptions[state.active].value
                    state.url = getStrUrl(tabType)
                    if (tabType === 'isStyle') {
                        state.queryObj = {
                            queryParam: {isStyle: "0,2"}
                        }
                    } else {
                        if(tabType == 'wode'){
                            state.queryObj = {

                            }
                        }else if(tabType == 'make-copy'){
                            state.queryObj = {
                                processDefinitionId: '',
                                processDefinitionName: 'param.processDefinitionName',
                                queryParam:{
                                    processDefinitionId: 'param.processDefinitionId'
                                }
                            }
                        }else {
                            state.queryObj = {
                                oldActiveTab: tabType
                            }
                        }
                    }
                }
            }
            const handleClik1 = (model) => {
                state.categoryId = model.value
            }
            const handleClik2 = (model) => {
                state.orgid = model.value
            }

            const getDate = (o) => {
                return Utils.getDate(o);
            }
            const dateFormat_YMD = (o) => {
                return Utils.dateFormat_YMD(o);
            }
            const dateFormat_date = (o) => {
                return Utils.dateFormat_date(o);
            }
            const handleSearch = () => {
                state.queryObj = Object.assign(state.queryObj,{
                    name: state.name,
                    processDefinitionName: state.processDefinitionName,
                    beginCreateTime: state.beginCreateTime,
                    endCreateTime: state.endCreateTime
                })
                state.showSearchPopup = false;
            }
            /** 任务填报-详情 */
            const taskhandleDetail = (item) => {
                queryOne({projectId: item.id}, "/sytTaskFillRecord").then(res => {
                    state.project = item;
                    if (!res.data.info) {
                        showFailToast("未查询到填报记录")
                    } else if (res.data.code === "00000") {
                        // TODO 先临时用原来的页面，后期调整
                        if (item.style === "10") {// 带流程
                            router.push({
                                path: '/mine-dealt-detail',
                                query: {
                                    currentId: res.data.info.processInstId,
                                    activeType: 4,
                                    backUrl: '/mine-dealt'
                                }
                            });
                        } else if (item.style === "20") {// 表单
                            router.push({
                                path: "/mine-service-task-detail",
                                query: {
                                    name: '填报详情-' + item.name,
                                    id: item.id,
                                    activeType: 4,
                                    backUrl: '/mine-dealt'
                                }
                            });
                        }
                    } else if (res.data.code === "00001") {
                        showFailToast(res.data.info)
                    }
                })
            }
            /**
             * 任务填报-上传
             * @param item
             */
            const taskreported = (item) => {
                queryOne({projectId: item.id}, "/sytTaskPerson").then(response => {
                    if (response.data.code == "00000") {
                        if (response.data.info.project) {
                            router.push({
                                path: "/mine-service-task-edit",
                                query: {
                                    project: JSON.stringify(response.data.info.project),
                                    name: item.name,
                                    activeType: state.active,
                                    activeTab: '待办',
                                    backUrl: 'mine-dealt'
                                }
                            })
                        }
                    } else {
                        showFailToast(response.data.info);
                    }
                })
            }

            const setNewKeyValTab4 = (val, key) => {
                let newVal = []
                if (val && key) {
                    if (key === 'fl' && state.categoryIdsData) {
                        newVal = state.categoryIdsData.filter(item => {
                            return item['id'] === val
                        })
                    }

                    if (key === 'shlc' && state.processlist) {
                        newVal = state.processlist.filter(item => {
                            return item['key'] === val
                        })
                    }
                }
                let newStr = newVal.length > 0 ? newVal[0].name : ''
                return newStr
            }
            return {
                ...toRefs(state),
                taskhandleDetail,
                taskreported,
                formatTime,
                formatStatus,
                setNewKeyValTab4,
                setNewKeyVal,
                getSytSysDict,
                handleOptDetail,
                changeTab,
                onConfirm,
                onCancel,
                handleClik1,
                handleClik2,
                getDate,
                dateFormat_YMD,
                dateFormat_date,
                handleSearch,
                updatePageData,
            }
        },
    };
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';

    .product-list-content {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }

    ::v-deep .van-tabs .van-tabs__content {
        background: #fff;
        border-top: 5px solid #f5f5f5;
    }
    .content {
        height: calc(~"(100vh - 90px)");
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 98px;
        margin-bottom: 76px;
    }

    .cell-class-name {
        color: #2c3e50;
        text-align: left;
    }

    .van-tag--plain::before {
        border: unset !important;
    }

    .van-dropdown-menu::after {
        border: none;
    }

</style>

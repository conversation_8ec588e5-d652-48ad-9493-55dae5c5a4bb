<template>
    <div>
        <div class="product-list-content">
            <header class="category-header wrap">
                <i class="nbicon nbfanhui" @click="goBack"></i>
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
                <div v-if="active === 1" class="header-right" @click="showSearchPopup = true">
                    <van-icon class="right-icon" :name="defaultSearchIcon" size="18"/>
                    <div class="right-div">
                        筛选
                    </div>
                </div>
            </header>
            <van-tabs v-model:active="active" color="#48a3ea" title-active-color="#48a3ea"
                      @click-tab="changeTab">
                <van-tab v-for="tab in tabsOptions" :key="tab.id" :title="tab.text">
                </van-tab>
            </van-tabs>
            <div class="checktime" v-if="active===0">
                <span class="chktime-span" @click="show = true"><van-icon name="notes-o"/>{{Dates[0]+`年`+Dates[1]+`月`+Dates[2]+`日`}}<van-icon
                        name="arrow-down"/></span>
                <van-button size="mini" type="default"
                            :style="currentChkMark==='next'?'background-color: rgba(242, 242, 242, 1);':''"
                            @click="changeday('next')">后一天
                    <van-icon name="arrow"/>
                </van-button>
                <van-button size="mini" type="default"
                            :style="currentChkMark==='current'?'background-color: rgba(242, 242, 242, 1);':''"
                            @click="changeday('current')">今天
                </van-button>
                <van-button size="mini" type="default"
                            :style="currentChkMark==='last'?'background-color: rgba(242, 242, 242, 1);':''"
                            @click="changeday('last')">
                    <van-icon name="arrow-left"/>
                    前一天
                </van-button>
            </div>
        </div>
        <div class="content" :style="active===0?'margin-top:140px;':'margin-top:95px;'">
            <list style="margin-bottom: 60px;" :url="url" :queryObj="queryObj">
                <template v-slot:default="slotProps">
                    <van-cell v-for="(item,index) in slotProps.list" :key="index"
                              @click="handleDetail(item)">
                         <span v-if="active===1" class="item_right_top">
                             {{dateFormat_YHMHM(item.createTime)}}
                         </span>
                        <div style="display: flex;justify-content: space-between;"
                             class=" contents_box_left cell-class-name">
                            <div v-if="active===0">
                                <div class="list-item-name">{{ item.name }}
                                </div>
                                <div class="list-item-info">{{item.fsss}}</div>
                                <div class="list-item-info">容纳{{item.krnrs}}人</div>
                                <div>
                                    <div v-for="(tqm,index) in timeQuantunMark(item)" :style="tqm.color"
                                         class="contents_box_timeQuantunMark" :key="index">
                                        <van-tag></van-tag>
                                    </div>
                                </div>
                                <!--                                <div class="item_right_btn">-->
                                <!--                                    <p style="margin-top: 86px;float: right;"-->
                                <!--                                       :style="{color: (item.timeQuantun && item.timeQuantun.length) > 0 ? '#00a5ec' : 'rgba(138,144,156,0.63)' }">-->
                                <!--                                        {{ ( item.timeQuantun && item.timeQuantun.length) > 0 ? '可预约' : '无可预约'}}</p>-->
                                <!--                                </div>-->
                            </div>
                            <div v-else>
                                <div class="list-item-name">
                                    {{ item.applyUserName }}
                                </div>
                                <div class="list">预约时间段</div>
                                <template v-if="item.arraysTimeQuantun">
                                    <div v-for="(arrtq,arrtqindex) in item.arraysTimeQuantun.split(';')"
                                         :key="arrtqindex">
                                        {{arrtq}}<br/>
                                    </div>
                                </template>
                                <div class="item_right_btn">
                                    <van-tag v-if="active===1" plain
                                             :color="setNewKeyVal(item.result)[1]">
                                        {{setNewKeyVal(item.result)[0]}}
                                    </van-tag>
                                </div>
                            </div>
                        </div>
                    </van-cell>
                </template>
            </list>
        </div>
        <van-popup v-model:show="show" round position="bottom">
            <van-date-picker v-model="Dates"
                             title="选择日期"
                             :min-date="minDate"
                             @cancel="show = false"
                             @confirm="changedata"/>
        </van-popup>

        <van-popup v-model:show="showSearchPopup" position="top" :style="{height:'auto',width:'100%'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <van-field label="预约人" v-model="searchApplyUserName" placeholder="请输入预约人"/>
                    <van-field label="申请状态" readonly label-align="top" rows="6" autosize type="textarea">
                        <template #input>
                            <div style="width: 100%;height: 100%;">
                                <van-button style="margin: 3px;width: 23%;float: left;" size="small"
                                            v-for="(category,index) in sytSysDictKeyVal" :key="index"
                                            hairline
                                            :plain="category.id===searchResult"
                                            :type="category.id===searchResult?'primary':'default'"
                                            @click="handleClik2(category)">{{category.name}}
                                </van-button>
                            </div>
                        </template>
                    </van-field>
                </van-cell-group>
            </div>
            <div class="van-submit-bar-search">
                <van-button type="primary" round size="small" @click="handleSearch"> 搜&nbsp;&nbsp;索
                </van-button>
            </div>
        </van-popup>

        <van-popup v-model:show="recourseShow" closeable position="bottom" @click-overlay="closeitem"
                   :style="{ height: '66%' }"
                   @click-close-icon="closeitem">
            <div class="popup_content">
                <div class="popup_content_title">
                    <p style="font-size: 16px;font-weight: bold;margin: 10px 0">{{Resouritem.name}}
                        <van-icon name="arrow"/>
                    </p>
                    <p style="line-height: 18px;margin: 0">{{Resouritem.fsss}}</p>
                    <p style="line-height: 18px;margin: 0 0 15px 0">容纳{{Resouritem.krnrs}}人</p>
                    <div class="mark-div">
                        <div class="mark-div-content">
                            <div class="mark-div-content-color"
                                 style="background: #f4f4f5;"></div>
                            <span class="mark-div-content-span">不可预约</span>
                        </div>
                        <div class="mark-div-content">
                            <div class="mark-div-content-color"
                                 style="background: #67c23a;"></div>
                            <span class="mark-div-content-span">可预约</span>
                        </div>
                        <div class="mark-div-content">
                            <div class="mark-div-content-color"
                                 style="background: #409eff;"></div>
                            <span class="mark-div-content-span">我的预约</span>
                        </div>
                        <div class="mark-div-content">
                            <div class="mark-div-content-color"
                                 style="background: #909399;"></div>
                            <span class="mark-div-content-span">他人预约</span>
                        </div>
                        <div class="mark-div-content">
                            <div class="mark-div-content-color"
                                 style="background: #e6a23c;"></div>
                            <span class="mark-div-content-span">当前所选</span>
                        </div>
                    </div>
                </div>
                <div class="popup_checktime">
                    <span class="chktime-span" @click="show = true"><van-icon
                            name="notes-o"/>{{Dates[0]+`年`+Dates[1]+`月`+Dates[2]+`日`}}<van-icon
                            name="arrow-down"/></span>
                    <van-button size="mini"
                                :style="currentChkMark==='next'?'background-color: rgba(242, 242, 242, 1);':''"
                                type="default" @click="changeday('next')">后一天
                        <van-icon name="arrow"/>
                    </van-button>
                    <van-button size="mini"
                                :style="currentChkMark==='current'?'background-color: rgba(242, 242, 242, 1);':''"
                                type="default" @click="changeday('current')">今天
                    </van-button>
                    <van-button size="mini"
                                :style="currentChkMark==='last'?'background-color: rgba(242, 242, 242, 1);':''"
                                type="default" @click="changeday('last')">
                        <van-icon name="arrow-left"/>
                        前一天
                    </van-button>
                </div>
                <div style="width: 100%;margin-top: 15px;">
                    <div class="tqm-div"
                         v-for="(tqm,index) in timeQuantunMark(Resouritem)"
                         :style="myApplyDateTime.indexOf(tqm.title)===-1?tqm.color:'background:#e6a23c;'"
                         :key="index"
                         @click="checkitem(tqm)">
                        <div class="tqm-div-time">
                            {{tqm.title}}
                        </div>
                    </div>
                </div>
                <div class="van-submit-bar">
                    <van-button round size="small" type="primary" @click="subInfo">预 约</van-button>
                </div>
            </div>
        </van-popup>
        <van-popup v-model:show="appointShow" closeable position="bottom"
                   :close-on-click-overlay="false"
                   :style="{ height: '66%' }">
            <div>
                <h2 style="text-align: center;">预约信息确认</h2>
                <van-cell-group>
                    <van-field label="预约资源" readonly :value="Resouritem.name"/>
                    <van-cell title="预约时间段（可申请人数/已申请人数）" readonly label-align="top" rows="6" autosize type="textarea">
                        <template #label>
                            <div v-for="item in myApplyDateTime" :key="item"
                                 style="font-size: 14px;color: #323233;line-height: 25px;">
                                <span>{{ currentDate+`/`+item }}  ( {{ Resouritem.ksqrs ? Resouritem.ksqrs : '0' }} / {{ Resouritem.applyDate ? Resouritem.applyDate.length : '0' }} )</span><br/>
                            </div>
                        </template>
                    </van-cell>
                    <van-field required label="预约事由" label-align="top" v-model="applyDate" type="textarea"
                               placeholder="请输入预约事由"
                               rows="4" autosize/>
                </van-cell-group>
                <div class="van-submit-bar">
                    <van-button @click="appointShow = false,myApplyDateTime=[]" round size="small"> 取消</van-button>
                    <van-button round size="small" type="primary" @click="subRecourse"> 提交</van-button>
                </div>
            </div>
        </van-popup>
        <van-popup v-model:show="detailLogShow" closeable position="bottom"
                   :close-on-click-overlay="false"
                   :style="{ height: '86%' }">
            <div>
                <h2 style="text-align: center;">预约记录查看</h2>
                <van-cell-group>
                    <van-field label="预&nbsp;&nbsp;约&nbsp;&nbsp;人" readonly
                               :model-value="Resouritem.applyUserName+'-'+Resouritem.applyUserCode"/>
                    <!--                    <van-field label="预约资源" readonly :value="Resouritem.name"/>-->
                    <van-field label="创建时间" readonly :model-value="dateFormat_YHMHM(Resouritem.createTime)"/>
                    <van-cell title="预约时段" readonly label-align="top" rows="6" autosize type="textarea">
                        <template #label>
                            <template v-if="Resouritem.arraysTimeQuantun">
                                <div v-for="item in Resouritem.arraysTimeQuantun.split(';')" :key="item"
                                     style="font-size: 14px;color: #323233;line-height: 25px;">
                                    <span>{{ item }}</span><br/>
                                </div>
                            </template>
                        </template>
                    </van-cell>
                    <van-field label="预约事由" label-align="top" v-model="Resouritem.applyReason"
                               type="textarea"
                               readonly
                               rows="4" autosize/>
                    <van-field label="预约状态" readonly :model-value="setNewKeyVal(Resouritem.result)[0]"/>
                </van-cell-group>
                <div class="van-submit-bar">
                    <van-button @click="detailLogShow = false,myApplyDateTime=[]" round size="small"> 关闭</van-button>
                    <van-button v-if="setNewKeyVal(Resouritem.result)[0] == '预约通过'" round size="small" type="primary" @click="showcancelApply = true;"> 取消预约 </van-button>
                </div>
            </div>
        </van-popup>
        <van-popup v-model:show="showcancelApply" round position="bottom">
            <h2 style="text-align: center;">取消预约</h2>
            <div style="margin-bottom: 60px;">
                <van-field
                    clickable
                    type="textarea"
                    row="5"
                    label="取消原因"
                    v-model="cancelApplytext"
                    placeholder="请输入取消原因"/>
            </div>

            <div class="van-submit-bar">
                <van-button @click="showcancelApply = false" round size="small"> 关闭</van-button>
                <van-button round size="small" type="primary" @click="handleCancel"> 确认 </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    import {useRoute,} from "vue-router";
    import {onMounted, reactive, toRefs, watch} from "vue";
    import {diyPost} from '@/service/home'
    import AjaxApi from "@/service/publicResource";
    import {showSuccessToast, showFailToast} from 'vant';
    import {ref} from 'vue'
    import Utils from "@/utils/momentWrap";
    import {getActiveDefinitionId} from "@/service/bpmProcessDefinition";
    import router from "@/router";
    import {getLocal} from '@/common/js/utils'
    import {mapState} from "vuex";
    import List from "@/components/List";

    export default {
        components: {List},
        computed: {
            ...mapState(["baseUrl", "defaultSearchIcon"]),
        },
        setup() {
            const route = useRoute()
            const state = reactive({
                url: AjaxApi.getApplyResour,
                queryObj: {},
                currentChkMark: 'current',
                currentId: null,
                resourceKey: null,
                resourceId: null,
                name: null,
                searchName: null,
                searchApplyUserName: null,
                cancelApplytext: null,
                cancelApplyid: '',
                searchResult: null,
                showSearchPopup: false,
                showcancelApply: false,
                recourseShow: false,
                appointShow: false,
                applyResour: null,
                Resouritem: null,
                show: false,
                myApplyDateTime: [],
                applyDate: '',
                currentDate: Utils.dateFormat_YMD(new Date()),
                Dates: ref(Utils.dateFormat_YMD(new Date()).split('-')),
                minDate: new Date(2020, 0, 1),
                active: 'yyzy',
                tabsOptions: [
                    {id: 'yyzy', text: '预约资源', value: 'yyzy'},
                    {id: 'yyjl', text: '预约记录', value: 'yyjl'},
                ],
                userInfo: null,
                sytSysDictKeyVal: [],
                detailLogShow: false,
            })
            onMounted(async () => {
                QueryEnumArray()
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                const {id, name, setNewOldActiveTab} = route.query
                if (id) {
                    const index = id.split('=')
                    state.resourceKey = index[0]
                    state.resourceId = index[1]
                    state.name = name
                    state.currentId = id
                }
                state.active = setNewOldActiveTab && setNewOldActiveTab === '预约记录' ? 1 : 0;
                setTabQueryParam()
            })
            watch(() => state.searchName, () => {
                setTabQueryParam()
            })
            const QueryEnumArray = () => {
                diyPost(AjaxApi.queryEnumArray,).then(res => {
                    if (res.status === 200) {
                        state.sytSysDictKeyVal = res.data
                    }
                })
            }

            const handleClik2 = (model) => {
                state.searchResult = model.id
            }
            const handleSearch = () => {
                setTabQueryParam()
                state.showSearchPopup = false;
            }
            const setNewKeyVal = (val) => {
                let newVal = []
                if (val && state.sytSysDictKeyVal) {
                    newVal = state.sytSysDictKeyVal.filter(item => {
                        return item["id"] === val.toString()
                    })
                }
                let newStr = newVal.length > 0 ? newVal[0].name : ''
                let tagColor = ""
                if (newStr) {
                    switch (newStr) {
                        case "审批中":
                            tagColor = "#1989fa"
                            break
                        case "预约通过":
                            tagColor = "#07c160"
                            break
                        case "处理中":
                            tagColor = "#31af90"
                            break
                        case "预约不通过":
                            tagColor = "#DD2C2C"
                            break
                        case "已取消":
                            tagColor = "#909090"
                            break
                    }
                }
                return [newStr, tagColor];
            }
            const dateFormat_YHMHM = (o) => {
                return Utils.dateFormat_YHMHM(o);
            }
            const goBack = () => {
                router.push({path: 'mine-service'})
            }
            const changeTab = (tab) => {
                state.active = Number(tab.name)
                setTabQueryParam()
            }
            const timeQuantunMark = (item) => {
                let timeQuantun = item.timeQuantun
                // let applyData = item.applyData
                let applyDateTime = item.applyDateTime
                let myApplyDateTime = item.myApplyDateTime
                let arr = [];
                //我的预约
                let wdyy = 'background: #409eff';
                //已占用
                let yzy = 'background: #909399';
                //可预约
                let kyy = 'background: #67c23a';
                //不可预约
                let bkyy = 'background: #f4f4f5';

                if (timeQuantun) {
                    timeQuantun.forEach(item => {
                        // let yyyChk = applyData && Object.keys(applyData).includes(state.currentDate + `/` + item)
                        let yyyChk = applyDateTime && applyDateTime.includes(state.currentDate + `/` + item)
                        let wdyyChk = myApplyDateTime && myApplyDateTime.includes(state.currentDate + `/` + item)
                        let sfkyyChk = new Date(state.currentDate + `/` + item.split('~')[1]).getTime() > new Date(new Date()).getTime()
                        if (yyyChk && !wdyyChk ) {
                            arr.push({title: item, color: yzy, readonly: true})
                        }
                        if (wdyyChk) {
                            arr.push({title: item, color: wdyy, readonly: true})
                        } else {
                            if(!yyyChk){
                                if (sfkyyChk) {
                                    arr.push({title: item, color: kyy, readonly: false})
                                } else {
                                    arr.push({title: item, color: bkyy, readonly: true})
                                }
                            }

                        }
                    })
                }
                console.log(`timeQuantunMark`,arr)
                return arr
            }

            const setTabQueryParam = () => {
                if (state.active === 1) {
                    state.url = AjaxApi.yyjlQueryPage
                    state.queryObj = {
                        queryParam: {
                            applyUserCode: state.userInfo.humanCode,
                            categoryId: state.resourceKey === 'groupBy' ? state.resourceId : '',
                        }
                    }
                    if (state.searchApplyUserName) state.queryObj.queryParam.applyUserName = state.searchApplyUserName
                    if (state.searchResult) state.queryObj.queryParam.result = state.searchResult
                } else {
                    state.url = AjaxApi.getApplyResour
                    state.queryObj = {
                        queryParam: {
                            // id: state.resourceKey === 'projectId' ? state.resourceId : '',
                            ksrq: state.currentDate,
                            jsrq: state.currentDate,
                            name: state.searchName ? state.searchName : '',
                        }
                    }
                    if (state.resourceKey === 'projectId') {
                        state.queryObj.queryParam.id = state.resourceId
                    } else if (state.resourceKey === 'groupBy') {
                        state.queryObj.queryParam.categoryId = state.resourceId
                    }
                }
            }
            const changedata = () => {
                let date = state.Dates.join('-')
                state.currentDate = date
                setTabQueryParam()
                state.show = false
                state.currentChkMark = '';
                state.myApplyDateTime = []
            }

            const changeday = (data) => {
                state.myApplyDateTime = []
                state.currentChkMark = data
                if (data == 'last') {
                    let day = -1
                    state.currentDate = Utils.dateFormat_YMD(new Date(state.currentDate).getTime() + day * 24 * 60 * 60 * 1000)
                    state.Dates = state.currentDate.split('-')
                }
                if (data == 'next') {
                    let day = 1
                    state.currentDate = Utils.dateFormat_YMD(new Date(state.currentDate).getTime() + day * 24 * 60 * 60 * 1000)
                    state.Dates = state.currentDate.split('-')
                }
                if (data == 'current') {
                    state.currentDate = Utils.dateFormat_YMD(new Date().getTime())
                    state.Dates = state.currentDate.split('-')
                }
                setTabQueryParam()
            }

            const closeitem = () => {
                state.searchName = ''
            }

            const checkitem = (item) => {
                if (!item.readonly) {
                    if (state.myApplyDateTime.indexOf(item.title) !== -1) {
                        state.myApplyDateTime.splice(state.myApplyDateTime.indexOf(item.title), 1)
                    } else {
                        state.myApplyDateTime.push(item.title)
                    }
                }
            }

            const handleCancel = () => {
                console.log(`state.cancelApplyid`,state.cancelApplyid)
                diyPost(AjaxApi.cancelApply, {"id": state.cancelApplyid, "reason": state.cancelApplytext}).then(res => {
                    // if (res.data.code !== "00000") {
                    //     showFailToast(res.data.info)
                    //     return false
                    // }
                    if (res.data.code === '00000') {
                        showFailToast('取消成功')
                        state.showcancelApply = false;
                        state.detailLogShow = false;
                        setTabQueryParam()
                        // this.$modal.msgSuccess( "取消成功");
                        // this.getInfo();
                    } else {
                        showFailToast("取消失败："+res.data.info)
                        // this.$modal.msgError("取消失败："+res.data.info);
                    }
                })

            }

            const subInfo = () => {
                if (state.myApplyDateTime.length > 0) {
                    let arrayState = []
                    state.myApplyDateTime.forEach(item => {
                        arrayState.push({projectId: state.Resouritem.id, applyDateTime: item, status: "是"})
                    })

                    diyPost(AjaxApi.queryState, {arrays: arrayState}).then(res => {
                        if (res.data.code !== "00000") {
                            showFailToast(res.data.info)
                            return false
                        }
                    })
                    state.appointShow = true
                } else {
                    showFailToast('请选择预约时段');
                }
            }

            const subRecourse = () => {
                if (!state.applyDate) {
                    showFailToast('请输入预约事由');
                    return false
                }
                if (state.Resouritem.sfsh == '是') {
                    getActiveDefinitionId({processDefinitionKey: state.Resouritem.processDefinitionKey}).then(res => {
                        if (res.data.code == "00000") {
                            const context = {
                                projectId: state.Resouritem.id,
                                processInstId: "",
                                applyReason: state.applyDate,
                                arrays: [],
                                processInstance: {
                                    processDefinitionId: null,
                                    variables: {},
                                    expressionVariables: {}
                                }
                            };
                            state.myApplyDateTime.forEach(item => {
                                context.arrays.push({
                                    projectId: state.Resouritem.id,
                                    applyId: "",
                                    dateday: state.currentDate,
                                    timeQuantun: item,
                                    applyDateTime: state.currentDate + `/` + item,
                                })
                            })
                            router.push({
                                path: '/mine-dealt-create',
                                query: {
                                    id: res.data.info,
                                    name: state.Resouritem.name,
                                    resourData: JSON.stringify(context),
                                    activeTab: '预约',
                                    backUrl: 'mine-service-recourse',
                                    backId: state.currentId,
                                    backName: state.name,
                                }
                            })
                        }
                    })
                } else {
                    handleSubmit()
                }
            }

            const handleSubmit = () => {
                const context = {
                    projectId: state.Resouritem.id,
                    processInstId: "",
                    applyReason: state.applyDate,
                    arrays: [],
                    processInstance: {
                        processDefinitionId: null,
                        variables: {},
                        expressionVariables: {}
                    }
                };
                state.myApplyDateTime.forEach(item => {
                    context.arrays.push({
                        projectId: state.Resouritem.id,
                        applyId: "",
                        dateday: state.currentDate,
                        timeQuantun: item,
                        applyDateTime: state.currentDate + `/` + item,
                    })
                })
                diyPost(AjaxApi.Edit, context).then(res => {
                    if (res.data.code == "00000") {
                        showSuccessToast("新增成功！");
                        state.recourseShow = false
                        state.appointShow = false
                        state.active = 1
                        setTabQueryParam()
                    } else {
                        showFailToast(res.data.info)
                    }
                })
            }
            const handleDetail = (item) => {
                state.Resouritem = item;
                state.searchName = item.name
                if (state.active === 0) {
                    state.recourseShow = true;
                } else {
                    if (item.processInstId) {
                        router.push({
                            path: '/mine-dealt-detail',
                            query: {
                                currentId: item.processInstId,
                                processDefinitionId: item.processDefinitionId,
                                activeType: '预约记录',
                                backUrl: 'mine-service-recourse',
                                backId: state.currentId,
                                backName: state.name,
                            }
                        });
                    } else {
                        //需求加详情弹框，显示申请信息
                        state.cancelApplyid=item.id
                        state.detailLogShow = true;
                    }
                }
            }

            return {
                ...toRefs(state),
                timeQuantunMark,
                handleDetail,
                handleSubmit,
                changedata,
                changeday,
                closeitem,
                checkitem,
                subInfo,
                subRecourse,
                changeTab,
                goBack,
                dateFormat_YHMHM,
                setNewKeyVal,
                handleClik2,
                handleSearch,
                handleCancel
            }
        }
    }
</script>

<style lang="less">
    @import '../common/style/mixin';

    .cell-class-name {
        color: #2c3e50;
        text-align: left;
    }

    .content {
        height: calc(~"(100vh - 90px)");
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 140px;
        margin-bottom: 76px;
    }

    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }

    .tqm-div {
        display: inline-block;
        width: 46%;
        margin-right: 10px;
        margin-bottom: 6px;
    }

    .tqm-div-time {
        width: 100%;
        height: 30px;
        border-radius: 5px;
        line-height: 30px;
        text-align: center;
        /*color: #868686;*/
    }

    .van-button--default {
        border: var(--van-button-border-width) solid var(--van-button-default-border-color) !important;
    }

    .mark-div {
        display: flex;
        justify-content: space-around;
        width: 100%;
        margin-top: 15px;
        margin-bottom: 10px;
        .mark-div-content {
            display: inline-block;
            /*width: 100%;*/

            .mark-div-content-color {
                height: 15px;
                width: 15px;
                display: inline-block;
                border-radius: 2px;
                vertical-align: middle;
            }

            .mark-div-content-span {
                vertical-align: middle;
            }
        }
    }

    .product-list-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }

    .checktime {
        margin: 15px;

        .chktime-span {
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
        }

        span {
            vertical-align: -webkit-baseline-middle;
        }

        .van-button {
            float: right;
            border: 1px solid #dcdee0 !important;
            margin-left: 5px;
        }
    }

    .contents_box_timeQuantunMark {
        width: 15px;
        height: 15px;
        display: inline-block;
        margin-right: 4px
    }

    .van-tag--plain::before {
        border: unset !important;
    }

    .popup_content {
        padding: 15px;

        .popup_content_title {
            border-bottom: 1px solid rgba(138, 144, 156, 0.37);
        }

        .popup_checktime {
            margin-top: 10px;

            .chktime-span {
                font-size: 14px;
                font-weight: bold;
                margin: 10px 0;
            }

            span {
                vertical-align: -webkit-baseline-middle;
            }

            .van-button {
                float: right;
                border: 1px solid #dcdee0 !important;
                margin-left: 5px;
            }
        }
    }


</style>

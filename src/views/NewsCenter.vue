<template>
    <section class="wrap">
<!--        <div class="top">-->
<!--            <van-search shape="round" placeholder="全部新闻"/>-->
<!--        </div>-->

        <div class="product-list-content">
            <header class="category-header wrap">
                <i class="nbicon nbfanhui" @click="goBack"></i>
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
            </header>
        </div>
        <div class="newWrap">
<!--            <van-icon style="top:20px;" class="lookMore" name="arrow"/>-->
            <van-tabs v-model="active" animated color="#2d8eed" title-active-color="#2d8eed" line-width="60px">
                <van-tab title="新闻资讯">
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newBlock">最新</span>
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                    s
                </van-tab>
                <van-tab title="新闻资讯">
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newBlock">最新</span>
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newBlock">最新</span>
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                </van-tab>
                <van-tab title="新闻资讯">
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newBlock">最新</span>
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                        <div class="newImgWrap">
                            <img class="newImg" src="../assets/newimg.png" alt="">
                        </div>
                    </van-row>
                    <van-row class="newRow">
                        <div class="titleWrap">
                            <p class="newTitle1">党员、干部初心变没变、使命记得牢不牢，要由群众来评价，由时间来评定</p>
                            <div class="newElse">
                                <span class="newBlock">最新</span>
                                <span class="newFrom">校新闻网</span>
                                <span>2020-05-29</span>
                            </div>
                        </div>
                    </van-row>
                </van-tab>
                <van-tab title="通知公告">
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                </van-tab>
                <van-tab title="学校发文">
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                    <div class="new">
                        <p class="newTitle"><span class="newTag"></span>市教育局工作组来学院检查校园安全工作</p>
                        <div class="date"><span class="newBlock">最新</span><span class="newFrom">校新闻网</span>2020-12-28
                        </div>
                    </div>
                </van-tab>
            </van-tabs>
        </div>
    </section>
</template>

<script>
    import {useRoute, useRouter} from "vue-router";
    import {onMounted, reactive, toRefs} from "vue";

    export default {
        setup() {
            const router = useRouter()
            const route = useRoute()
            const state = reactive({
                searchName: null,
                queryObj: {},
            })
            onMounted(async () => {
                const {currentType} = route.query
                state.queryObj = {
                    queryParam: {
                        type: currentType
                    }
                }
            })

            const goBack = () => {
                router.go(-1)
            }

            return {
                ...toRefs(state),
                goBack,
            }
        },
        data() {
            return {
                active: 0,
            };
        },
    };
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';

    .product-list-content {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }

    .wrap {
        width: 100%;
        height: 100vh;
        overflow-y: scroll;
        background: #f5f5f5;
    }

    /*.top {*/
    /*    width: 100%;*/
    /*    background: #fff;*/
    /*}*/

    /*.top >>> .van-search__content--round {*/
    /*    border: 1px solid #eee;*/
    /*}*/

    .newWrap {

        height: 100%;
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 50px;
        background: #f7f8fa;
        /*margin-top: 8px;*/
        /*background: #fff;*/
        /*padding: 5px 10px;*/
        /*position: relative;*/
    }

    .lookMore {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 16px;
        color: #999;
        z-index: 1000;
    }

    .new {
        width: 100%;
        height: 45px;
        margin-bottom: 13px;
    }

    .newTitle {
        font-size: 14px;
        color: #333;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .newTag {
        float: left;
        width: 5px;
        height: 5px;
        background: #72bcfb;
        border-radius: 50%;
        margin-top: 7.5px;
        margin-right: 5px;
    }

    .date {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
        padding-left: 10px;
    }

    /*.newWrap >>> .van-tabs--line .van-tabs__wrap {*/
    /*    height: 40px;*/
    /*    padding-left: 5px;*/
    /*    padding-right: 25px;*/
    /*}*/

    /*.newWrap >>> .van-hairline--top-bottom::after {*/
    /*    border: none;*/
    /*}*/

    /*.newWrap >>> .van-tab__pane-wrapper {*/
    /*    background: #fff;*/
    /*    padding: 13px 5px;*/
    /*    padding-bottom: 5px;*/
    /*}*/

    /*.newWrap >>> .van-tab {*/
    /*    width: 60px;*/
    /*    flex: none;*/
    /*    text-align: left;*/
    /*    font-family: "微软雅黑";*/
    /*    padding: 0;*/
    /*    font-size: 15px;*/
    /*    margin-right: 20px;*/
    /*    flex-basis: 60px !important;*/
    /*}*/

    /*.newWrap >>> .van-tab--active {*/
    /*    font-weight: bold;*/
    /*}*/


    .van-row {
        padding: 0;
    }

    .newRow {
        display: flex;
        padding-top: 3px;
        margin-bottom: 10px;
    }

    .newRow .titleWrap {
        flex-grow: 5;
    }

    .newRow .newImgWrap {
        flex-grow: 1;
        padding: 4px;
        padding-right: 0;
    }

    .newTitle1 {
        font-size: 14px;
        line-height: 22px;
        color: #333;
        display: block;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 5px;
    }

    .newElse {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
    }

    .newBlock {
        display: inline-block;
        color: #da3b35;
        width: 30px;
        height: 15px;
        line-height: 14px;
        border: 1px solid #da3b35;
        text-align: center;
        border-radius: 2px;
        margin-right: 10px;
    }

    .newFrom {
        font-size: 14px;
        margin-right: 10px;
    }

    .van-col--8 {
        padding-left: 10px;
    }

    .newImg {
        width: 100px;
        height: 100%;
        border-radius: 5px;
    }

</style>

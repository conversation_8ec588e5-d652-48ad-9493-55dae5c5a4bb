<template>
    <div v-if="!isnew">
        <div class="good">
            <header class="good-header"></header>
            <div class="applyServiceTitle">
                <span style="float: left;">{{item.data.name}}</span>
                <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis"/>
                <van-tabs style="width: 61%;float: left;" v-model:active="active" color="#48a3ea"
                          title-active-color="#48a3ea"
                          @click-tab="changeTab">
                    <van-tab v-for="tab in item.data.tabs" :key="tab.id" :title="tab.name">
                    </van-tab>
                </van-tabs>
            </div>
        </div>
        <div v-if="applist && applist.length > 0" class="newContent" style="display: block;padding: 5px 10px;">
            <div class="mineDesktopDataWrap" v-for="news in applist" :key="news.id">
                <div class="mineDesktopData">
                    <!-- <img class="mineDesktopDataImg" :src="news.icon" :alt="news.title" /> -->
                    <p class="mineDesktopDataNumber">{{ news.content }}</p>
                    <div class="mineDesktopDataTitle">{{ news.title }}</div>
                </div>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
        </div>
        <div v-if="applist && applist.length > 0" class="newContent" style="display: block;padding: 5px 10px;">
            <div class="mineDesktopDataWrap" v-for="news in applist" :key="news.id">
                <div class="mineDesktopData">
                    <!-- <img class="mineDesktopDataImg" :src="news.icon" :alt="news.title" /> -->
                    <p class="mineDesktopDataNumber">{{ news.content }}</p>
                    <div class="mineDesktopDataTitle">{{ news.title }}</div>
                </div>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
</template>

<script>

    import {GetUserData} from "@/service/portal";
    import {onMounted, reactive, toRefs,} from "vue";
    import {getLocal} from '@/common/js/utils'

    export default {
        name: "wodeshuju",
        props: [
            'item',
            'isnew'
        ],
        setup(props) {
            const state = reactive({
                moreUrl: "",
                userInfo: null,
                applist: [],
                adress: require("../../assets/ksj.png"),
                icon: 'icon-template',
                active: 0,
            })

            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                let url = "";
                console.log(props.item.data)
                if (props.item.data) {
                    userDataTabs(0);
                }
                // if (props.item.tabs && props.item.tabs.length > 0) {
                //     url = props.item.tabs[key - 1].dataSources;
                //     state.moreUrl = props.item.tabs[key - 1].moreUrl;
                // } else {
                //     props.item.tabs.push({
                //         name: "",
                //         type: "",
                //     });
                //     if (props.item.dataSources && props.item.dataSources !== '') {
                //         url = props.item.dataSources;
                //         state.moreUrl = props.item.moreUrl;
                //     }
                // }
                console.log(url)
            })
            // watch(() => props.item.data, (newVal) => {
            //     // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
            //     if (newVal) load();
            // })
            const userDataTabs = (key) => {
                let item = props.item.data
                GetUserData({
                    categoryId: item.tabs[key].categoryId,
                    limit: item.limit
                }).then(res => {
                    if (res.data.code === "00000") {
                        state.applist = res.data.info
                    }
                });
            }

            const changeTab = (tab) => {
                userDataTabs(Number(tab.name))

            }
            const showDetail = (app) => {
                // window.open(app.redirectUrl, '_blank')
                window.location.href = app.redirectUrl

            }
            const ellipsis = () => {
                if (state.moreUrl && state.moreUrl !== '') {
                    if (state.moreUrl.indexOf("http" !== -1)) {
                        if (state.userInfo) {
                            let openUrl = state.moreUrl
                            // window.open(openUrl, '_self')
                            window.location.href = openUrl
                        }
                    } else {
                        let routeData = state.$router.resolve({path: state.moreUrl});
                        // window.open(routeData.href, '_blank');
                        window.location.href = routeData.href
                    }
                }
            }
            return {
                ...toRefs(state),
                showDetail,
                ellipsis,
                userDataTabs,
                changeTab,
            }
        },
        // data() {
        //     return {
        //         moreUrl: "",
        //     };
        // },
        // created() {
        //     let url = "";
        //     if (item.tabs && item.tabs.length > 0) {
        //         url = item.tabs[key - 1].dataSources;
        //         this.moreUrl = item.tabs[key - 1].moreUrl;
        //     } else {
        //         item.tabs.push({
        //             name: "",
        //             type: "",
        //         });
        //         if (item.dataSources && item.dataSources !== '') {
        //             url = item.dataSources;
        //             this.moreUrl = item.moreUrl;
        //         }
        //     }
        // },
        // methods: {
        //     ellipsis() {
        //         if (this.moreUrl && this.moreUrl !== '') {
        //             if (this.moreUrl.indexOf("http" !== -1)) {
        //                 let userInfo = getStore({name: "user-info"});
        //                 if (userInfo) {
        //                     let openUrl = this.moreUrl.replace("#{humancode}", userInfo.humancode);
        //                     window.open(openUrl, '_blank')
        //                 }
        //             } else {
        //                 let routeData = this.$router.resolve({path: this.moreUrl});
        //                 window.open(routeData.href, '_blank');
        //             }
        //         }
        //     },
        //     userDataTabs(key, item) {
        //         this.obj.userData = [];
        //         GetUserData({
        //             categoryId: item.tabs[key - 1].categoryId,
        //             limit: item.limit
        //         }).then(res => {
        //             this.$store.commit("SET_LOADING", {name: "userData", flag: false});
        //             this.obj.userData = res.data.info;
        //         });
        //     },
        // },
        // watch: {},
        // computed: {
        //     ...mapGetters(["isPortalLoading", "isEmpty"]),
        // }
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';

    .small-newWrapTitle {
        display: none;
    }

    .clearfix:before,
    .clearfix:after {
        content: "";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }

    .clearfix {
        *zoom: 1;
    }

    @media screen and (max-width: 1366px) {

        .newContent {
            min-height: 190px;
            height: auto;
        }


    }
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
</style>

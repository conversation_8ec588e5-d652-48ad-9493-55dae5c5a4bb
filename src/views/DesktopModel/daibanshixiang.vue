<template>

    <div v-if="!isnew">
        <div>
            <div class="good">
                <header class="good-header"></header>
                <p class="applyServiceTitle">{{item.data.name}}
                    <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis"/>
                </p>
            </div>
        </div>
        <div v-if="list && list.length > 0" class="newContent4">
            <ul class="newUl">
                <li v-for="(items,index) in list" :key="index">
                    <div class="point" @click="showNewsDetail(items)">
                            <span class="newLiTitle" :title="items.title" :style="{color: items.isread ? '#33333369' : '#333', }">
                                <van-tag type="warning"
                                         size="mini">{{items.type}}</van-tag>{{ items.startUserNickname }}提交了{{items.title}}</span>
                        <span class="newLiTime">{{ items.createDate }}</span>
                    </div>
                </li>
            </ul>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
            <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis"/>
        </div>
        <div v-if="list && list.length > 0" class="newContent4">
            <ul class="newUl">
                <li v-for="(items,index) in list" :key="index">
                    <div class="point" @click="showNewsDetail(items)">
                            <span class="newLiTitle" :title="items.title" :style="{color: items.isread ? '#33333369' : '#333', }">
                                <van-tag type="warning"
                                         size="mini">{{items.type}}</van-tag>{{ items.startUserNickname }}提交了{{items.title}}</span>
                        <span class="newLiTime">{{ items.createDate }}</span>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
    import axios from '@/utils/axios'
    import {getTodoTaskPage} from "@/service/bpmTask";
    import Utils from "@/utils/momentWrap";
    import {useRouter} from "vue-router";
    import {onMounted, reactive, toRefs} from "vue";
    import {getLocal} from '@/common/js/utils'
    import {GetReadList,SetReadList} from "@/service/portal";

    export default {
        name: "daibanshixiang",
        props: [
            'item',
            'isnew'
        ],
        setup(props,) {
            const router = useRouter()
            const state = reactive({
                list: [],
                num: 0,
                moreUrl: "",
                readlist: [],
                adress: require("../../assets/ksj.png"),
                userInfo: null,
            })
            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                await GetReadList(
                    {
                        "page": 1,
                        "pageSize": 20,
                        "queryParam": {
                            "type":"校内通知"
                        }
                    }
                ).then(res => {
                    if(res.data.code === "00000"){
                        state.readlist = res.data.info.records;
                    }
                });
                if (props.item.data) {
                    load(1, props.item.data);
                    window.setInterval(() => {
                        load(1, props.item.data);
                    }, 300000)
                }
            })

            const load = (key, item) => {
                let url = "";
                if (item.dataSources && item.dataSources !== '') {
                    // url = item.dataSources + "/" + (item.tabs && item.tabs.length > 0 ? item.tabs[key - 1].value : "");
                    url = item.dataSources;
                }
                //配置的数据来源
                if (url && url !== '') {
                    axios.post(
                        url + (url.indexOf('?') != -1 ? '&' : '?') + `page=1&pageSize=` + item.limit + '&Content-Type=application/x-www-form-urlencoded',
                        {'Content-Type': 'application/x-www-form-urlencoded'},
                        {}
                    ).then(res => {
                        if (res.data.code === 200) {
                            if (Object.prototype.hasOwnProperty.call(res.data.data, "list")) {
                                const data = res.data.data.list;
                                let listData = [];
                                data.forEach(d => {
                                    if (Object.prototype.hasOwnProperty.call(d, "processInstance")) {
                                        d.startUserNickname = d.processInstance.startUserNickname;
                                    }
                                    listData.push(d);
                                });
                                state.list = listData;
                                state.list.forEach(item=>{
                                    state.readlist.forEach(read=>{
                                        if( item.title === read.key){
                                            item.isread = true
                                        }
                                    })
                                })
                                state.num = res.data.data.total;
                            } else {
                                let type = item.tabs[key - 1].value.split('?');
                                state.list = res.data.data[type[0]];
                                state.list.forEach(item=>{
                                    state.readlist.forEach(read=>{
                                        if( item.title === read.key){
                                            item.isread = true
                                        }
                                    })
                                })

                            }
                        }
                    });
                } else {  //系统接口
                    getTodoTaskPage({
                        page: 1,
                        pageSize: item.limit,
                    }).then(res => {
                        // console.log(`todolist`,res)
                        const data = res.data.info.records;
                        let listData = [];
                        if (data) data.forEach(d => {
                            d.createDate = Utils.formatTime(new Date(d.createTime), '{y}-{m}-{d}');
                            d.title = d.processInstance.name;
                            d.startUserNickname = d.processInstance.startUserNickname;
                            // d.url = '';
                            d.type = '流程待办';
                            listData.push(d);
                        });
                        state.list = listData;
                        state.list.forEach(item=>{
                            state.readlist.forEach(read=>{
                                if( item.title === read.key){
                                    item.isread = true
                                }
                            })
                        })
                        // console.log(`list`, state.list)
                        // console.log(this.$router)
                        state.num = res.data.info.total;
                    });
                }
            }

            const showNewsDetail = (item) => {
                let data = {
                    "key": item.title,
                    "type":"校内通知"
                }
                SetReadList(data).then(()=>{})
                if (item.url && item.url !== '') {
                    if (state.userInfo) {
                        let openUrl = item.url.replace("#{humancode}", state.userInfo.humanCode) + item.id;
                        // window.open(openUrl, '_blank')
                        window.location.href = openUrl
                    }
                } else {
                    router.push({
                        path: '/mine-dealt-detail',
                        query: {
                            currentId: item.processInstance.id,
                            processDefinitionId: item.processInstance.processDefinitionId,
                            activeTab: 0
                        }
                    });
                }
            }

            const ellipsis = () => {
                console.log(state.moreUrl)
                if (state.moreUrl && state.moreUrl !== '') {
                    if (state.moreUrl.indexOf("http" !== -1)) {
                        if (state.userInfo) {
                            let openUrl = state.moreUrl;
                            // window.open(, '_self')
                            window.location.href =openUrl
                        }
                    } else {
                        router.push({
                            path: '/mine-dealt',
                            query:{isNavbar: true}
                        });
                    }
                } else {
                    router.push({
                        path: '/mine-dealt',
                        query:{isNavbar: true}
                    });
                }
            }
            return {
                ...toRefs(state),
                showNewsDetail, ellipsis
            }
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';

    .newContent4 {
        display: flex;
        flex-shrink: 0;
        flex-wrap: wrap;
    }

    .newContent1 {
        padding: 5px 10px;
    }

    .newUl {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .newUl li {
        /*width: 100%;*/
        height: 29px;
        line-height: 29px;
        padding: 0 10px;
    }

    .newUl li a {
        color: #666;
    }

    .newLiTitle {
        float: left;
        width: calc(80% - 12px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 14px;
    }

    .newLiTitle .van-tag {
        margin-right: 5px;
        padding-top: 2px!important;
    }

    .newLiTime {
        float: right;
        width: 80px;
        text-align: right;
        font-size: 14px;
        color: #999;
    }
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
</style>

<template>
    <div class="product-list-wrap">
        <div class="product-list-content">
            <header class="category-header wrap">
                <i class="nbicon nbfanhui" @click="goBack"></i>
                <span style="width: 110px;padding: 0 6px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;">{{title}}</span>
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           @click="showSearchPopup=true"/>
                </div>
            </header>
        </div>
        <div class="content">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      @load="onLoad">
                <van-cell v-for="item in list" :key="item">
                    <div v-for="optc in optColumn" :key="optc.prop">
                        <div style="display: flex;justify-content: space-between;" class="cell-class-name">
                            <div class="list-item-info">{{optc.label}}：{{item[optc.prop]}}</div>
                        </div>
                    </div>
                </van-cell>
            </van-list>
        </div>
<!--        <nav-bar/>-->
        <van-popup v-model:show="showSearchPopup" position="top" :style="{height:'auto',width:'100%'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <div v-for="optc in optColumn" :key="optc.prop">
                        <div v-if="optc.search">
                            <van-field :label="optc.label" readonly/>
                            <van-field label="" :placeholder="'请输入'+optc.label" v-model="searchObj[optc.prop]"/>
                        </div>
                    </div>
                </van-cell-group>
            </div>
            <div class="van-submit-bar">
                <van-button type="primary" round size="small" @click="handleSearch"> 搜&nbsp;&nbsp;索
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    // import navBar from '@/components/NavBar'
    import {getDataByUrl} from "@/service/portal";
    import {useRoute, useRouter} from "vue-router";
    import {onMounted, reactive, toRefs,} from "vue";
    import {getLocal} from '@/common/js/utils'

    export default {
        components: {
            // navBar,
        },
        setup() {
            const router = useRouter()
            const route = useRoute()
            const state = reactive({
                loading: false,
                finished: false,
                list: null,
                data: [],
                optColumn: [],
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10
                },
                userInfo: null,
                url: null,
                title: null,
                showSearchPopup: false,
                searchObj: {},
            })

            onMounted(async () => {
                const {title, detailUrl} = route.query
                state.title = title
                state.url = detailUrl
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
            })
            const pageParam = () => {
                return {
                    page: state.page.currentPage,
                    pageSize: state.page.pageSize,
                    code: state.userInfo.humanCode
                };
            }
            const handleSearch = () => {
                state.page.currentPage = 1;
                state.showSearchPopup = false;
                onLoad()
            }
            const objectToQueryString = (obj) => {
                const params = new URLSearchParams(obj);
                return params.toString();
            }
            const onLoad = () => {
                let param = pageParam()
                let newObj = Object.assign(
                    param,
                    state.searchObj
                );
                let str = objectToQueryString(newObj)
                getDataByUrl(state.url + "?" + str).then(res => {
                    state.optColumn = res.data.data.columns;
                    if (param.page === 1) {
                        state.list = res.data.data.list
                    } else {
                        state.list = state.list.concat(res.data.data.list)
                    }
                    state.loading = false;
                    state.page.currentPage++
                    state.finished = (Math.floor(res.data.data.total/state.page.pageSize) == res.data.data.total/state.page.pageSize) ? res.data.data.total/state.page.pageSize == param.page : Math.floor(res.data.data.total/state.page.pageSize) +1  == param.page
                    // state.finished = true
                }).catch(()=>{
                    // state.list = [];
                    state.loading = false;
                    state.finished = true;
                });

            };
            const goBack = () => {
                router.go(-1)
            }
            return {
                ...toRefs(state),
                onLoad,
                goBack,
                handleSearch,
            }
        },
        computed: {},
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';

    .cell-class-name {
        color: #2c3e50;
        text-align: left;
    }

    .product-list-content {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }

    .content {
        height: calc(~"(100vh - 90px)");
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 52px;
        margin-bottom: 56px;
    }

    .cell-class-name {
        color: #2c3e50;
        text-align: left;
    }

    /*.van-tag--plain::before {*/
    /*    border: unset !important;*/
    /*}*/


    .van-submit-bar {
        bottom: unset;
        left: unset;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 88%!important;
        /*font-size: 16px;*/
        /*left: 18px;*/
        /*bottom: 6px;*/
    }

</style>

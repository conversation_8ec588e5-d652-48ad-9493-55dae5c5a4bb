<template>
    <div>
        <s-header :name="title"></s-header>
        <div class="category-list">
            <template v-if="type === 'jichengxitong'">
                <div v-for="(item, index) in serviceData" :key="index"
                     @click="showDetailsys(item.id)">
                    <img :src="item.clientIcon?(baseUrl+item.clientIcon):defaultIcon" :onerror="defaultIcon">
                    <span>{{item.clientName.length >5?item.clientName.substring(0,5)+"..":item.clientName}}</span>
                </div>
            </template>
            <template v-if="type === 'yingyongPT'">
                <div v-for="(item, index) in serviceData" :key="index"
                     @click="showDetail(item)">
                    <img :src="item.imgUrl.url?(baseUrl+item.imgUrl.url):defaultIcon" :onerror="defaultIcon">
                    <span>{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</span>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
    import sHeader from '@/components/SimpleHeader'
    import {onMounted, reactive, toRefs} from "vue";
    import {GetSystem, ToService,} from "@/service/portal";
    import {GetAppletList} from "@/service/applet";
    import {useRoute} from "vue-router";
    import {mapState} from "vuex";

    export default {
        components: {
            sHeader
        },
        computed: {
            ...mapState(["baseUrl"])
        },
        setup() {
            const route = useRoute()
            const state = reactive({
                title: null,
                type: null,
                defaultIcon: require("../../../assets/isnull.png"),
                serviceData: null,
            })
            onMounted(async () => {
                const {title, type} = route.query
                state.title = title
                state.type = type
                if (type === 'jichengxitong') getSystem()
                if (type === 'yingyongPT') getAppletList()
            })

            const getSystem = () => {
                GetSystem({
                    limit: 99999
                }).then(res => {
                    if (res.data.code === "00000") {
                        state.serviceData = res.data.info;
                    }
                });
            }
            const getAppletList = () => {
                GetAppletList({
                    queryParam: {type: "应用平台"},
                    page: 1,
                    pageSize: 99999
                }).then(res => {
                    if (res.data.code === "00000") {
                        state.serviceData = res.data.info.records;
                    }
                });
            }
            const showDetailsys = (id) => {
                // let title = "系统";
                let context = {
                    id: id,
                    type: "sys"
                };
                // context.type = "sys";
                // if (type == "sys") {
                //     title = "系统";
                //     context.type = "sys";
                // } else if (type == "service") {
                //     title = "服务";
                // }
                ToService(context).then(res => {
                    if (res.data.code == "00000") {
                        // window.open(res.data.info, '_blank');
                        window.location.href = res.data.info

                    } else {
                        // this.$modal.msgWarning(res.data.info);
                    }
                });
                // window.open(app.redirectUrl, '_blank')
            }
            const showDetail = (app) => {
                // window.open(app.redirectUrl, '_blank')

                window.location.href = app.redirectUrl
            }
            return {
                ...toRefs(state),
                showDetail,
                showDetailsys
            }
        },
    };
</script>

<style lang="less" scoped>
    @import 'src/common/style/mixin';
    @import 'src/common/style/home';

</style>

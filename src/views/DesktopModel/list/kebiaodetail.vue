<template>
      <div>
          <s-header :name="title"></s-header>
          <div class="timetable w100 h100">
              <div class="time-b w100">
                  <div class="title">课程表</div>
                  <div class="time-detail">
<!--                      pc端展示的数据-->
<!--                      {{currentSchoolCalendar.term_num}}&nbsp;&nbsp;&nbsp;-->
<!--                      第 <b>{{currentSchoolCalendar.current_week}}</b> 教学周-->
<!--                      {{nowDate}}&nbsp;&nbsp;&nbsp;-->
<!--                      {{xq}}-->
                      第 <b>{{ currentSchoolCalendar.current_week }}</b> 教学周
                  </div>
                  <div class="time-controller">

                  </div>
              </div>
              <div class="timetable-b w100">
                  <table class="timetable-content w100">
                      <thead>
                          <tr>
                              <th></th>
                              <th v-for="(item1, index1) in 7" :key="index1">
                                  <!--            {{item1}}-->
                                  <!--            {{getNextDate(today,item1 - topdayweek).split('-')[1].replace(/\b(0+)/gi,"")+`-`+today.split('-')[2].replace(/\b(0+)/gi,"")}}-->
                                  <p style="font-weight: normal;color: #8a909c">
                                      {{getNextDate(today,item1 - topdayweek).split('-')[1].replace(/\b(0+)/gi,"")+`-`+getNextDate(today,item1 - topdayweek).split('-')[2].replace(/\b(0+)/gi,"")}}
                                  </p>
                                  <p>
                                      {{ "周" + numberToChinease(item1, "week") }}
                                  </p>
                              </th>
                          </tr>
                      </thead>
                      <tbody>
                      <tr v-for="(item2, index2) in maxCourseLength" :key="index2">
                          <td style="font-size: 14px;font-weight:bold;background-color: #fcfcfc;">
                              <p>{{ item2 }}</p>
                          </td>
                            <template v-for="(item3, index3) in weeks">
                              <td
                                  :key="item3"
                                  :rowspan="showData(index3, index2).skcd"
                                  v-if="showData(index3, index2).index == showData(index3, index2).ksjc"
                              >
                                <div
                                    class="dmsjandjs-b"
                                    :style="[
                                    {
                                      background: showData(index3, index2).kcmc ? '#38A1E5' : '#fff',
                                    },
                                    { color: '#fff' },
                                    { borderRadius: '5px' },

                                    { height: '100%' },
                                  ]"
                                >
                                      <p v-if="showData(index3, index2)" @click="showPopup(showData(index3, index2))">
                                        {{ showData(index3, index2).kcmc }}
                                      </p>
                                </div>
                              </td>
                            </template>
                      </tr>
                      </tbody>
                  </table>
              </div>
          </div>
          <van-dialog v-model:show="show" title="" :show-confirm-button="false" cancel-button-text="关闭" show-cancel-button>
              <p style="text-align: center;font-weight: bold">{{ popupcontent.kcmc }}</p>
              <div style="padding: 5px 10px">
                  <span v-if="showData(index3, index2).qsz">
                      课程时间:  {{ showData(index3, index2).qsz }}-{{ showData(index3, index2).zzz }}周
                  </span> <br>
                  教师名称:  {{ popupcontent.jsxm }}<br>
<!--                  课程时间:  {{ popupcontent.sksj }}<br>-->
                  教学地点:  {{ popupcontent.jxddmc }}<br>
                  <span v-if="showData(index3, index2).ksjc">
                      课程时间:  第{{ item.weekNumber }}周 星期{{ item.xqj }} 第{{ item.ksjc }}到{{ item.jsjc }}节
                  </span> <br>
                  <span v-if="popupcontent.skbj && popupcontent.skbj !== ''">教学班级:  {{ popupcontent.skbj }}</span>
              </div>
          </van-dialog>
      </div>
</template>

<script>
import {mapState} from "vuex";
import sHeader from '@/components/SimpleHeader'
import { weekCourse, colorList } from "./timetable";
import Utils from "@/utils/momentWrap";

// import {GetAppletList} from "@/service/applet";
import {onMounted, reactive, toRefs} from "vue";
import {getCurrentSchoolCalendar} from "@/service/applet";
import {getDataByUrl} from "@/service/portal";
import {useRoute} from "vue-router";
// import {useRouter} from "vue-router";

// var resdata = {
//   "maxJc": 14,
//   "list": [
//       {
//           "xn": "2022-2023",
//           "xq": "2",
//           "xqj": "5",
//           "humancode": "2022020642",
//           "qsz": "1",
//           "zzz": "8",
//           "sksj": "1-8周 星期五[10-13节]",
//           "kcmc": "嵌入式系统及应用",
//           "jxddmc": "2-2-125(原109)",
//           "skcd": "4",
//           "ksjc": "10",
//           "jsjc": "13",
//           "jsxm": "王超",
//           "skbj": "嵌入式系统及应用01",
//           "rowId": 7
//       },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "1",
//       "humancode": "2022020642",
//       "qsz": "1",
//       "zzz": "8",
//       "sksj": "1-8周 星期一[10-13节]",
//       "kcmc": "机器学习理论与方法",
//       "jxddmc": "2-1-二阶梯",
//       "skcd": "4",
//       "ksjc": "10",
//       "jsjc": "13",
//       "jsxm": "朱敏玲,苗军",
//       "skbj": "机器学习理论与方法01",
//       "rowId": 1
//     },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "2",
//       "humancode": "2022020642",
//       "qsz": "1",
//       "zzz": "17",
//       "sksj": "1-17周 星期二[3-4节]",
//       "kcmc": "英语（二）",
//       "jxddmc": "2-2-125(原109)",
//       "skcd": "2",
//       "ksjc": "3",
//       "jsjc": "4",
//       "jsxm": "彭鹭鹭",
//       "skbj": "22级研EA2",
//       "rowId": 2
//     },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "2",
//       "humancode": "2022020642",
//       "qsz": "6",
//       "zzz": "9",
//       "sksj": "6-9周 星期二[6-9节]",
//       "kcmc": "自然辩证法概论",
//       "jxddmc": "2-1-三阶梯",
//       "skcd": "4",
//       "ksjc": "6",
//       "jsjc": "9",
//       "jsxm": "韩剑英",
//       "skbj": "自然辩证法概论08",
//       "rowId": 3
//     },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "4",
//       "humancode": "2022020642",
//       "qsz": "10",
//       "zzz": "17",
//       "sksj": "10-17周 星期四[1-4节]",
//       "kcmc": "专业实践",
//       "jxddmc": "2-1-二阶梯",
//       "skcd": "4",
//       "ksjc": "1",
//       "jsjc": "4",
//       "jsxm": "侯霞,刘秀磊,崔展齐",
//       "skbj": "专业实践01",
//       "rowId": 4
//     },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "4",
//       "humancode": "2022020642",
//       "qsz": "10",
//       "zzz": "17",
//       "sksj": "10-17周 星期四[6-9节]",
//       "kcmc": "深度学习",
//       "jxddmc": "2-1-二阶梯",
//       "skcd": "4",
//       "ksjc": "6",
//       "jsjc": "9",
//       "jsxm": "黄宏博",
//       "skbj": "深度学习01",
//       "rowId": 5
//     },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "5",
//       "humancode": "2022020642",
//       "qsz": "1",
//       "zzz": "1",
//       "sksj": "1,3-8周 星期五[1-4节];9周 星期五[1-4节]",
//       "kcmc": "网络与信息安全",
//       "jxddmc": "2-2-125(原109)",
//       "skcd": "4",
//       "ksjc": "1",
//       "jsjc": "4",
//       "jsxm": "陈昕",
//       "skbj": "网络与信息安全01",
//       "rowId": 6
//     },
//     // {
//     //   "xn": "2022-2023",
//     //   "xq": "2",
//     //   "xqj": "5",
//     //   "humancode": "2022020642",
//     //   "qsz": "1",
//     //   "zzz": "8",
//     //   "sksj": "1-8周 星期五[10-13节]",
//     //   "kcmc": "嵌入式系统及应用",
//     //   "jxddmc": "2-2-125(原109)",
//     //   "skcd": "4",
//     //   "ksjc": "10",
//     //   "jsjc": "13",
//     //   "jsxm": "王超",
//     //   "skbj": "嵌入式系统及应用01",
//     //   "rowId": 7
//     // },
//     {
//       "xn": "2022-2023",
//       "xq": "2",
//       "xqj": "5",
//       "humancode": "2022020642",
//       "qsz": "3",
//       "zzz": "8",
//       "sksj": "1,3-8周 星期五[1-4节];9周 星期五[1-4节]",
//       "kcmc": "网络与信息安全",
//       "jxddmc": "2-2-125(原109)",
//       "skcd": "4",
//       "ksjc": "1",
//       "jsjc": "4",
//       "jsxm": "陈昕",
//       "skbj": "网络与信息安全01",
//       "rowId": 8
//     },
//     // {
//     //   "xn": "2022-2023",
//     //   "xq": "2",
//     //   "xqj": "5",
//     //   "humancode": "2022020642",
//     //   "qsz": "9",
//     //   "zzz": "9",
//     //   "sksj": "1,3-8周 星期五[1-4节];9周 星期五[1-4节]",
//     //   "kcmc": "网络与信息安全",
//     //   "jxddmc": null,
//     //   "skcd": "4",
//     //   "ksjc": "1",
//     //   "jsjc": "4",
//     //   "jsxm": "陈昕",
//     //   "skbj": "网络与信息安全01",
//     //   "rowId": 9
//     // },
//       // {
//       //     "xn": "2022-2023",
//       //     "xq": "2",
//       //     "xqj": "5",
//       //     "humancode": "2022020642",
//       //     "qsz": "9",
//       //     "zzz": "9",
//       //     "sksj": "1,3-8周 星期五[1-4节];9周 星期五[1-4节]",
//       //     "kcmc": "网络与信息安全",
//       //     "jxddmc": null,
//       //     "skcd": "4",
//       //     "ksjc": "1",
//       //     "jsjc": "4",
//       //     "jsxm": "陈昕",
//       //     "skbj": "网络与信息安全01",
//       //     "rowId": 9
//       // }
//   ]
// }

export default {
    components: {
        sHeader
    },
    name: "kebiaodetail",

    setup() {
        // const router = useRouter()
        const route = useRoute()
        const state = reactive({
            title: null,
            maxCourseLength: 14,
            weekCourse: [],// 课程详细课程、数量
            colorList: [], //随机颜色
            weeks:[],
            today: null,
            topdayweek: null,
            show:false,
            popupcontent: null,
            currentSchoolCalendar: {
              current_week: ''
            },
            kblx: route.query.kblx
        })
        onMounted(async () => {
            console.log(`route.query=====`,route.query)
            state.today = Utils.dateFormat_YMD(new Date())
            state.topdayweek = new Date().getDay()
            console.log(new Date().getDay())
            // let newlist = []
            // resdata.list.forEach(item=>{
            //     // item.index = parseInt(item.ksjc, 10)
            //     if(item.skcd > 1){
            //         for (let i = 0; i < item.skcd; i++){
            //             newlist.push({
            //                 index: parseInt(item.ksjc, 10) + i,
            //                 ...item
            //             })
            //         }
            //     }else {
            //         item.index = parseInt(item.ksjc, 10)
            //         newlist.push(item)
            //     }
            //     // newlist.push(item)
            // })
            state.weekCourse = weekCourse;
            state.colorList = colorList;
            // console.log(`newlist`,newlist)
            // let NewList = unique(newlist)
            // console.log(`NewList`,NewList)
            // NewList.forEach(item=>{
            //     state.weekCourse.forEach(week=>{
            //         if(item.xqj == week.week+1){
            //             week.courses.push(item)
            //         }
            //     })
            // })
            // console.log(`state.weekCourse`,state.weekCourse)
            // // debugger
            //
            // sortData();
            init();

        })

        const unique = (arr) => {
            let newArr = [arr[0]];
            for (let i = 1; i < arr.length; i++) {
                let repeat = false;
                for (let j = 0; j < newArr.length; j++) {
                    if (arr[i].kcmc === newArr[j].kcmc && arr[i].ksjc === newArr[j].ksjc && arr[i].jsjc === newArr[j].jsjc && arr[i].index === newArr[j].index) {
                        repeat = true;
                        break;
                    }
                }
                if (!repeat) {
                    newArr.push(arr[i]);
                }
            }
            return newArr;

        }

        const init = () => {
            let url = "/data/api/index-data/wdkb?kblx=" + state.kblx;
            getCurrentSchoolCalendar().then(res => {
                let currentSchoolCalendar = res.data.info;
                state.currentSchoolCalendar = currentSchoolCalendar;
                console.log(`currentSchoolCalendar`,currentSchoolCalendar)
                getDataByUrl(url + (url.indexOf('?') != -1 ? '&' : '?') + `page=1&pageSize=` + 9999 +`&xn=${currentSchoolCalendar.year_num}&xq=${currentSchoolCalendar.xq_num}`, {
                    // xn: currentSchoolCalendar.year_num,
                    // xq: currentSchoolCalendar.xq_num
                }).then(res => {
                    if (res.data.code === 200) {
                        // state.$store.commit("SET_LOADING", {name: "kebiao", flag: false});
                        if (res.data.data.list) {
                            // state.weekCourse = res.data.data.list;
                            state.maxCourseLength = res.data.data.maxJc;
                            let resdata = res.data.data
                            let newlist = []
                            resdata.list.forEach(item=>{
                                // item.index = parseInt(item.ksjc, 10)
                                if(!item.skcd){
                                    item.skcd = item.jsjc - item.ksjc +1
                                }
                                if(item.skcd > 1){
                                    for (let i = 0; i < item.skcd; i++){
                                        newlist.push({
                                            index: parseInt(item.ksjc, 10) + i,
                                            ...item
                                        })
                                    }
                                }else {
                                    item.index = parseInt(item.ksjc, 10)
                                    newlist.push(item)
                                }
                                // newlist.push(item)
                            })
                            let NewList = unique(newlist)
                            console.log(`NewList`,NewList)
                            newlist.forEach(item=>{
                                state.weekCourse.forEach(week=>{
                                    if(item.xqj == week.week+1){
                                        week.courses.push(item)
                                    }
                                })
                            })
                            sortData();
                            console.log(`kebiao`, state.weekCourse);
                            state.weeks = []; //周集合
                            // state.maxCourseLength = 0;
                            state.weeks = state.weekCourse.map((item) => {
                                for (const key in item) {
                                    if (key === "courses") {
                                        let max = 0; //
                                        //取出一周中最大的课节数及当天的最大课节数
                                        for (let j of item[key]) {
                                            j.index > state.maxCourseLength &&
                                            (state.maxCourseLength = j.index); //取所有一周里最大课节值
                                            j.index > max && (max = j.index); //取当天最大课节值
                                        }
                                        // console.log("max:", max);

                                        //如果当天的课节总数小于当天的最大课节值
                                        if (item[key].length < max) {
                                            //以最大课节值为终点遍历当天课节
                                            for (let i = 0; i < max-1; i++) {
                                                //如果下标课节不存在或着与循环的下标不匹配
                                                if (!item[key][i] || item[key][i].index != i + 1) {
                                                    item[key].splice(i, 0, " "); //填充空课节
                                                }
                                            }
                                        }
                                    }
                                }
                                return item.week;
                            });
                            console.log(`state.weeks`, state.weeks);
                        } else {

                            // let type = url.split("/");
                            // state.weekCourse = res.data.data[type[type.length - 1]];
                            state.weeks = [0,1,2,3,4,5,6]
                        }
                        // console.log(`kebiao`, state.weekCourse);
                        // state.weeks = []; //周集合
                        // // state.maxCourseLength = 0;
                        // state.weeks = state.weekCourse.map((item) => {
                        //     for (const key in item) {
                        //         if (key === "courses") {
                        //             let max = 0; //
                        //             //取出一周中最大的课节数及当天的最大课节数
                        //             for (let j of item[key]) {
                        //                 j.index > state.maxCourseLength &&
                        //                 (state.maxCourseLength = j.index); //取所有一周里最大课节值
                        //                 j.index > max && (max = j.index); //取当天最大课节值
                        //             }
                        //             // console.log("max:", max);
                        //
                        //             //如果当天的课节总数小于当天的最大课节值
                        //             if (item[key].length < max) {
                        //                 //以最大课节值为终点遍历当天课节
                        //                 for (let i = 0; i < max-1; i++) {
                        //                     //如果下标课节不存在或着与循环的下标不匹配
                        //                     if (!item[key][i] || item[key][i].index != i + 1) {
                        //                         item[key].splice(i, 0, " "); //填充空课节
                        //                     }
                        //                 }
                        //           }
                        //       }
                        //     }
                        //     return item.week;
                        // });
                        // console.log(`state.weeks`, state.weeks);
                    }else {
                      state.weeks = [0,1,2,3,4,5,6]
                    }
                }).catch(()=>{
                state.weeks = [0,1,2,3,4,5,6]
                });
            });
            state.weeks = [0,1,2,3,4,5,6]
            // state.weeks = []; //周集合
            // state.maxCourseLength = 14;
            // state.weeks = state.weekCourse.map((item) => {
            //     for (const key in item) {
            //         if (key === "courses") {
            //             let max = 0; //
            //             //取出一周中最大的课节数及当天的最大课节数
            //             for (let j of item[key]) {
            //                 j.index > state.maxCourseLength &&
            //                 (state.maxCourseLength = j.index); //取所有一周里最大课节值
            //                 j.index > max && (max = j.index); //取当天最大课节值
            //             }
            //             // console.log("max:", max);
            //
            //             //如果当天的课节总数小于当天的最大课节值
            //             if (item[key].length < max) {
            //                 //以最大课节值为终点遍历当天课节
            //                 for (let i = 0; i < max-1; i++) {
            //                     //如果下标课节不存在或着与循环的下标不匹配
            //                     if (!item[key][i] || item[key][i].index != i + 1) {
            //                         item[key].splice(i, 0, " "); //填充空课节
            //                     }
            //                 }
            //             }
            //         }
            //     }
            //     return item.week;
            // });
            // console.log(`state.weekCourse`, state.weekCourse);

        }

        const sortData = () =>{
              //周期
              state.weekCourse.sort((a, b) => {
                  return a.week - b.week;
              });
              state.weekCourse.forEach((item) => {
                  for (const key in item) {
                      if (key === "courses") {
                          item[key].sort((a, b) => {
                              return a.ksjc - b.ksjc;
                          });
                      }
                  }
              });
        }
        const showData = (weekIndex, courseNum) =>{
              if (
                  state.weekCourse[weekIndex] &&
                  state.weekCourse[weekIndex].courses[courseNum] &&
                  state.weekCourse[weekIndex].courses[courseNum].index === courseNum + 1
              ) {
                  // this.getRandomColor();
                  return state.weekCourse[weekIndex].courses[courseNum];
              }
              return false;
        }
        const numberToChinease = (n, identifier) =>{
              const chnArr = [
                  "零",
                  "一",
                  "二",
                  "三",
                  "四",
                  "五",
                  "六",
                  "七",
                  "八",
                  "九",
                  "十",
                  "十一",
                  "十二",
              ];
              return identifier === "week" && (n === 0 || n === 7) ? "日" : chnArr[n];
        }
        const getNextDate = (date, day) =>{
              var dd = new Date(date);
              dd.setDate(dd.getDate() + day);
              var y = dd.getFullYear();
              var m = dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
              var d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate();
              return y + "-" + m + "-" + d;
        }
        const showPopup = (data) => {
            console.log(data)
            state.popupcontent = data
            state.show = true;
        };
        const onClickCloseIcon = () => {
            state.show = false;

        };

        return {
            ...toRefs(state),
            init,
            sortData,
            showData,
            numberToChinease,
            getNextDate,
            showPopup,
            onClickCloseIcon,
            unique
        }
    },
    computed: {
        ...mapState(["baseUrl"])
    },
};
</script>

<style lang="scss" scoped>
//@import '../../common/style/mixin';
//@import '../../common/style/home';
.timetable {
    background-color: #ffffff;
    padding:15px;
    padding-bottom: 65px;
    .w100 {
        width: 100% !important;
    }
    .h100 {
        height: 100% !important;
    }
    .time-b {
        //margin-top: 4px;
        height: 46px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time-detail {
            color: #888888;
            font-size: 14px;
        }
        .title {
            font-size: 20px;
            font-weight: 700;
        }
    }
    .timetable-b {
        //height: 1207px;
        background-color: #fff;
        //overflow: auto;
        .timetable-content {
            height: 100%;
            table-layout: fixed;
            border-collapse: collapse; //设置表格的边框是否被合并为一个单一的边框
            text-align: center;
            color: #333333;
            font-weight: 400;
            font-size: 14px;

            thead {
                height: 50px;

                th {
                    font-size: 14px;
                    border: 1px solid #e6e6e6;
                    background-color: #fcfcfc;
                }
            }
            tbody {
                height: calc(100% - 2px) / 7;

                td {
                    padding: 5px;
                    border: 1px solid #e6e6e6;
                    .dmsjandjs-b {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                    }
                }
            }
        }
    }
}
::v-deep {
    .time-controller {
        .el-button-group {
            .el-button {
                height: 35px;
                background: #ffffff;
                font-size: 14px;
                font-weight: 600;
                border: 1px solid rgba(27, 100, 240, 0.1);
                border-radius: 4px;
                color: #333333;
                padding:10px;
            }
            .date-btn {
                padding:10px 20px;
            }
            :nth-child(2) {
                margin: 0px 12px;
            }
        }
    }
}
</style>

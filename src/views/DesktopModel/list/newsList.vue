<template>
    <div>
        <s-header :name="title"></s-header>
        <list :url="url" :queryObj="queryObj">
            <template v-slot:default="slotProps">
                <div v-if="slotProps.list && slotProps.list.length > 0" class="newContent4">
                    <ul class="newUl">
                        <li v-for="(items,index) in slotProps.list" :key="index">
                            <div class="point" @click="showNewsDetail(items)">
                            <span class="newLiTitle" :title="items.title" :style="{color: items.isread ? '#33333369' : '#333', }">
<!--                                <van-tag type="danger" plain-->
                                <!--                                         size="mini">最新</van-tag>-->
                                {{items.title}}</span>
                                <span class="newLiTime">{{ items.createDate }}</span>
                            </div>
                        </li>
                    </ul>
                </div>
            </template>
        </list>
        <van-popup v-model:show="detailShow" position="center"
                   :style="{height: '80%',width:'96%',overflow:'hidden'}">
            <div>
                <h2 class="popupH2">{{ curNews.title }}</h2>
                <p class="popupP">
                    发布时间：{{ curNews.createDate }}</p>
                <div v-html="curNews.content" class="popupDiv"></div>
            </div>
        </van-popup>
        <div v-if="popupClose" @click="detailShowFn()">
            <div class="shuxian"></div>
            <van-icon class="clickClose" name="close"/>
        </div>
    </div>
</template>

<script>
import sHeader from '@/components/SimpleHeader'
import {onMounted, reactive, toRefs} from "vue";
import {useRoute} from "vue-router";
import {mapState} from "vuex";
import {getLocal} from "@/common/js/utils";

export default {
    components: {
        sHeader
    },
    computed: {
        ...mapState(["baseUrl"])
    },
    setup() {
        const route = useRoute()
        const state = reactive({
            list: [],
            url: '/sytNews/getList',
            queryObj: {queryParam: {}},

            page: {
                //pageSizes: [10, 20, 30, 40],默认
                currentPage: 1,
                total: 0,
                pageSize: 10,
            },
            userInfo: null,
            categoryId: null,
            detailShow: false,
            popupClose: false,
            curNews: {},
            context: {},
            title: ''
        })
        onMounted(async () => {
            const userInfo = JSON.parse(getLocal('userInfo'))
            state.userInfo = userInfo
            if (route.query){
                state.categoryId = route.query.id;
                state.context.categoryId = state.categoryId;
                state.queryObj.queryParam = state.context
                state.title =  route.query.name;
            }
        })

        const showNewsDetail = (item) => {
            state.curNews = item;
            state.detailShow = true;
            state.popupClose = true;
        }

        const detailShowFn = () => {
            state.detailShow = false;
            state.popupClose = false;
        }
        return {
            ...toRefs(state),
            showNewsDetail,
            detailShowFn
        }
    },
};
</script>

<style lang="less" scoped>
@import 'src/common/style/mixin';
@import 'src/common/style/home';
.popupH2 {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.popupP {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 20px;
    text-align: center;
}

.popupDiv {
    padding: 0px 20px;
    height: 63vh;
    overflow-y: scroll;
}

.clickClose {
    font-size: 40px;
    position: fixed;
    left: calc(50% - 20px);
    top: 92%;
    color: #fff;
    z-index: 3000;
}

.shuxian {
    border-left: 2px solid #fff;
    height: 2.2%;
    width: 0px;
    position: fixed;
    left: calc(50% - 1px);
    top: 90%;
    color: #fff;
    z-index: 3000;
}
.newContent4 {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
}

.newContent1 {
    padding: 5px 10px;
}

.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    height: 29px;
    line-height: 29px;
    padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}
</style>

<template>
    <div v-if="!isnew">
        <div class="good">
            <header class="good-header"></header>
            <p class="applyServiceTitle">{{item.data.name}}
                <router-link :to="{path:'new-mine-service',query:{isNavBar: true}}">
                    <van-icon class="lookMoreDiy" name="arrow"/>
                </router-link>
<!--                <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis" v-if="moreUrl && moreUrl !== ''"/>-->
            </p>
        </div>
        <div v-if="serviceData && serviceData.length > 0" class="category-list">
            <div v-for="(item, index) in serviceData" :key="index"
                 @click="toService(item)">
                <img :src="item.icon?(baseUrl+'/file/view/'+item.icon):defaultIcon" @error="handleImageError">
                <span>{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</span>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
            <router-link :to="{path:'new-mine-service',query:{isNavBar: true}}">
                <van-icon class="lookMoreDiy" name="arrow"/>
            </router-link>
        </div>
        <div v-if="serviceData && serviceData.length > 0" class="category-list">
            <div v-for="(item, index) in serviceData" :key="index"
                 @click="toService(item)">
                <img :src="item.icon?(baseUrl+'/file/view/'+item.icon):defaultIcon" @error="handleImageError">
                <span>{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</span>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
</template>

<script>
import {GetService, hitCount,} from "@/service/home";
    import {mapState} from "vuex";
    import {nextTick, onMounted, reactive, toRefs, watch} from "vue";
    import {useRouter} from "vue-router";
import {getActiveDefinitionId} from "@/service/bpmProcessDefinition";
import {showFailToast} from "vant";

    export default {
        name: "wodefuwu",
        props: [
            'item',
            'isnew'
        ],
        setup(props) {
            const router = useRouter()
            const state = reactive({
                defaultIcon: require("../../assets/isnull.png"),
                serviceData: null,
                adress: require("../../assets/ksj.png"),
            })

            onMounted(async () => {
                serviceTabs(1, props.item.data);
            })
            watch(() => props.item.data, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) serviceTabs(1, props.item.data);
            })

            const handleImageError = (event) => {
                event.target.src = state.defaultIcon;
            }
            const serviceTabs = (key, item) => {
                if (item.tabs && item.tabs.length > 0) {
                    this.moreUrl = item.tabs[key - 1].moreUrl;
                } else {
                    if (item.moreUrl && item.moreUrl !== '') {
                        state.moreUrl = item.moreUrl;
                    }
                }
                if (item.tabs && item.tabs.length == 0) {
                    item.tabs.push({
                        name: "",
                        type: "",
                    });
                }
                // if (item.tabs[key - 1].type == "collect") {
                //     GetCollect({limit: item.limit}).then(res => {
                //         // this.$store.commit("SET_LOADING", {name: "service", flag: false});
                //         res.data.info.length == 0
                //             // ? this.$store.commit("SET_EMPTY", {name: "service", flag: true})
                //             : (state.obj.serviceData = res.data.info);
                //     });
                // } else if (item.tabs[key - 1].type == "recommend") {
                //     GetRecommend({limit: item.limit}).then(res => {
                //         this.$store.commit("SET_LOADING", {name: "service", flag: false});
                //         res.data.info.length == 0
                //             ? this.$store.commit("SET_EMPTY", {name: "service", flag: true})
                //             : (this.obj.serviceData = res.data.info);
                //     });
                // } else if (item.tabs[key - 1].type == "common") {
                //     GetCommon({limit: item.limit}).then(res => {
                //         this.$store.commit("SET_LOADING", {name: "service", flag: false});
                //         res.data.info.length == 0
                //             ? this.$store.commit("SET_EMPTY", {name: "service", flag: true})
                //             : (this.obj.serviceData = res.data.info);
                //     });
                // } else {
                GetService({
                    categoryId: item.tabs && item.tabs.length > 0 ? item.tabs[key - 1].categoryId : "",
                    tab: "all",
                    limit: item.limit,
                    showType: "mobile"
                }).then(res => {
                    state.serviceData = res.data.info
                });
            }

            const showNewsDetail = (item) => {
                if (item.detail && item.detail !== '') {
                    // window.open(item.detail, '_blank');
                    window.location.href = item.detail

                }
            }

            // 访问次数
            const setHitCount = (hitData) => {
                hitCount(hitData).then(res => {
                    console.log(res)
                })
            }

            const toService = (service) => {
                nextTick(() => {
                    let role = []
                    if (service.roleId&&state.roleList) {
                        service.roleId.forEach(item => {
                            state.roleList.forEach(val => {
                                if (item == val.id) {
                                    role.push(val.rolename)
                                }
                            })
                        })
                    }

                    if(service.popWindow == '否'){
                        let hitData = {
                            id: service.id
                        };
                        if (service.linkType == '0') {
                            getActiveDefinitionId({processDefinitionKey: service.processDefinitionId}).then(res => {
                                if (res.data.code == "00000") {
                                    setHitCount(hitData)
                                    router.push({
                                        path: "/mine-dealt-create",
                                        query: {
                                            id: res.data.info, name: service.name,
                                            activeTab: service.linkType,
                                            backUrl: '/new-mine-service',
                                            // backId: state.currentId,
                                            // backName: state.name,
                                        }
                                    })
                                } else {
                                    showFailToast(res.data.info);
                                }
                            })
                        } else if (service.linkType == '1') {
                            // 访问次数
                            setHitCount(hitData)
                            let routeData = service.link
                            // window.open(routeData, '_blank')
                            window.location.href = routeData

                        } else if (service.linkType == '2') {
                            // 访问次数
                            setHitCount(hitData)
                            router.push({path: "/mine-service-recourse", query: {id: service.resourceId, name: service.name}})
                        } else if (service.linkType == '3') {
                            // 访问次数
                            setHitCount(hitData)
                            router.push({path: "/mine-service-taskFill", query: {taskId: service.taskId, name: service.name}})
                        }
                    }else {
                        // let linkTypeName = service.linkType ? state.serviceList.find(item => {
                        //     return item.value === service.linkType;
                        // })['text'] : '';
                        // service.linkTypeName = linkTypeName
                        // let categoryName = service.categoryId ? state.categoryList.find(item => {
                        //     return item.value === service.categoryId;
                        // })['text'] : '';
                        // service.categoryName = categoryName
                        // service.rolename = role.join()
                        router.push({
                            path: 'mine-service-details',
                            query: {
                                service: JSON.stringify(service),
                            }
                        });
                    }


                })
            }
            return {
                ...toRefs(state),
                toService,
                showNewsDetail,
                handleImageError
            }
        },
        computed: {
            ...mapState(["baseUrl"])
        }
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
</style>

<template>
    <div style="border-radius: 5px;overflow: hidden;display: flex;justify-content: space-between">
        <div @click="todaiban" class="thebox" style="width: 48%;height: 90px;display: flex;border-radius: 10px;justify-content: space-around;background: linear-gradient(110.874782369444deg, rgba(255, 226, 122, 0.3) 1%, rgba(252, 0, 0, 0.3) 100%);">
            <div>
                <p style="font-size: 18px;font-weight: bold;margin: 15px 0;">今日待办</p>
                <p style="font-size: 22px;font-weight: bold;color: #F96C17;margin: 15px 0;">{{num}}</p>
            </div>
            <div>
                <img style="width: 50px;margin-top: 20px;" src="../../assets/u292.svg"/>
            </div>
        </div>
        <div @click="tofwdt" class="thebox" style="width: 48%;height: 90px;display: flex;border-radius: 10px;justif y-content: space-around;background: linear-gradient(110.75620491234deg, rgba(0, 252, 206, 0.3) 1%, rgba(56, 117, 198, 0.3) 75%);">
            <div style="margin-left: 15px;">
                <p style="font-size: 18px;font-weight: bold;margin: 15px 0;">服务大厅</p>
                <p style="font-size: 12px;color: #B2B2B2;margin: 15px 0;">一站式服务办理</p>
            </div>
            <div>
                <img style="width: 50px;margin-top: 20px;" src="../../assets/u293.svg"/>
            </div>
        </div>
    </div>
</template>

<script>
import {useRouter} from "vue-router";
import { onMounted, reactive, toRefs} from "vue";
import {getTodoTaskPage} from "@/service/bpmTask";
// import Utils from "@/utils/momentWrap";


export default {
    name: "fuwudating",
    setup() {
        const router = useRouter()
        const state = reactive({
            num:0
        })

        onMounted(async () => {

            await getData()
        })


        const getData = () => {
            getTodoTaskPage({
                page: 1,
                pageSize: 5,
            }).then(res => {
                state.num = res.data.info.total;
            });
        }



        const tofwdt = () => {
            // showLoadingToast({
            //     message: '正在跳转...',
            //     forbidClick: true,
            //     duration: 0
            // });
            router.push(
                {path: '/service'}
            )

        }
        const todaiban = () => {
            // showLoadingToast({
            //     message: '正在跳转...',
            //     forbidClick: true,
            //     duration: 0
            // });
            router.push(
                {path: '/mine-dealt',query:{isNavbar: true}}
            )

        }

        return {
            ...toRefs(state),
            tofwdt,
            getData,
            todaiban
        }
    },
}
</script>

<style scoped>

</style>
<template>
    <div>

<!--        <div v-if="!isnew">-->
<!--            <div class="good">-->
<!--                <header class="good-header"></header>-->
<!--                <div class="applyServiceTitle">-->
<!--                    <span style="float: left;">{{item.data.name}}</span>-->
<!--                    <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis" v-if="moreUrl && moreUrl !== ''"/>-->
<!--                    &lt;!&ndash;                <van-tabs style="width: 61%;float: left;" v-model:active="active"&ndash;&gt;-->
<!--                    &lt;!&ndash;                          color="#48a3ea"&ndash;&gt;-->
<!--                    &lt;!&ndash;                          title-active-color="#48a3ea"&ndash;&gt;-->
<!--                    &lt;!&ndash;                          @click-tab="changeTab">&ndash;&gt;-->
<!--                    &lt;!&ndash;                    <van-tab v-for="tab in item.data.tabs" :key="tab.id" :title="tab.name">&ndash;&gt;-->
<!--                    &lt;!&ndash;                    </van-tab>&ndash;&gt;-->
<!--                    &lt;!&ndash;                </van-tabs>&ndash;&gt;-->
<!--                </div>-->
<!--            </div>-->
<!--            <div v-if="list && list.length > 0" class="newContent4">-->
<!--                <ul class="newUl">-->
<!--                    <li v-for="(items,index) in list" :key="index">-->
<!--                        <div v-if="index < item.data.limit" class="point" style="height: 29px;line-height: 29px;padding: 0 10px;" @click="showNewsDetail(items)">-->
<!--                            <span class="newLiTitle" style="float: left;" :title="items.title" :style="{color: items.isread ? '#33333369' : '#333', }">-->
<!--&lt;!&ndash;                                <van-tag type="danger" plain&ndash;&gt;-->
<!--                                &lt;!&ndash;                                         size="mini">最新</van-tag>&ndash;&gt;-->
<!--                                {{items.title}}</span>-->
<!--                            <span class="newLiTime">{{ items.createDate }}</span>-->
<!--                        </div>-->
<!--                    </li>-->
<!--                </ul>-->
<!--            </div>-->
<!--            <div v-else>-->
<!--                <div style="text-align: center;font-size: 18px;color: silver">-->
<!--                    <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">-->
<!--                    暂无数据-->
<!--                </div>-->
<!--            </div>-->
<!--            <van-popup v-model:show="detailShow" position="center"-->
<!--                       :style="{height: '80%',width:'96%',overflow:'hidden'}">-->
<!--                <div>-->
<!--                    <h2 class="popupH2">{{ curNews.title }}</h2>-->
<!--                    <p class="popupP">-->
<!--                        发布时间：{{ curNews.createDate }}</p>-->
<!--                    <div v-html="curNews.content" class="popupDiv"></div>-->
<!--                </div>-->
<!--            </van-popup>-->
<!--            <div v-if="popupClose" @click="detailShowFn()">-->
<!--                <div class="shuxian"></div>-->
<!--                <van-icon class="clickClose" name="close"/>-->
<!--            </div>-->
<!--        </div>-->
        <div class="thebox" style="padding-bottom: 20px;background: linear-gradient(-77.3112106493847deg, rgba(184, 113, 255, 0.3) 0%, rgba(80, 250, 212, 0.3) 100%);" @click="ellipsis">
            <img style="height: 90px;position: absolute;right: 0;z-index: 1;opacity: 0.34;" :src="back">
            <div style="line-height: 50px;margin: 0 15px;display: flex;justify-content: space-between">
                        <span style="font-size: 20px;font-weight: bold;">
                            {{item.data.name}}
                        </span>
                <van-swipe vertical :autoplay="3000" :show-indicators="false" :height="20" style="height: 20px;line-height: 20px;margin-top: 15px;">
                    <van-swipe-item v-for="item in list" :key="item.id" default="2"> <span style="color: #9e9e9e;">{{item.createDate}}</span> </van-swipe-item>
                </van-swipe>
            </div>
            <div style="padding: 0 20px;">
                <van-swipe vertical :autoplay="3000" :show-indicators="false" :height="20" style="height: 20px;line-height: 20px;">
                    <van-swipe-item v-for="item in list" :key="item.id" default="2" style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap; /* 不换行 */flex: 1;"> <img style="height: 15px;vertical-align: text-top;" :src="laba"> {{item.title}}</van-swipe-item>
                </van-swipe>
            </div>
        </div>
        <!--        <div v-else class="thebox">-->
        <!--            <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">-->
        <!--                {{item.data.name}}-->
        <!--                <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis" v-if="moreUrl && moreUrl !== ''"/>-->
        <!--            </div>-->
        <!--            <van-tabs v-model="active" @click-tab="newsTabs">-->
        <!--                <van-tab :title="item.name" v-for="item in item.data.tabs" :key="item.id"  />-->
        <!--            </van-tabs>-->
        <!--            <div v-if="list && list.length > 0" class="newContent4">-->
        <!--                <ul class="newUl">-->
        <!--                    <li v-for="(items,index) in list" :key="index">-->
        <!--                        <div v-if="index < item.data.limit" class="point" style="padding: 0 20px;margin: 10px 0;" @click="showNewsDetail(items)">-->
        <!--                            <span class="newLiTitle" style="line-height: 20px;margin-bottom: 3px;width:100%;white-space: pre-wrap;display: -webkit-box;-webkit-line-clamp: 1; /* 显示的行数 */-webkit-box-orient: vertical;overflow: hidden;" :title="items.title" :style="{color: '#333' }">-->
        <!--                                {{items.title}}</span>-->
        <!--                            <div style="height: 20px;">-->
        <!--                                <span class="newLiorgName">{{ items.orgName }}</span>-->
        <!--                                <span class="newLiTime">{{ items.createDate }}</span>-->
        <!--                            </div>-->
        <!--                        </div>-->
        <!--                    </li>-->
        <!--                </ul>-->
        <!--            </div>-->
        <!--            <div v-else>-->
        <!--                <div style="text-align: center;font-size: 18px;color: silver">-->
        <!--                    <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">-->
        <!--                    暂无数据-->
        <!--                </div>-->
        <!--            </div>-->
        <!--            <van-popup v-model:show="detailShow" position="center"-->
        <!--                       :style="{height: '80%',width:'96%',overflow:'hidden'}">-->
        <!--                <div>-->
        <!--                    <h2 class="popupH2">{{ curNews.title }}</h2>-->
        <!--                    <p class="popupP">-->
        <!--                        发布时间：{{ curNews.createDate }}</p>-->
        <!--                    <div v-html="curNews.content" class="popupDiv"></div>-->
        <!--                </div>-->
        <!--            </van-popup>-->
        <!--            <div v-if="popupClose" @click="detailShowFn()">-->
        <!--                <div class="shuxian"></div>-->
        <!--                <van-icon class="clickClose" name="close"/>-->
        <!--            </div>-->
        <!--        </div>-->
    </div>
</template>

<script>

import {getDataByUrl,GetNewsList, GetReadList,SetReadList,GetDataCategory} from '@/service/portal';
import {useRouter} from 'vue-router';
import {onMounted, reactive, toRefs} from 'vue';
import {getLocal} from '@/common/js/utils';
// import router from "@/router";

export default {
    name: "xinwen",
    props: [
        'item',
        'isnew'
    ],
    setup(props) {
        const router = useRouter()
        const state = reactive({
            detailShow: false,
            popupClose: false,
            curNews: {},
            list: [],
            active: 0,
            // active: 0,
            moreUrl: "",
            clickUrl: "",
            readlist: [],
            userInfo: null,
            categoryList: null,
            category: null,
            categoryId: null,
            adress: require("../../assets/ksj.png"),
            laba: require("../../assets/u253.svg"),
            back: require("../../assets/u254.png"),
        })

        onMounted(async () => {
            const userInfo = JSON.parse(getLocal('userInfo'))
            state.userInfo = userInfo
            await GetReadList(
                {
                    "page": 1,
                    "pageSize": 20,
                    "queryParam": {
                        "type":"校内通知"
                    }
                }
            ).then(res => {
                if(res.data.code === "00000"){
                    state.readlist = res.data.info.records;
                }
            });
            if (props.item.data) {
                console.log(`props.item=======`,props.item)
                newsTabs({name:0});
            }
        })
        // watch(() => props.item.data, (newVal) => {
        //     // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
        //     if (newVal) load();
        // })

        // const changeTab = (tab) => {
        //     newsTabs(Number(tab.name))
        // }
        const newsTabs = (tab) => {
            let item = props.item.data
            let url = "";
            let key = tab.name
            console.log(`item======`,item)
            console.log(`key======`,key)

            if (item.tabs && item.tabs.length > 0) {
                url = item.tabs[key].dataSources;
                state.moreUrl = item.tabs[key].mobileMoreUrl;
                state.clickUrl = item.tabs[key].clickUrl;
            } else {
                // item.tabs.push({
                //     name: "",
                //     type: "",
                // });
                if (item.dataSources && item.dataSources !== '') {
                    url = item.dataSources;
                    // state.moreUrl = item.moreUrl;
                }
                state.moreUrl = item.mobileMoreUrl;
                state.clickUrl = item.clickUrl;
            }
            //配置的数据来源
            if (url && url !== '') {
                getDataByUrl(
                    url + (url.indexOf('?') != -1 ? '&' : '?') + `page=1&pageSize=` + item.limit,
                    {code: state.userInfo ? state.userInfo.humanCode : ''}
                ).then(res => {
                    if (res.data.code === 200) {
                        if (Object.prototype.hasOwnProperty.call(res.data.data, "list")) {
                            state.list = res.data.data.list;
                            state.list.forEach(item=>{
                                state.readlist.forEach(read=>{
                                    if( item.title === read.key){
                                        item.isread = true
                                    }
                                })
                            })
                        } else {
                            let type = url.split("/");
                            state.list = res.data.data[type[type.length - 1]];
                            state.list.forEach(item=>{
                                state.readlist.forEach(read=>{
                                    if( item.title === read.key){
                                        item.isread = true
                                    }
                                })
                            })
                        }
                    }
                });
            } else {  //系统内部新闻发布
                let obj = {
                    limit: item.limit
                }
                if (item.tabs && item.tabs.length > 0) {
                    obj.categoryId = item.tabs[key].categoryId
                    state.categoryId = item.tabs[key] ? item.tabs[key].categoryId : "";
                }else{
                    state.categoryId = item.categoryId ? item.categoryId : "";
                }

                // GetNews(obj).then(res => {
                //     state.list = res.data.info;
                //     state.list.forEach(item=>{
                //         state.readlist.forEach(read=>{
                //             if( item.title === read.key){
                //                 item.isread = true
                //             }
                //         })
                //     })
                // });
                const context = {
                    page: 1,
                    pageSize: item.limit,
                    // pageSize: 1,
                    queryParam: {
                        categoryId: item.tabs[key] ? item.tabs[key].categoryId : "",
                    },
                };
                GetNewsList(context).then(res => {
                    // this.$store.commit("SET_LOADING", {name: "news", flag: false});
                    // this.obj.news = res.data.info;
                    state.list = res.data.info.records;
                    if(state.readlist.length > 0){
                        state.list.forEach(item=>{
                            state.readlist.forEach(read=>{
                                if( item.title === read.key){
                                    item.isread = true
                                }
                            })
                        })
                    }
                });
                GetDataCategory({model: 'tongzhigonggao'}).then(res => {
                    state.categoryList = res.data.info;
                    state.categoryList.forEach(item=>{
                        if(item.id == state.categoryId){
                            state.category = item.name
                        }
                    })
                    console.log(`state.category`,res)
                });
            }
        }

        const showNewsDetail = (item) => {
            console.log(`showNewsDetail`,item)
            let data = {
                "key": item.title,
                "type":"校内通知"
            }
            SetReadList(data).then(()=>{})
            if (item.url && item.url !== '') {
                if (state.userInfo) {
                    let openUrl = item.clickUrl ? item.clickUrl : item.url.replace("#{humancode}", state.userInfo.humanCode) + (item.id ? item.id : '');
                    if (state.clickUrl && state.clickUrl !== '') {
                        openUrl = state.clickUrl + encodeURIComponent(openUrl);
                        getDataByUrl(openUrl, {}).then(res => {
                            if (res.data.code == "00000") {
                                window.location.href =res.data.info;
                                // console.log(res.data);
                            } else {
                                // this.$modal.msgWarning(res.data.info);
                            }
                        });
                    } else {
                        // window.open(openUrl, '_blank')
                        window.location.href = openUrl;
                    }

                }
            } else {
                if(item.detailLinks && item.detailLinks !== ''){
                    window.location.href = item.detailLinks;
                }else {
                    state.curNews = item;
                    state.detailShow = true;
                    state.popupClose = true;
                }

            }
        }
        const ellipsis = () => {
            router.push(
                {path: '/zixun',query:{tztabs: JSON.stringify(props.item.data.tabs),name: props.item.data.name }}
            )
            // if (state.moreUrl && state.moreUrl !== '') {
            //     if (state.moreUrl.indexOf("http") !== -1) {
            //         // let routeData = state.moreUrl;
            //         // window.open(routeData, '_self');
            //         // window.location.href = routeData
            //         if (state.userInfo) {
            //             let openUrl = state.moreUrl.replace("#{humancode}", state.userInfo.humanCode);
            //             // window.open(openUrl, '_blank')
            //             window.location.href = openUrl
            //         }
            //     } else {
            //         console.log(state.moreUrl)
            //         console.log(state.category)
            //         console.log(state.categoryId)
            //
            //         let routeData = state.moreUrl + '?name=' + state.category + '&id='+ state.categoryId   //+'?name=自定义标题'
            //         // console.log(`routeData`,routeData)
            //         // let routeData = router.push({path: state.moreUrl,query:{
            //         //         name:state.category,
            //         //         id: state.categoryId
            //         //     }});
            //         console.log(`routeData`,routeData)
            //
            //         // window.open(routeData.href, '_blank');
            //         window.location.href = routeData
            //
            //     }
            // }
        }
        const detailShowFn = () => {
            state.detailShow = false;
            state.popupClose = false;
        }
        return {
            ...toRefs(state),
            showNewsDetail,
            detailShowFn,
            // changeTab,
            ellipsis,
            newsTabs
        }
    },
};
</script>

<style lang="less" scoped>
@import '../../common/style/mixin';
@import '../../common/style/home';

.popupH2 {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.popupP {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 20px;
    text-align: center;
}

.popupDiv {
    padding: 0px 20px;
    height: 63vh;
    overflow-y: scroll;
}

.clickClose {
    font-size: 40px;
    position: fixed;
    left: calc(50% - 20px);
    top: 92%;
    color: #fff;
    z-index: 3000;
}

.shuxian {
    border-left: 2px solid #fff;
    height: 2.2%;
    width: 0px;
    position: fixed;
    left: calc(50% - 1px);
    top: 90%;
    color: #fff;
    z-index: 3000;
}


.newContent4 {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
}

.newContent1 {
    padding: 5px 10px;
}

.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    //height: 29px;
    //line-height: 29px;
    //padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    //float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}
.newLiorgName{
    float: left;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding-bottom: 15px;
}

</style>

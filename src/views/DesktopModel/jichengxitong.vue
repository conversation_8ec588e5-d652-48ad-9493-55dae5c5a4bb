<template>
    <div v-if="!isnew">
        <div class="good">
            <header class="good-header"></header>
            <p class="applyServiceTitle">{{item.data.name}}
                <van-icon v-if="mark!=='list'" class="lookMoreDiy" name="arrow" @click="handleClickMore"/>
            </p>
        </div>
        <div v-if="serviceData && serviceData.length > 0" class="category-list">
            <div v-for="(item, index) in serviceData" :key="index"
                 @click="showDetail(item.id)">
                <img :src="item.clientIcon?(baseUrl+item.clientIcon):defaultIcon" @error="handleImageError">
                <span>{{item.clientName.length >5?item.clientName.substring(0,5)+"..":item.clientName}}</span>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
            <van-icon v-if="mark!=='list'" class="lookMoreDiy" name="arrow" @click="handleClickMore"/>
        </div>
        <div v-if="serviceData && serviceData.length > 0" class="category-list">
            <div v-for="(item, index) in serviceData" :key="index"
                 @click="showDetail(item.id)">
                <img :src="item.clientIcon?(baseUrl+item.clientIcon):defaultIcon" @error="handleImageError">
                <span>{{item.clientName.length >5?item.clientName.substring(0,5)+"..":item.clientName}}</span>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
</template>

<script>
    import {GetSystem,ToService} from "@/service/portal";
    import {onMounted, reactive, toRefs, watch} from "vue";
    import {mapState} from "vuex";
    import {useRouter} from "vue-router";
    import {showLoadingToast, showFailToast, closeToast} from 'vant';

    export default {
        name: "jichengxitong",
        props: [
            'item',
            'mark',
            'isnew'
        ],
        computed: {
            ...mapState(["baseUrl"])
        },
        setup(props) {
            const router = useRouter()
            const state = reactive({
                defaultIcon: require("../../assets/isnull.png"),
                adress: require("../../assets/ksj.png"),
                serviceData: null,
            })

            onMounted(async () => {
                getData(1, props.item.data);
            })
            watch(() => props.item.data, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) getData(1, props.item.data);
            })
            const getData = (key, item) => {
                GetSystem({
                    limit: item.limit,
                    showType: "mobile"
                }).then(res => {
                    if (res.data.code === "00000") {
                        state.serviceData = res.data.info;
                    }
                });
            }

            const handleImageError = (event) => {
                event.target.src = state.defaultIcon;
            }


            const showDetail = (id) => {
                // let title = "系统";
                let context = {
                    id: id,
                    type: "sys"
                };
                showLoadingToast({
                    message: '正在跳转...',
                    forbidClick: true,
                    duration: 0
                });
                // context.type = "sys";
                // if (type == "sys") {
                //     title = "系统";
                //     context.type = "sys";
                // } else if (type == "service") {
                //     title = "服务";
                // }
                ToService(context).then(res => {
                    if (res.data.code == "00000") {
                        // window.open(res.data.info, '_blank');
                        // state.show = true;
                        closeToast()
                        window.location.href = res.data.info;
                        // setTimeout(()=>{
                        //     closeToast()
                        // },2000)
                        // showFailToast('失败文案');
                    } else {
                        showFailToast('跳转失败');
                        setTimeout(()=>{
                            closeToast()
                        },2000)
                        // this.$modal.msgWarning(res.data.info);
                    }
                });
                // window.open(app.redirectUrl, '_blank')
            }
            const handleClickMore = () => {
                router.push({
                    path: '/jichengxitong-list',
                    query: {
                        title: props.item.data.name,
                        type: 'jichengxitong'
                    }
                });
            }
            return {
                ...toRefs(state),
                showDetail,
                handleClickMore,
                handleImageError
            }
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
</style>

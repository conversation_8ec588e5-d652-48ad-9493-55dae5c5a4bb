<template>
    <div>
        <div v-if="!isnew">
            <div class="good">
                <header class="good-header"></header>
                <p class="applyServiceTitle">{{item.data.name}}
                    <van-icon class="lookMoreDiy" name="arrow" @click="ellipsis"/>
                </p>
            </div>
            <div v-if="list && list.length > 0" class="bodys">
                <div class="content_bodys" v-for="(item, index) in list" :key="index">
                    <div class="content_box">
                        <!--                    {{ item.kcmc }}-->
                        <span class="content_le">{{ item.kcmc }}</span>
                        <span class="content_ri">{{ item.jxddmc }}</span>
                    </div>
                    <!--                <div class="content_box">-->
                    <!--&lt;!&ndash;                    {{ item.jxddmc }}&ndash;&gt;-->
                    <!--                    <span class="content_p1">{{ item.jxddmc }}</span>-->

                    <!--                </div>-->
                    <p style="margin: 5px 0;color: #C3C4C4;">教师名称: {{ item.jsxm }}</p>
                    <p style="margin: 5px 0;color: #C3C4C4;">课程时间: {{ item.qsz }}-{{ item.zzz }}周</p>
                    <!--                <p style="margin: 5px 0;color: #C3C4C4;">{{ item.sksj }}</p>-->
                    <p style="margin: 5px 0;color: #C3C4C4;">教学班级: {{ item.skbj }}</p>

                    <!--                <div class="content_box2">-->
                    <!--                    &lt;!&ndash;                    {{ item.sksj }}&ndash;&gt;-->
                    <!--                    <span class="content_p1" >{{ item.jsxm }}</span>-->
                    <!--                    <br>-->
                    <!--                    <span class="content_p1">{{ item.sksj }}</span>-->

                    <!--                </div>-->
                    <!--                <p class="content_p2" :title="item.sksj">{{ item.sksj }}</p>-->
                </div>

            </div>
            <div v-else>
                <div style="text-align: center;font-size: 18px;color: silver">
                    <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                    暂无数据
                </div>
            </div>
        </div>
        <div v-else class="thebox">
            <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
                {{item.data.name}}
                <van-icon class="lookMoreDiy" name="arrow" @click="handleClickMore"/>
            </div>
            <div v-if="list && list.length > 0" class="bodys">
                <div class="content_bodys" v-for="(item, index) in list" :key="index">
                    <div class="content_box">
                        <span class="content_le">{{ item.kcmc }}</span>
<!--                        <span class="content_ri">{{ item.jxddmc }}</span>-->
                    </div>
                    <p style="margin: 5px 0;color: #C3C4C4;">教学地点: <span style="color: #319cfe">{{ item.jxddmc }}</span></p>
                    <p v-if="item.qsz" style="margin: 5px 0;color: #C3C4C4;">课程时间: {{ item.qsz }}-{{ item.zzz }}周</p>
                    <p v-if="item.skbj" style="margin: 5px 0;color: #C3C4C4;">教学班级: {{ item.skbj }}</p>
                    <p v-if="item.ksjc" style="margin: 5px 0;color: #C3C4C4;">课程时间: <span style="color: #319cfe">
                        第{{ item.weekNumber }}周 星期{{ item.xqj }} 第{{ item.ksjc }}到{{ item.jsjc }}节
                    </span></p>
                    <p style="margin: 5px 0;color: #C3C4C4;">教师名称: {{ item.jsxm }}</p>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
    import {mapState} from "vuex";
    // import {GetAppletList} from "@/service/applet";
    import {onMounted, reactive, toRefs, watch} from "vue";
    import {useRouter} from "vue-router";
    import {getCurrentSchoolCalendar} from "@/service/applet";
    import {getDataByUrl} from "@/service/portal";

    export default {
        name: "kebiao",
        props: [
            'item',
            'isnew'
        ],
        setup(props) {
            const router = useRouter()
            const state = reactive({
                curNews: {},
                applist: [],
                moreUrl: "",
                defaultIcon: require("../../assets/isnull.png"),
                serviceData: null,
                // list: [
                //
                // ],
                adress: require("../../assets/ksj.png"),
                list: [
                    // {
                    //     kcmc: '机器人的嵌入式控制器设计',
                    //     jxddmc: '1-3-6-4J',
                    //     sksj: '2-9周 星期日 第1节-第4节 1-3-6-4J',
                    //     jsxm: '李星星' ,
                    //     skbj: '制器设计',
                    //     qsz:'2',
                    //     zzz: '9',
                    // },
                    // {
                    //     kcmc: '机器人的嵌入式控制器设计',
                    //     jxddmc: '1-3-6-4J',
                    //     sksj: '2-9周 星期日 第1节-第4节 1-3-6-4J',
                    //     jsxm: '李星星' ,
                    //     skbj: '制器设计',
                    //     qsz:'2',
                    //     zzz: '9',
                    // },
                    {
                        xn: '2024-2025',
                        xq: '1',
                        humancode: '2033',
                        jsxm: '于烊' ,
                        kcdm: '801A706',
                        xqj:'5',
                        jxddmc: '图书馆楼7',
                        ksjc: '5',
                        jsjc: '6',
                        weekNumber: '6',
                        kcmc: '劳动教育（1）',
                    }],
            })
            onMounted(async () => {
                getData(1, props.item.data);
            })
            watch(() => props.item.data, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) getData(1, props.item.data);
            })
            const getData = (key) => {
                let item = props.item.data
                let url = "";
                if (item.tabs && item.tabs.length > 0) {
                    url = item.tabs[key].dataSources;
                    state.moreUrl = item.tabs[key].moreUrl;
                } else {
                    item.tabs.push({
                        name: "",
                        type: "",
                    });
                    if (item.dataSources && item.dataSources !== '') {
                        url = item.dataSources;
                        state.moreUrl = item.moreUrl;
                    }
                }

                //配置的数据来源
                if (url && url !== '') {
                    getCurrentSchoolCalendar({}).then(res => {
                        if (res.data.code === "00000") {
                            let currentSchoolCalendar = res.data.info;
                            getDataByUrl(
                                url + (url.indexOf('?') != -1 ? '&' : '?') + `page=1&pageSize=` + item.limit + `&xn=${currentSchoolCalendar.year_num}&xq=${currentSchoolCalendar.xq_num}`,
                                {
                                    xn: currentSchoolCalendar.year_num,
                                    xq: currentSchoolCalendar.xq_num
                                }
                            ).then(res => {
                                if (res.data.code === 200) {
                                    if (Object.prototype.hasOwnProperty.call(res.data.data, "list")) {
                                        state.list = res.data.data.list;
                                    } else {
                                        let type = url.split("/");
                                        state.list = res.data.data[type[type.length - 1]];
                                    }
                                }
                            });
                        }
                    });
                }

            }
            const showDetail = (app) => {
                // window.open(app.redirectUrl, '_blank')
                window.location.href = app.redirectUrl
            }
            const ellipsis = () => {
                if (props.item.data.mobileMoreUrl && props.item.data.mobileMoreUrl !== '') {
                    console.log(`props.item.data.mobileMoreUrl`, props.item.data.mobileMoreUrl)
                    // window.open(props.item.data.mobileMoreUrl, '_self')
                    window.location.href = props.item.data.mobileMoreUrl

                }
            }

            const handleClickMore = () => {
                router.push({
                    path: '/kebiaodetail',
                });
            }
            return {
                ...toRefs(state),
                showDetail,
                ellipsis,
                handleClickMore,
            }
        },
        computed: {
            ...mapState(["baseUrl"])
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';
    .content_bodys{
        //width: 100%;
        margin-bottom: 10px;
        background-color: #F4F9FF;
        color: #333;
        border-radius: 5px;
        padding: 10px 15px;
    }
    .bodys{
        //width: 100%;
        max-height: 200px;
        overflow-y: auto;
        padding: 0 20px;
    }
    .content_box{
        //width: 100%;
        //height: 20px;
        margin-top: 5px;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }
    .content_box2{
        margin-top: 5px;
        margin-bottom: 5px;
        flex-wrap: wrap;
    }
    .content_le{
        //float: left;
        font-size: 13px;
    }
    .content_ri{
        //float: right;
        color: #319cfe;
    }
    .content_p1{
        //margin: 0;
        color: #C3C4C4;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 1;
    }
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
    }
</style>

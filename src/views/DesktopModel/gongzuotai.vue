<template>
    <div>
        <div class="thebox">
            <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
                {{item.data.name}}
            </div>
            <van-tabs v-model="active" @click-tab="newsTabs">
                <van-tab :title="item.name" v-for="item in item.data.tabs" :key="item.id"  />
            </van-tabs>
            <div class="title">
                <van-row type="flex" justify="center">
                    <van-col style="text-align: left;" span="10">流程名</van-col>
                    <van-col style="text-align: left;white-space: nowrap; /* 防止文本换行 */overflow: hidden; /* 隐藏超出部分 */text-overflow: ellipsis;" span="5">更新时间</van-col>
                    <van-col style="white-space: nowrap; /* 防止文本换行 */overflow: hidden; /* 隐藏超出部分 */text-overflow: ellipsis;" v-for="(item,index) in titlist" :key="index" span="3">{{item}}</van-col>
                </van-row>
            </div>
            <div style="height: 200px;overflow: auto;margin: 0 15px;">
                <van-list v-model:loading="loading"
                          :finished="finished"
                          finished-text="没有更多了"
                          @load="onLoad">
                    <van-cell v-for="item in list"
                              :key="item.id"
                              style="padding-left: 0;padding-right: 0;"
                              @click="onDetail(item)">
                        <van-row type="flex" justify="center" class="room-row">
                            <van-col style="text-align: left;padding-left: 3px;white-space: nowrap; /* 防止文本换行 */overflow: hidden; /* 隐藏超出部分 */text-overflow: ellipsis;" span="10">{{item.NAME}}</van-col>
                            <van-col style="text-align: left;" span="5">{{timeAgo(dateFormat_date(item.CREATETIME))}}</van-col>
                            <van-col @click="handleBpmData(item,key == 0 ? 'db' : 'jxz')" style="color: #FFCB00;" span="3">{{key == 0 ? item.DBNUM : item.JXZ}}</van-col>
                            <van-col @click="handleBpmData(item,key == 0 ? 'yb' : 'yzz')" style="color: #FFCB00;" span="3">{{key == 0 ? item.YBNUM : item.YZZ}}</van-col>
                            <van-col @click="handleBpmData(item,key == 0 ? 'cs' : 'ybj')" style="color: #FFCB00;" span="3">{{key == 0 ? item.CSNUM : item.YBJ}}</van-col>
                        </van-row>
                    </van-cell>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>

import {getDataByUrl} from '@/service/portal';
import {useRouter} from 'vue-router';
import {onMounted, reactive, toRefs} from 'vue';
import {getLocal} from '@/common/js/utils';
import Utils from "@/utils/momentWrap";

export default {
    name: "gongzuotai",
    props: [
        'item',
        'isnew'
    ],
    setup(props) {
        const router = useRouter()
        const state = reactive({
            detailShow: false,
            popupClose: false,
            loading: false,
            finished: false,

            curNews: {},
            list: [],
            active: 0,
            // active: 0,
            moreUrl: "",
            clickUrl: "",
            readlist: [],
            key: 0,
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            titlist: null,
            userInfo: null,
            categoryList: null,
            url: '',
            category: null,
            categoryId: null,
            adress: require("../../assets/ksj.png"),
        })

        onMounted(async () => {
            const userInfo = JSON.parse(getLocal('userInfo'))
            state.userInfo = userInfo
            if (props.item.data) {
                newsTabs({name: 0});
            }
        })
        const dateFormat_date = (o) => {
            return Utils.dateFormat_date(o);
        }
        // watch(() => props.item.data, (newVal) => {
        //     // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
        //     if (newVal) load();
        // })

        // const changeTab = (tab) => {
        //     newsTabs(Number(tab.name))
        // }

        const timeAgo = (timestamp) => {
            const date = new Date(timestamp);
            const now = new Date();

            const seconds = Math.floor((now - date) / 1000);
            let interval = Math.floor(seconds / 31536000);

            if (interval > 1) {
                return interval + " 年前";
            }
            interval = Math.floor(seconds / 2592000);
            if (interval > 1) {
                return interval + " 月前";
            }
            interval = Math.floor(seconds / 86400);
            if (interval > 1) {
                return interval + " 天前";
            }
            interval = Math.floor(seconds / 3600);
            if (interval > 1) {
                return interval + " 小时前";
            }
            interval = Math.floor(seconds / 60);
            if (interval > 1) {
                return interval + " 分钟前";
            }
            return Math.floor(seconds) + " 秒前";
        }

        const newsTabs = (key) => {
            let item = props.item.data
            // let url = "";
            console.log(`item======`,item)
            console.log(`key======`,key)
            //配置的数据来源
            if (key.name === 0) {
                state.key = key.name
                // state.tableColumns = state.wsdColumns;
                state.titlist = null;
                state.titlist = [
                    '待办',
                    '已办',
                    '抄送',
                ];
                state.url = '/bpmProcessInstanceExt/receivedStatistics';
            }else if (key.name === 1) {
                state.key = key.name
                state.titlist = null;
                state.titlist = [
                    '进行中',
                    '已终止',
                    '已办结',
                ];
                // state.tableColumns = state.wfqColumns;
                state.url = "/bpmProcessInstanceExt/originatedStatistics";
            }
            console.log(`titlist======`,state.titlist)
            state.list = [];
            state.page = {
                total: 0,
                currentPage: 0,
                pageSize: 10
            }
            onLoad();
        }

        const pageParam = () => {
            return {
                page: state.page.currentPage,
                pageSize: state.page.pageSize,
                queryParam: {}
            };
        }

        const onLoad = () => {
            let param = pageParam()
            let newObj = Object.assign(
                param
            );
            console.log(`getDataByUrl`,state.url)
            getDataByUrl(state.url, newObj).then(res => {
                // state.optColumn = res.data.data.columns;
                if (param.page === 1) {
                    state.list = res.data.info.records
                } else {
                    state.list = state.list.concat(res.data.info.records)
                }
                console.log(`receivedStatistics====`,state.list)
                state.loading = false;
                state.page.currentPage++
                if(res.data.info.total == 0 ){
                    state.finished = true
                }else {
                    state.finished = res.data.info.pages <= res.data.info.current
                }
                // state.finished = true

            }).catch(()=>{
                // state.list = [];
                state.loading = false;
                state.finished = true;
            });

        };

        const onDetail = () => {

        }

        const handleBpmData = (item,type) => {
            let queryParam = {
                processDefinitionName: item.NAME,
                processDefinitionId: item.PROCESSDEFINITIONID,
            }
            if(type == 'db'){
                router.push({
                    path: '/mine-dealt',
                    query: {
                        queryParam: JSON.stringify(queryParam),
                        setNewOldActiveTab: 0,
                        isNavbar: true
                    }
                });
            }else if(type == 'yb'){
                router.push({
                    path: '/mine-dealt',
                    query: {
                        queryParam: JSON.stringify(queryParam),
                        setNewOldActiveTab: 1,
                        isNavbar: true
                    }
                });
            }else if(type == 'cs'){
                router.push({
                    path: '/mine-dealt',
                    query: {
                        queryParam: JSON.stringify(queryParam),
                        setNewOldActiveTab: 5,
                        isNavbar: true
                    }
                });
            }else if(type == 'jxz'){
                queryParam.status = 1;
                router.push({
                    path: '/mine-dealt',
                    query: {
                        queryParam: JSON.stringify(queryParam),
                        setNewOldActiveTab: 6,
                        isNavbar: true
                    }
                });
            }else if(type == 'yzz'){
                queryParam.status = 2;
                queryParam.result = 3;
                router.push({
                    path: '/mine-dealt',
                    query: {
                        queryParam: JSON.stringify(queryParam),
                        setNewOldActiveTab: 6,
                        isNavbar: true
                    }
                });
            }else if(type == 'ybj'){
                queryParam.status = 2;
                queryParam.result = 2;
                router.push({
                    path: '/mine-dealt',
                    query: {
                        queryParam: JSON.stringify(queryParam),
                        setNewOldActiveTab: 6,
                        isNavbar: true
                    }
                });
            }

        }


        const detailShowFn = () => {
            state.detailShow = false;
            state.popupClose = false;
        }
        return {
            ...toRefs(state),
            detailShowFn,
            onLoad,
            // changeTab,
            dateFormat_date,
            newsTabs,
            handleBpmData,
            onDetail,
            timeAgo
        }
    },
};
</script>

<style lang="less" scoped>
@import '../../common/style/mixin';
@import '../../common/style/home';

.popupH2 {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.popupP {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 20px;
    text-align: center;
}

.popupDiv {
    padding: 0px 20px;
    height: 63vh;
    overflow-y: scroll;
}

.clickClose {
    font-size: 40px;
    position: fixed;
    left: calc(50% - 20px);
    top: 92%;
    color: #fff;
    z-index: 3000;
}

.shuxian {
    border-left: 2px solid #fff;
    height: 2.2%;
    width: 0px;
    position: fixed;
    left: calc(50% - 1px);
    top: 90%;
    color: #fff;
    z-index: 3000;
}


.newContent4 {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
}

.newContent1 {
    padding: 5px 10px;
}

.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    //height: 29px;
    //line-height: 29px;
    //padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding-bottom: 15px;
}
.title {
    text-align: center;
    color: #000;
    font-size: 11px;
    font-weight: bold;
    line-height: 35px;
    border-bottom: 1px solid #eee;
    margin: 0 15px;
    background-color: #E8F3FD;
}

.van-row {
    border-bottom: 1px solid #eee;
}

.van-row:last-child {
    border: none;
}

.room-row {
    font-size: 11px;
}

.room-row .van-col:last-child {
    color: #F79E1E;
}

.room-row .van-col {
    text-align: center;
}

.van-cell__value--alone {
    color: #969799;
}

.van-cell .van-col--6, .title .van-col--6 {
    text-align: left;
}

.van-cell .van-col:nth-of-type(4), .title .van-col:nth-of-type(4) {
    //text-align: right;
}
</style>

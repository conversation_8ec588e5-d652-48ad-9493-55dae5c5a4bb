<template>
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="#1baeae">
        <van-swipe-item v-for="(item, index) in carouselData" :key="index">
            <img :src="baseUrl+item.url" @error="handleImageError" @click="showNewsDetail(item)">
        </van-swipe-item>
    </van-swipe>
</template>
<script>

    import {GetCarousel} from "@/service/portal";
    import {mapState} from "vuex";
    import {onMounted, reactive, toRefs, } from "vue";

    export default {
        name: "lunbotu",
        props: [
            'item'
        ],
        computed: {
            ...mapState(["baseUrl"])
        },
        setup(props) {
            const state = reactive({
                defaultIcon: require("../../assets/lunbo.png"),
                carouselData: [],
            })

            onMounted(async () => {
                console.log(props.item)
                load();
            })
            // watch(() => state.item.data, (newVal) => {
            //     // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
            //     if (newVal.id) load();
            // })
            const load = () => {
                GetCarousel({type: props.item.data.lunbotuType}).then(res => {
                    console.log(res.data.info)
                    state.carouselData = res.data.info;
                });
            }

            const handleImageError = (event) => {
                event.target.src = state.defaultIcon;
            }

            const showNewsDetail = (item) => {
                if (item.detail && item.detail !== '') {
                    // window.open(item.detail, '_blank');
                    window.location.href = item.detail
                }
            }
            return {
                ...toRefs(state),
                showNewsDetail,
                handleImageError
            }
        },
    };
</script>

<style lang='less' scoped>
    .my-swipe {
        img {
            width: 100%;
            height: 145px;
        }
    }
</style>

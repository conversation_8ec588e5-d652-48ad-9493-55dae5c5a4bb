<template>
    <div v-if="!isnew">
        <div class="good">
            <header class="good-header"></header>
            <p class="applyServiceTitle">{{item.data.name}}
            </p>
        </div>
        <div v-if="applist && applist.length > 0" class="category-list">
            <div class="data" v-for="(item, index) in applist" :key="index" @click="showDetail(item)">
                <p>{{item.name.length >10?item.name.substring(0,10)+"..":item.name}}</p>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
        </div>
        <div v-if="applist && applist.length > 0" class="category-list">
            <div class="data" v-for="(item, index) in applist" :key="index" @click="showDetail(item)">
                <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{item.name.length >10?item.name.substring(0,10)+"..":item.name}}</p>
            </div>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
    </div>
</template>

<script>
    import {GetAppletList} from "@/service/applet";
    import {useRouter} from "vue-router";
    import {onMounted, reactive, toRefs, watch} from "vue";
    import {closeToast, showLoadingToast} from "vant";

    export default {
        name: "wodeliebiao",
        props: [
            'item',
            'isnew'
        ],
        setup(props) {
            const router = useRouter()
            const state = reactive({
                moreUrl: "",
                applist: [],
                adress: require("../../assets/ksj.png"),
                icon: 'icon-template'
            })

            onMounted(async () => {
                load();
            })
            watch(() => props.item.data, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) load();
            })
            const load = () => {
                GetAppletList({
                    queryParam: {type: "超链接"},
                    page: 1,
                    pageSize: 10
                }).then(res => {
                    if (res.data.code === "00000") {
                        state.applist = res.data.info.records;
                    }
                });
            }

            const showDetail = (app) => {
                // window.open(app.redirectUrl, '_blank')
                showLoadingToast({
                    message: '正在跳转...',
                    forbidClick: true,
                    duration: 0
                });
                window.location.href = app.redirectUrl
                setTimeout(()=>{
                    closeToast()
                },2000)
            }
            const ellipsis = () => {
                if (props.item.data.moreUrl && props.item.data.moreUrl !== '') {
                    let routeData = router.resolve({name: '更多链接', query: {data: JSON.stringify(props.item.data)}});
                    console.log(`routeData`, routeData)
                    // window.open(routeData.href, '_blank');
                    window.location.href = routeData.href
                }
            }
            return {
                ...toRefs(state),
                showDetail,
                ellipsis,
            }
        },
    };
</script>


<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
</style>

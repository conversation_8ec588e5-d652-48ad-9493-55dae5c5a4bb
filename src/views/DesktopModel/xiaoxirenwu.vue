<template>
  <div>
    <div>
      <div class="small-newWrapTitle clearfix">
        <p class="small-serviceTopTitle">{{ item.data.name }}</p>
        <div v-if="item.data.moreUrl !== null" class="small-serviceMore">
          <i class="icon-font icon-ellipsis"></i>
        </div>
      </div>
      <p class="serviceTopTitle">{{ item.data.name }}</p>
      <a-tabs :default-active-key="1" @tabClick="messageTabs($event, item.data)">
        <a-tab-pane v-for="item in item.data.tabs" :key="item.index" :tab="item.name">
          <div class="loading" v-show="isPortalLoading.message">
            <img src="../../../styles/assets/image/loadinggif.gif" width="50px"/>
          </div>
          <div class="newContent newContent1 newContentRight" style="display: block;">
            <ul class="newUl">
              <li v-for="msg in obj.msgData" :key="msg.id">
                <a :href="msg.detailLinks">
                                                <span class="newLiTitle">
                                                    <!-- <span v-if="msg.msgType == '申请'" class="shenqing">[{{msg.msgType}}]</span>
                          <span v-else-if="msg.msgType == '待办'" class="daiban">[{{msg.msgType}}]</span>
                          <span v-else-if="msg.msgType == '消息'" class="xiaoxi">[{{msg.msgType}}]</span>-->
                                                    [{{ msg.type }}] {{ msg.content }}
                                                </span>
                  <span class="newLiTime">{{ msg.createDate }}</span>
                </a>
              </li>
            </ul>
          </div>
        </a-tab-pane>
      </a-tabs>
      <div class="serviceMore">
        <i @click="ellipsis" class="icon-font icon-ellipsis"></i>
      </div>
    </div>
  </div>
</template>

<script>

import {GetMsg} from "@/api/portal";
import {mapGetters} from "vuex";
import {getStore} from "@/util/store";

export default {
  name: "xiaoxirenwu",
  props: [
    'item',
    'obj'
  ],
  data() {
    return {
      moreUrl:"",
    };
  },
  created() {

  },
  methods: {
    ellipsis(){
      if (this.moreUrl && this.moreUrl !== '') {
        if(this.moreUrl.indexOf("http" !== -1)){
          let userInfo = getStore({ name: "user-info" });
          if (userInfo) {
            let openUrl = this.moreUrl
            window.open(openUrl, '_blank')
          }
        }else {
          let routeData = this.$router.resolve({path: this.moreUrl});
          window.open(routeData.href, '_blank');
        }
      }
    },
    messageTabs(key, item) {
      this.obj.msgData = [];
      GetMsg({
        categoryId: item.tabs[key - 1].categoryId,
        limit: item.limit
      }).then(res => {
        this.$store.commit("SET_LOADING", {name: "message", flag: false});
        this.obj.msgData = res.data.info;
      });
    },
  },
  watch: {
  },
  computed: {
    ...mapGetters(["isPortalLoading", "isEmpty"]),
  }
};
</script>

<style scoped>
@import url(../../../styles/assets/css/mine.css);
.small-newWrapTitle {
  display: none;
}
.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}
.newUl li:hover {
  background: rgba(0, 0, 0, 0.07);
}
</style>

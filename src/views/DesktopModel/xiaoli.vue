<template>
    <div v-if="!isnew">
        <div class="good">
            <header class="good-header"></header>
            <p class="applyServiceTitle">{{item.data.name}}
                <van-icon class="lookMoreDiy" name="arrow"/>
            </p>
        </div>
        <div ref="serviceBOX" class="newContents">
            <div class="contentbox" @click="ellipsis">
                <p class="contentbox_p1"><van-icon name="calendar-o" size="45" /></p>
                <p class="contentbox_p2">
                    {{resData.term_num}}
                </p>
                <p class="contentbox_p3">
                    第 <b>{{resData.current_week}}</b> 教学周
                </p>
                <p class="contentbox_p4">
                    {{nowDate}} {{xq}}
                </p>
                <div v-for="(item,index) in resData.currentHolidays" class="contentbox_div" :key="index" :title="item.holiday_name">
                    {{item.holiday_name}}
                </div>
            </div>
        </div>
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
        </div>
        <div ref="serviceBOX" class="newContents">
            <div class="contentbox" @click="ellipsis">
                <p class="contentbox_p1"><van-icon name="calendar-o" size="45" /></p>
                <p class="contentbox_p2">
                    {{resData.term_num}}
                </p>
                <p class="contentbox_p3">
                    第 <b>{{resData.current_week}}</b> 教学周
                </p>
                <p class="contentbox_p4">
                    {{nowDate}} {{xq}}
                </p>
                <div v-for="(item,index) in resData.currentHolidays" class="contentbox_div" :key="index" :title="item.holiday_name">
                    {{item.holiday_name}}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {mapState} from "vuex";
    import {getCurrentSchoolCalendar} from "@/service/applet";
    import {onMounted, reactive, toRefs, } from "vue";
    import Utils from "@/utils/momentWrap";
    import {useRouter} from "vue-router";

    export default {
        name: "xiaoli",
        props: [
            'item',
            'isnew'
        ],
        setup(props) {
            const router = useRouter()
            const state = reactive({

                nowDate:Utils.dateFormat_YMD(new Date()),
                xq: Utils.dateFormat_DDDD(new Date()),
                resData: [],
            })
            onMounted(async () => {
                getData();
            })
            const getData = () => {
                getCurrentSchoolCalendar({}).then(res => {
                    if (res.data.code === "00000") {
                        state.resData = res.data.info;
                        console.log(res.data.info)
                    }
                });
            }
            const ellipsis = () => {
                router.push({
                    path: '/calendar',
                    query: {
                        title:props.item.data.name,
                        type:'calendar'
                    }
                });
            }

            return {
                ...toRefs(state),
                ellipsis,
            }
        },
        computed: {
            ...mapState(["baseUrl"])
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';

    .newContents {
        overflow-y: hidden;
    }

    .weekBox {
        width: 100%;
        height: 50px;
        /*border: 1px solid #EBEEF5;*/
        display: flex;
    }

    .weekitem1 {
        width: 10%;
        /*border: 1px solid #EBEEF5;*/
        text-align: center;
        min-height: 50px;
    }

    .weekitem {
        width: 12.85%;
        border: 1px solid #EBEEF5;
        text-align: center;
        min-height: 50px;
    }

    .dataBox {
        /*display: flex;*/
        height: 100%;
        width: 100%;
        /*margin-left: 20%;*/
        /*float: right;*/
        /*flex-wrap: wrap;*/
    }

    .dataline {
        display: flex;
        width: 100%;
        /*height: 100%;*/
    }

    .dataitem3 {
        width: 10%;
        border: 1px solid #00a5ec;
        text-align: center;
        min-height: 80px;
    }

    .dataitem2 {
        width: 12.85%;
        border: 1px solid #00a5ec;
        text-align: center;
        min-height: 80px;
    }

    .dataitem {
        width: 14.2%;
        border: 1px solid #00a5ec;
        text-align: center;
        min-height: 80px;
    }

    .small-newWrapTitle {
        display: none;
    }

    .clearfix:before,
    .clearfix:after {
        content: "";
        display: table;
    }

    .clearfix:after {
        clear: both;
    }

    .clearfix {
        *zoom: 1;
    }

    .newUl li:hover {
        background: rgba(0, 0, 0, 0.07);
    }

    ::v-deep .cus-a-modal .ant-modal {
        top: 60px;
    }

    ::v-deep .cus-a-modal .ant-modal-header {
        padding: 15px 24px;
    }

    ::v-deep .el-calendar__header {
        display: none;
    }

    .contentbox {
        text-align: center;
        background-color: #ffffff10;
        padding: 10px 10px 30px 10px;
        cursor: pointer;
        height: calc(100% - 10px);
        overflow-y: auto;
    }

    .contentbox_p1 {
        color: #02d9ec;
        /*font-size: 40px;*/
    }

    .contentbox_p2 {
        /*color: #f8f8f895;*/
    }

    .contentbox_p3 {
        margin-top: 5px;
        /*color: #fcfcfc;*/
        padding: 5px;
    }

    .contentbox_p4 {
        margin-top: 5px;
        margin-bottom: 10px;
        /*color: #f8f8f895;*/
    }

    .contentbox_p3 b {
        font-size: 20px;
    }

    .contentbox_div {
        background-color: #ffffff15;
        height: 30px;
        padding: 2px 5px;
        line-height: 25px;
        /*color: #f8f8f8;*/
        margin-top: 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
</style>

<template>
    <div v-if="!isnew">
        <div class="good">
            <header class="good-header"></header>
            <div class="applyServiceTitle">
                <span style="float: left;">{{item.data.name}}</span>
            </div>
        </div>

        <div class="category-list">
            <div class="data" v-for="(app,index) in item.data.tabs" :key="index" @click="handleClick(app)">
                <p v-if="app.name">
                    <van-icon name="bar-chart-o" style="margin: 6px;"/>
                    {{app.name}}
                </p>
            </div>
        </div>

<!--        <iframe v-if="iframeshow" class="iframe" :src="dataSources"></iframe>-->
    </div>
    <div v-else class="thebox">
        <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
            {{item.data.name}}
        </div>
        <div class="category-list">
            <div class="data" v-for="(app,index) in item.data.tabs" :key="index" @click="handleClick(app)">
                <p v-if="app.name">
                    <van-icon name="bar-chart-o" style="margin: 6px;"/>
                    {{app.name}}
                </p>
            </div>
        </div>
    </div>
</template>

<script>
    import {useRouter} from "vue-router";
    import {onMounted, reactive, toRefs} from "vue";
    import {getLocal} from '@/common/js/utils'
    import {showSuccessToast} from 'vant';

    export default {
        name: "qiantao",
        props: [
            'item',
            'obj',
            'isnew'
        ],
        setup(props) {
            const router = useRouter()
            const state = reactive({
                isNewsShow: false,
                curNews: {},
                list: [],
                moreUrl: "",
                dataSources: '',
                tabsshow: false,
                iframeshow: false,
                userInfo: null,
            })
            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                if (props.item.data) {
                    newsTabs({name:0});
                }

            })

            const newsTabs = (tab) => {
                let key = tab.name
                let item = props.item.data
                console.log(`item`, item)
                let url = "";
                if (item.tabs && item.tabs.length > 0) {
                    url = item.tabs[key].dataSources;
                    state.moreUrl = item.tabs[key].moreUrl;
                    state.dataSources = url;
                    state.tabsshow = true;
                    state.iframeshow = false;

                } else {
                    item.tabs.push({
                        name: "",
                        type: "",
                    });
                    if (item.dataSources && item.dataSources !== '') {
                        url = item.dataSources;
                        state.moreUrl = item.moreUrl;
                        state.dataSources = url;
                        state.iframeshow = true;
                        state.tabsshow = false;
                    }
                }
            }
            const ellipsis = () => {
                if (state.moreUrl && state.moreUrl !== '') {
                    if (state.moreUrl.indexOf("http" !== -1)) {
                        if (state.userInfo) {
                            let openUrl = state.moreUrl
                            // window.open(openUrl, '_blank')
                            window.location.href = openUrl

                        }
                    } else {
                        let routeData = router.resolve({path: state.moreUrl});
                        // window.open(routeData.href, '_blank');
                        window.location.href = routeData.href

                    }
                }

            }

            const handleClick = (app) => {
                console.log(app)
                showSuccessToast('功能定开中。。。');
            }
            return {
                ...toRefs(state),
                ellipsis,
                handleClick,
            }
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';

    /*.small-newWrapTitle {*/
    /*    display: none;*/
    /*}*/

    /*.clearfix:before,*/
    /*.clearfix:after {*/
    /*    content: "";*/
    /*    display: table;*/
    /*}*/

    /*.clearfix:after {*/
    /*    clear: both;*/
    /*}*/

    /*.clearfix {*/
    /*    *zoom: 1;*/
    /*}*/

    /*.newUl li:hover {*/
    /*    background: rgba(0, 0, 0, 0.07);*/
    /*}*/

    /*::v-deep .cus-a-modal .ant-modal {*/
    /*    top: 60px;*/
    /*}*/

    /*::v-deep .cus-a-modal .ant-modal-header {*/
    /*    padding: 15px 24px;*/
    /*}*/

    iframe {
        height: calc(100% - 45px);
        width: 100%;
        border: 0px;
    }

    .iframe {
        margin-top: 45px;
        height: calc(100% - 45px);
        width: 100%;
    }
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        padding-bottom: 10px;
    }
    /*.box {*/
    /*    height: 100%;*/
    /*    width: 100%;*/
    /*}*/

    /*::v-deep .ant-tabs-content {*/
    /*    height: 100%;*/
    /*}*/

    /*/deep/ .ant-tabs {*/
    /*    height: 100% !important;*/
    /*}*/
</style>

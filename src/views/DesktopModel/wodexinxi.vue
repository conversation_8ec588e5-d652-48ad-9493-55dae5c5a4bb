<template>
    <div>

        <div v-if="!isnew">
            <div class="good">
                <header class="good-header"></header>
                <p class="applyServiceTitle">{{item.data.name}} </p>
            </div>
            <div v-if="applist && applist.length > 0" class="dataContent">
                <div class="current-data" v-for="(app,index) in applist" :key="index" @click="handleClick(app)">
                    <span style="overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;">{{app.name}}</span>
                    <div><span class="colorBlue">{{app.count?app.count:0}}</span></div>
                </div>
            </div>
            <div v-else>
                <div style="text-align: center;font-size: 18px;color: silver">
                    <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                    暂无数据
                </div>
            </div>
        </div>
        <div v-else class="thebox">
            <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
                {{item.data.name}}
            </div>
            <div class="service-row">
                <div v-for="item in applist" :key="item" class="index-sml" @click="handleClick(item)" style="width: 33%">
                    <p style="font-size: 18px;font-weight: bold;margin: 10px 0 0 0;">{{item.count ? item.count : 0}}</p>
                    <p style="font-size: 12px;color: #B2B2B2;margin: 10px 0;">{{item.name}}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    import {GetAppletList} from "@/service/applet";
    import {getDataByUrl} from "@/service/portal";
    import {onMounted, reactive, toRefs} from "vue";
    import {getLocal} from '@/common/js/utils'
    import {useRouter} from "vue-router";

    export default {
        name: "wodexinxi",
        props: [
            'item',
            'isnew'
        ],
        setup() {
            const router = useRouter()
            const state = reactive({
                applist: [],
                userInfo: null,
                adress: require("../../assets/ksj.png"),
            })
            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                load()
            })
            const load = () => {
                GetAppletList({page: 1, pageSize: 10, queryParam: {type: "小应用",placement:"小应用"}}).then(async res => {
                    if (res.data.code === "00000") {
                        let data = res.data.info.records;
                        for (const app of data) {
                            if (app && app.dataUrl && app.dataUrl != null && app.dataUrl !== '') {
                                const obj = await getDataByUrl(app.dataUrl, {
                                    code: state.userInfo ? state.userInfo.humancode : ''
                                });
                                app.count = obj.data.data.count;
                            }
                        }
                        state.applist = data;
                    }
                });
            }

            const handleClick = (app) => {
                if(app.detailUrl) {
                    router.push({
                        path: '/wodexinxi-list',
                        query: {
                            title: app.name,
                            detailUrl: app.detailUrl,
                        }
                    });
                }
            }
            return {
                ...toRefs(state),
                handleClick,
            }
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';

    .current-data {
        text-align: center;
        width: 23%;
        border-radius: 3px;
        margin: 3px;
        float: left;
        font-size: 14px;
        background: #f4f9ff;
        background-size: 100% 100%;
        line-height: 28px;
    }

    .current-data:nth-last-of-type(1) {
        margin-right: 0;
    }

    .current-data div {
        color: #333;
    }

    .current-data .colorBlue {
        color: #319cfe;
        font-size: 16px;
        font-weight: bold;
        margin-right: 5px;
    }
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
    }
    .service-row {
        overflow: hidden;
        font-size: 12px;
    }
    .index-sml {
        width: 25%;
        height: 60px;
        float: left;
        text-align: center;
        margin-bottom: 20px;
    }

    .index-sml img {
        width: 26px;
        height: 26px;
    }

    .index-sml p {
        margin-top: 10px;
    }

</style>

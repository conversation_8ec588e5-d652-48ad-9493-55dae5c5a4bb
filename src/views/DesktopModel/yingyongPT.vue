<template>
    <div>
        <div v-if="!isnew">
            <div v-if="!notit" class="good">
                <header class="good-header"></header>
                <p class="applyServiceTitle">{{item.data.name}}
                    <van-icon class="lookMoreDiy" name="arrow" @click="handleClickMore"/>
                </p>
            </div>
            <div v-if="serviceData && serviceData.length > 0" class="category-list">
                <div v-for="(item, index) in serviceData" :key="index"
                     @click="showDetail(item)">
                    <img :src="item.imgUrl.url?(baseUrl+item.imgUrl.url):defaultIcon" style="height: 36px;width: 36px;" @error="handleImageError">
                    <span style="font-size: 14px;">{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</span>
                </div>
            </div>
            <div v-else>
                <div style="text-align: center;font-size: 18px;color: silver">
                    <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                    暂无数据
                </div>
            </div>
        </div>

        <div v-else class="thebox">
            <div v-if="!notit"  style="line-height: 50px;font-size: 20px;font-weight: bold;margin-left: 15px;">
                推荐应用
                <van-icon class="lookMoreDiy" name="arrow" @click="handleClickMore"/>
            </div>
            <div class="service-row">
                <div class="index-sml" v-for="(item) in serviceData" :key="item.id" @click="showDetail(item)">
                    <img :src="item.imgUrl.url?(baseUrl+item.imgUrl.url):defaultIcon" style="height: 36px;width: 36px;" @error="handleImageError"/>
                    <p style="font-size: 14px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap; /* 不换行 */flex: 1;">{{item.name}}</p>
                </div>
<!--                <div class="index-sml">-->
<!--                    <img src="../../assets/u238.svg"/>-->
<!--                    <p>更多</p>-->
<!--                </div>-->
            </div>
        </div>
    </div>
</template>

<script>
    import {mapState} from "vuex";
    import {GetAppletList} from "@/service/applet";
    import {onMounted, reactive, toRefs, watch} from "vue";
    import {useRouter} from "vue-router";
    import {showLoadingToast,closeToast} from "vant";
    import {getLocal} from "@/common/js/utils";
    // import {useRouter} from "vue-router";

    export default {
        name: "yingyongPT",
        props: [
            'item',
            'isnew',
            'notit',
        ],
        setup(props) {
            const router = useRouter()
            const state = reactive({
                curNews: {},
                applist: [],
                moreUrl: "",
                defaultIcon: require("../../assets/isnull.png"),
                serviceData: null,
                userInfo: null,
                adress: require("../../assets/ksj.png"),
            })
            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                getData(1, props.item.data);
            })
            watch(() => props.item.data, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) getData(1, props.item.data);
            })

            const handleImageError = (event) => {
                event.target.src = state.defaultIcon;
            }
            const getData = (key, item) => {
                GetAppletList({
                    queryParam: {type: "应用平台"},
                    page: 1,
                    pageSize: item.limit,
                    showType: "mobile"
                }).then(res => {
                    if (res.data.code === "00000") {
                        state.serviceData = res.data.info.records;
                    }
                });
            }
            const showDetail = (app) => {
                // window.open(app.redirectUrl, '_blank')
                showLoadingToast({
                    message: '正在跳转...',
                    forbidClick: true,
                    duration: 0
                });
                console.log(`app===========`,app)
                if (app.redirectUrl && app.redirectUrl !== '') {
                    if (state.userInfo) {
                        // let openUrl = app.redirectUrl.replace("#{humancode}", state.userInfo.humanCode);
                        // // window.open(openUrl, '_blank')
                        // window.location.href = openUrl
                        if(app.redirectUrl.indexOf("http") !== -1){

                            let openUrl = app.redirectUrl.replace("#{humancode}", state.userInfo.humancode);
                            // window.open(openUrl, '_blank')
                            window.location.href = openUrl
                            closeToast()
                        }else {
                            // let routeData = this.$router.resolve({path: app.redirectUrl});   //+'?name=自定义标题'
                            // window.open(app.redirectUrl, '_blank');
                            // window.location.href = app.redirectUrl
                            let openUrl = app.redirectUrl.split('#');
                            closeToast()
                            router.push({
                                path: openUrl[1],
                                // query: {
                                //     title:props.item.data.name,
                                //     type:'yingyongPT'
                                // }
                            });
                            closeToast()
                        }

                        // let openUrl = app.redirectUrl.split('#');
                        // closeToast()
                        // router.push({
                        //     path: openUrl[1],
                        //     // query: {
                        //     //     title:props.item.data.name,
                        //     //     type:'yingyongPT'
                        //     // }
                        // });
                    }
                } else {
                    // window.location.href = app.redirectUrl
                    closeToast()
                }
                // setTimeout(()=>{
                //     closeToast()
                // },2000)
            }
            // const ellipsis = () => {
            //     if (props.item.data.moreUrl && props.item.data.moreUrl !== '') {
            //         let routeData = router.resolve({name: '更多链接', query: {data: JSON.stringify(props.item.data)}});
            //         console.log(`routeData`, routeData)
            //         window.open(routeData.href, '_blank');
            //     }
            // }

            const handleClickMore = () => {
                router.push({
                    path: '/jichengxitong-list',
                    query: {
                        title:props.item.data.name,
                        type:'yingyongPT'
                    }
                });
            }
            return {
                ...toRefs(state),
                showDetail,
                // ellipsis,
                handleClickMore,
                handleImageError
            }
        },
        computed: {
            ...mapState(["baseUrl"])
        },
    };
</script>

<style lang="less" scoped>
    @import '../../common/style/mixin';
    @import '../../common/style/home';
    .thebox{
        height: 100%;
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
    }
    .service-row {
        overflow: hidden;
        font-size: 12px;
    }

    .title {
        border-top: 1px solid #eee;
    }

    .title span {
        margin: 10px 4px 0 10px;
        display: inline-block;
        height: 10px;
        width: 3px;
        background: #1a9fe4;
    }

    .title p {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        display: inline-block;
    }

    .index-sml {
        width: 25%;
        height: 60px;
        float: left;
        text-align: center;
        margin-bottom: 20px;
    }

    .index-sml img {
        width: 26px;
        height: 26px;
    }

    .index-sml p {
        margin-top: 10px;
    }

</style>

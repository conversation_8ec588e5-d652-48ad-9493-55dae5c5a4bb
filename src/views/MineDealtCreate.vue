<template>
    <section style="overflow-y: auto;">
        <!--        <s-header :name="'发起流程 - 【'+name+'】'"></s-header>-->
        <s-header :name="'发起流程 - 【'+name+'】'" :back="backUrl" :newParams="params"></s-header>
        <van-tabs v-model:active="active" color="#48a3ea" title-active-color="#48a3ea"
                  @click-tab="changeTab">
            <van-tab v-for="(tab,index) in tabsOptions"
                     :title="tab.text"
                     :name="tab.value"
                     :key="index">
            </van-tab>
        </van-tabs>
        <ZZ_Form style="padding-bottom: 80px;" ref="form" v-if="processInstance && processInstance.formType == 10 && active==='sq'&&setformObj" :issubform="issubform"
                 :fields="setfieldArr"
                 :setformObj="setformObj"
                 v-on:formsubmit="onFormSubmit"></ZZ_Form>
        <div v-if="processInstance && processInstance.formType == 20">
            <van-collapse v-model="activeNames">
                <van-collapse-item v-for="(item,index) in setfieldArr" :key="index" :title="item.name" :name="index+1">
                    <ZZ_Form style="padding-bottom: 80px;" ref="form" v-if="item.sfdt == '否' && active==='sq'&&setformObj" :issubform="issubform"
                             :fields="item.moblielist"
                             :setformObj="setformObj"
                             :gid="item.id"
                             @toformsubmit="toformsubmit"
                             v-on:formsubmit="onFormSubmit"></ZZ_Form>
                    <div v-if="item.sfdt == '是' && active==='sq'&&setformObj">
                        <div v-if="item.dictFieldList.length > 0" style="font-weight: bold;padding: 5px;margin-left: 5px;">
                            <van-tag plain type="primary" style="padding: 5px;" @click="formdadd(item,item.accountData)">
                                新增
                            </van-tag>
                        </div>
                        <div v-if="item.dictFieldList.length > 0 && item.accountData && item.accountData.length>0" >
                            <div v-for="(dicts,index) in item.accountData" :key="index" class="boxSty">
                                <div class="list-item-info">修改方式：{{dicts.xgfs == 'add' ? '新增' : (dicts.xgfs == 'edit' ? '编辑' : (dicts.xgfs == 'delete' ? '删除' : '')) }}</div>
                                <div v-for="(field,index) in item.dictFieldList" :key="index" class="list-item-info">{{field.fieldzh}}：{{dicts[field.fielden]}}</div>
                                <div class="item_right_btn">
                                    <van-tag style="margin-right: 8px;" plain type="danger" @click="formdelete(dicts,item)">
                                        删除
                                    </van-tag>
                                    <van-tag style="margin-right: 8px;" plain type="primary" @click="formdedit(dicts,item)">
                                        编辑
                                    </van-tag>
                                </div>
                            </div>
                        </div>
                    </div>
                </van-collapse-item>
            </van-collapse>
        </div>

        <div v-if="active==='lct'" style="background-color: #fff;">
            <my-process-viewer v-if="bpmnShow" key="designer" v-model="bpmnXML" :bpmnXML="bpmnXML"
                               v-bind="bpmnControlForm"/>
        </div>
        <div class="van-submit-bar">
            <van-button v-if="!resourShow" round type="primary" :disabled="submitshow" size="small" @click="handleAudit">提&nbsp;&nbsp;&nbsp;交<span v-if="submitshow">({{countdown}})</span>
            </van-button>
            <van-button v-if="resourShow" round type="primary" size="small" @click="handleSubmit">保&nbsp;&nbsp;&nbsp;存
            </van-button>
        </div>
        <van-popup v-model:show="showSelectPopup" position="bottom" :style="{ height: '80%',width:'101%' }">
            <SetRang :code="code"
                     :rang="rang"
                     :setMark="currentSelectType"
                     :nextNodesList="nextNodesList"
                     @update:checkChange="onCheckChange"
                     @update:handleRangCancle="selectedUser"></SetRang>
        </van-popup>
        <van-popup v-model:show="itemformshow" position="bottom" :style="{ height: '80%',width:'101%' }">
            <ZZ_Form v-if="itemformshow" style="padding-bottom: 80px;" ref="form"
                     :fields="itemformlist"
                     :setformObj="itemformdata"></ZZ_Form>
            <div class="van-submit-bar">
                <van-button round size="small" type="default" @click="onCancel">取消</van-button>
                <van-button round size="small" type="success" @click="onConfirm">确认</van-button>
            </div>
        </van-popup>
    </section>
</template>

<script>
    import sHeader from '@/components/SimpleHeader'
    import AjaxApi from "@/utils/api";
    import ZZ_Form from "@/components/form/ZZ_Form";
    import {diyGet, diyGet2, diyPost} from '@/service/home'
    import {GetBpmVariabledata, getProcessDefinition,} from "@/service/bpmProcessDefinition";
    import {mapState} from 'vuex'
    import {forEachFormJson,} from "@/utils/forEachFormJson"
    import {useRoute,} from "vue-router";
    import {computed, onMounted, reactive, toRefs, watch,} from "vue";
    import router from "@/router";
    import {showNotify, showFailToast, showSuccessToast, showToast, showConfirmDialog} from "vant";
    import AjaxApiPr from "@/service/publicResource";
    import {getLocal} from '@/common/js/utils'
    import SetRang from '@/components/Rang'

    export default {
        components: {
            ZZ_Form,
            sHeader,
            SetRang
        },
        setup() {
            const route = useRoute()
            const state = reactive({
                issubform: false,
                xgfs: null,
                currentId: null,
                activeNames: [1,2,3,4,5,6,7],
                showSelectPopup: false,
                code: [],
                changegid: null,
                rang: [],
                currentSelectType: null,
                handleUpdate: {},
                itemformlist: [],
                addformlist: [],
                itemformdata: {},
                nextNodesList: null,

                name: null,
                user: {},
                pageTotal: 0,
                active: 'sq',
                tabsOptions: [
                    {text: '申请信息', value: 'sq'},
                    // {text: '审批记录', value: 'sp'},
                    {text: '流程图', value: 'lct'},
                ],
                processInstance: null,
                formObj: {},
                xmlString: null,
                formVariables: {},
                changeformdata: {},
                submitformdata:{},
                dtxxlist:{},
                bpmnControlForm: {
                    prefix: "activiti"
                },
                bpmnXML: '',
                bpmnShow: false,
                itemformshow: false,
                fieldsPermission: {},
                moreBtnVisible: false,
                dialogVisible: false,
                approveButtons: [],
                approveInfo: null,
                runningTasks: [],
                showPickerReason: false,
                submitshow: false,
                reason: null,
                Expression: {},
                selectNextUserList: [],
                reasonStr: null,
                reasonColumns: [], // 我的常用意见
                reasonList: null,//我的常用意见list
                auditForms: [],
                setfieldArr: null,
                setformObj: null,
                activeTab: null,
                resourData: null,
                resourShow: false,
                params: null,
                backUrl: null,
                DefinitionKey: null,
                serviceLogBackId: null,
                serviceLogBackName: null,
                userInfo: null,
                countdown: 5, // 倒计时初始值为5秒
                timer: null,
            })

            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo

                const {id, name, activeTab, backId, backName, backUrl} = route.query
                if (route.query.resourData) {
                    state.resourData = JSON.parse(route.query.resourData)
                    state.resourShow = true
                }
                state.backUrl = backUrl ? backUrl : '/mine-service-recourse?isNavBar=true'
                state.currentId = id
                state.name = name
                state.activeTab = activeTab
                state.serviceLogBackId = backId
                state.serviceLogBackName = backName
                GetProcessDefinition()
            })

            const codes = computed(() => {
                let arr = [];
                if (state.rang) {
                    state.rang.forEach(item => {
                        arr.push(item.id)
                    })
                }
                return arr
            })

            watch(() => state.activeTab, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal === '预约') {
                    state.params = JSON.stringify({
                        setNewOldActiveTab: newVal,
                        id: state.serviceLogBackId,
                        name: state.serviceLogBackName
                    })
                }
            })

            const onCheckChange = (e) => {
                let userCode = e.id;
                if (codes.value.includes(userCode)) {
                    state.rang.splice(codes.value.indexOf(userCode), 1)
                } else {
                    state.rang.push(e)
                }
            }

            const selectedUser = () => {
                let users = [];
                state.rang.forEach(item => {
                    users.push(item.humancode)
                })
                if(state.selectNextUserList && state.selectNextUserList.length > 0){
                    state.selectNextUserList.forEach(item=>{
                        if (state.rang.length === 1) {
                            let data = state.rang[0];
                            item.userTaskFormVO.assignee = data.humancode;
                            item.userTaskFormVO.text = data.humanname;
                            item.userTaskFormVO.candidateUsers = null;

                        } else {
                            item.userTaskFormVO.candidateUsers = state.rang.map(k => k.humancode).join() || null;
                            item.userTaskFormVO.text = state.rang.map(k => k.humanname).join() || null;
                            item.userTaskFormVO.assignee = null;
                        }
                    })
                    let josndata = {
                        taskDefinitionKey: state.DefinitionKey,
                        processDefinitionId: state.processInstance.id,
                        processInstanceId: '',
                        variables: state.formVariables,
                        expressionVariables: state.Expression,
                        selectNextUserList: state.selectNextUserList,
                    }
                    diyPost(AjaxApi.bpmProcessInstanceExtcreate, josndata).then(response => {
                        if (response.data.code === '00000') {
                            if (state.userInfo.humanCode !== "syt_visitor") {
                                showSuccessToast("提交成功")
                                router.push({
                                    path: '/mine-dealt',
                                    query: {isNavbar: true, setNewOldActiveTab: 2}
                                });
                                // state.submitshow = true;
                            } else {
                                showSuccessToast("提交成功")
                                router.push({
                                    path: state.backUrl,
                                    query: {}
                                });
                                // state.submitshow = true;
                            }
                        } else {
                            showNotify({type: 'warning', message: response.data.info});
                            // state.submitshow = true;
                        }
                    })
                }
            }

            const GetProcessDefinition = () => {
                let json = {
                    definitionId: state.currentId
                }
                getProcessDefinition(json).then(res => {
                    if (res.status === 200 && res.data.code === '00000') {
                        state.processInstance = res.data.info;
                        console.log(`state.processInstance`,state.processInstance)
                        if (res.data.info && state.processInstance.formType == 20 && state.processInstance.formCustomCreatePath == "userInfocreateform") {
                            inituserFormObj();
                        } else {
                            initFormObj();
                        }
                    }
                })
            }

            const inituserFormObj = () => {
                // let arr = [];
                let fieldArr = [];
                let newFormObj = {};
                if (state.processInstance) {
                    diyGet(AjaxApi.getDetailbycode + `?humancode=` + state.userInfo.humanCode).then(res => {
                        newFormObj = res.data.data;
                        state.setformObj = newFormObj;
                        console.log(`newFormObj===`,newFormObj)
                    })

                    diyGet2(AjaxApi.allAndField).then(res => {
                        let resData = res.data.data;
                        fieldArr = resData;
                        fieldArr.forEach(item=>{
                            let newArr = [];
                            if(item.dictFieldList.length>0){
                                if(item.sfdt == '否'){
                                    item.dictFieldList.forEach(field=>{
                                        let dicData = [];
                                        if(field.fieldData || field.fieldAttribute){
                                            if(!field.fieldData){
                                                if(field.fieldAttribute){
                                                    let arr = field.fieldAttribute.split("#")
                                                    arr.forEach(i=>{
                                                        dicData.push({
                                                            label: i,
                                                            value: i,
                                                        })
                                                    })
                                                }
                                            }else {
                                                if(field.fieldAttribute){
                                                    // dicUrl = item.fieldData;
                                                    let arr = field.fieldAttribute.split("#")
                                                    diyPost(field.fieldData, {}).then(res => {
                                                        let resData = res.data;
                                                        console.log(`resData`,resData)

                                                        if (res.data.info && res.data.info.length>0) {
                                                            resData.info.forEach(i=>{
                                                                dicData.push({
                                                                    label: i[arr[0]],
                                                                    value: i[arr[1]],
                                                                })
                                                            })
                                                            console.log(`dicData`,dicData)
                                                        }
                                                    })
                                                }

                                            }
                                        }
                                        newArr.push({
                                            type: field.fieldType == 'upload' ? 'file-upload' : field.fieldType,
                                            values: null,
                                            options:{
                                                name: field.fielden,
                                                label: field.fieldzh,
                                                disabled: field.sfbj == '否',
                                                optionItems: dicData ? dicData : [],
                                            }
                                        })
                                    })
                                    item.moblielist = newArr;
                                }else {
                                    item.accountData = state.setformObj.accountListMap[item.id]
                                }

                            }
                            // item.moblielist = newArr;
                        })
                        state.setfieldArr = fieldArr;
                    })
                }
            }

            const initFormObj = () => {
                let arr = [];
                let fieldArr = [];
                let newFormObj = {}
                if (state.processInstance) {
                    state.formVariables.widgetList = []
                    state.processInstance.formFields.forEach(item => {
                        state.formVariables.widgetList.push(JSON.parse(item))
                    })
                    state.formVariables.formConfig = JSON.parse(state.processInstance.formConf);
                    console.log(`mobilefields`,state.processInstance.mobilefields)

                    GetBpmVariabledata({formFields: state.processInstance.mobilefields}).then(res => {
                        if (res.data.code != "00000") {
                            // this.$modal.msgError(res.data.info);
                            return;
                        }else {
                            console.log(`GetBpmVariabledata`,JSON.parse(res.data.info))
                            state.processInstance.mobilefields = JSON.parse(res.data.info);
                            if (state.processInstance.mobilefields) {
                                arr = state.processInstance.mobilefields
                                let newObj = {}
                                if (typeof arr[0] == 'string') {
                                    arr.forEach(item => {
                                        newObj = JSON.parse(item)
                                        fieldArr.push(newObj)
                                    })
                                } else {
                                    arr.forEach(item => {
                                        newObj = item
                                        fieldArr.push(newObj)
                                    })
                                }
                                if (state.processInstance.fieldsValues == String) {
                                    newFormObj = JSON.parse(state.processInstance.fieldsValues);
                                } else {
                                    newFormObj = state.processInstance.fieldsValues;
                                }

                                state.setfieldArr = fieldArr;
                                if (typeof newFormObj === 'string') {
                                    state.setformObj = JSON.parse(newFormObj)
                                } else {
                                    state.setformObj = newFormObj
                                }
                            } else {
                                forEachFormJson(state.formVariables.widgetList, arr)
                                let newObj = {}
                                if (typeof arr[0] == 'string') {
                                    arr.forEach(item => {
                                        newObj = JSON.parse(item)
                                        let setVal = newObj.values
                                        if (newObj.type === "radio" || newObj.type === "checkbox" || newObj.type === "select") setVal = newObj.values ? String(newObj.values) : null
                                        fieldArr.push(newObj)
                                        newFormObj[newObj.options.name] = setVal;
                                    })
                                } else {
                                    arr.forEach(item => {
                                        newObj = item
                                        let setVal = newObj.values
                                        if (newObj.type === "radio" || newObj.type === "checkbox" || newObj.type === "select") setVal = newObj.values ? String(newObj.values) : null
                                        fieldArr.push(newObj)
                                        newFormObj[newObj.options.name] = setVal;
                                    })
                                }
                                state.setfieldArr = fieldArr;
                                if (typeof newFormObj === 'string') {
                                    state.setformObj = JSON.parse(newFormObj)
                                } else {
                                    state.setformObj = newFormObj
                                }
                            }
                        }
                        console.log(`111111111`,state.setfieldArr,state.setformObj)

                    })

                }
                // state.setfieldArr = fieldArr;
                // if (typeof newFormObj === 'string') {
                //     state.setformObj = JSON.parse(newFormObj)
                // } else {
                //     state.setformObj = newFormObj
                // }
                // console.log(`newFormObj`,newFormObj)
            }

            const getBpmnXml = () => {
                let json = {
                    id: state.currentId
                }
                diyPost(AjaxApi.getBpmnXml, json).then(res => {
                    if (res.status === 200 && res.data.code === '00000') {
                        state.bpmnXML = res.data.info
                        state.bpmnShow = true
                    }
                })
            }

            const changeTab = (tab) => {
                state.active = tab.name
                if (tab.name === 'lct') {
                    getBpmnXml();
                } else {
                    // GetProcessDefinition();
                }
            }

            const getRegExp = function (validatorName) {
                const commonRegExp = {
                    number: /^\\d+(\\.\\d+)?$/,
                    letter: /^[A-Za-z]+$/,
                    letterAndNumber: /^[A-Za-z0-9]+$/,
                    mobilePhone: /^[1][3-9][0-9]{9}$/,
                    letterStartNumberIncluded: /^[A-Za-z]+[A-Za-z\\d]*$/,
                    noChinese: /^[^\u4e00-\u9fa5]+$/,
                    chinese: /^[\u4e00-\u9fa5]+$/,
                    email: /^([-_A-Za-z0-9.]+)@([_A-Za-z0-9]+\\.)+[A-Za-z0-9]{2,3}$/,
                    url: '/^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$/',
                }
                return commonRegExp[validatorName]
            }

            const handleAudit = () => {
                state.submitshow = true;
                state.countdown = 5;
                state.timer = setInterval(() => {
                    if (state.countdown > 0) {
                        state.countdown--;
                    } else {
                        clearInterval(state.timer); // 清除定时器
                    }
                }, 1000);
                setTimeout(()=>{
                    state.submitshow = false;
                },5000)

                let submit = true
                console.log(`state.setfieldArr`,state.setfieldArr)
                console.log(`state.setformObj`,state.setformObj)
                if(state.processInstance.formType == 10){
                    state.setfieldArr.forEach(item =>{
                        if( item.options.required ){
                            console.log(`item`,item)
                            if(item.options.validation  && item.options.validation!==''){
                                if(!getRegExp(item.options.validation).test(state.setformObj[item.options.name])){
                                    showFailToast("请填写"+item.options.label)
                                    submit = false
                                }
                            }
                            if(!state.setformObj[item.options.name] || state.setformObj[item.options.name] == '' || state.setformObj[item.options.name] == []){
                                console.log(state.setformObj,item.options.name)
                                submit = false
                                showFailToast("请按要求填写"+item.options.label)
                            }
                        }
                    })
                }

                if(submit){
                    let expression = {}
                    if (state.processInstance.conditionIds || state.processInstance.processDefinition.conditionIds) {
                        let conditionIds = JSON.parse(state.processInstance.processDefinition ? state.processInstance.processDefinition.conditionIds : state.processInstance.conditionIds);
                        conditionIds.forEach(item => {
                            let data = null;
                            if(Array.isArray( state.setformObj[item])){
                                data = state.setformObj[item].join(',');
                            }else {
                                data = state.setformObj[item]
                            }
                            expression[item] = data
                        })
                    }
                    console.log(`state.setformObj`,state.setformObj)
                    console.log(`expression`,expression)
                    if(state.processInstance.formType == 10){
                        state.formVariables.mobilefields = state.setfieldArr;
                        state.formVariables.fieldsValues = state.setformObj;
                    }else {
                        state.formVariables.formCustomCreateData = state.submitformdata;
                        state.formVariables.mobilefields = state.processInstance.mobilefields;
                        state.formVariables.formConfig = state.processInstance.formConfig;
                        state.formVariables.widgetList = state.processInstance.widgetList;
                        state.formVariables.fieldsValues = state.processInstance.fieldsValues;

                    }


                    let obj = {
                        expressionVariables: expression,
                        definitionKey: state.processInstance.key,
                        definitionId: state.processInstance.id,
                        returnTaskDefKey: ""
                    }
                    let id = state.processInstance.id;
                    // const data = {
                    //     processDefinitionId: state.processInstance.id,
                    //     variables: state.formVariables,
                    //     expressionVariables: expression,
                    // }


                    let json = {
                        definitionId: id,
                        // processInstanceId: state.processInstance.id
                    }
                    diyPost('/bpmTask/getFirstUserTask', json).then(res => {
                        if (res.data.code === '00000') {
                            let data = res.data.info;
                            let definitionKey = data.id;
                            let returnTaskDefKey = data.documentation;
                            let extendData = data.attributes.extendData;
                            let flag = false;
                            // 流程驳回标识，驳回到发起人，选择跳转审批到驳回节点时，不判断发起人节点是否配置可选下一节点办理人，只要跳转的节点设置为【允许手动选择下一节点办理人】都会弹出选择下一节点办理人
                            let returnFlag = !!returnTaskDefKey;
                            if (extendData && extendData.length > 0) {
                                let valArr = JSON.parse(extendData[0].value);
                                if (valArr.length > 0) {
                                    if (valArr[0] && valArr[0].changeuser) {
                                        flag = false;
                                    } else {
                                        flag = true;
                                    }
                                } else {
                                    flag = true;
                                }
                            } else {
                                flag = true;
                            }
                            if (returnFlag) {
                                flag = false;
                            }
                            if (!flag) {
                                diyPost('/bpmTask/getNextNodesByProcessDefinitionIdAndTaskDefinitionKey', {
                                    id: obj.taskId,
                                    expressionVariables: obj.expressionVariables,
                                    definitionKey: definitionKey,
                                    definitionId: obj.definitionId,
                                    returnTaskDefKey: returnTaskDefKey,
                                }).then(result => {
                                    console.log(`flag1`, flag)
                                    if (result.data.code != "00000") {
                                        showToast({type: 'warning', message: '操作失败'});
                                        return;
                                    }
                                    let info = result.data.info
                                    let nextUserList = [];
                                    info.forEach((item) => {
                                        nextUserList.push({
                                            taskKey: item.id,
                                            taskName: item.name,
                                            userTaskFormVO: {
                                                dataType: 'USERS',
                                                assignee: '',
                                                candidateUsers: '',
                                                candidateGroups: '',
                                                text: '',
                                            }
                                        })
                                    })
                                    state.selectNextUserList = nextUserList;
                                    state.InstanceId = data.instanceId;
                                    state.DefinitionId = data.definitionId;
                                    state.DefinitionKey = definitionKey;
                                    state.Expression = obj.expressionVariables;
                                    console.log(`state.Expression`,state.Expression)
                                    state.nextNodesList = info;
                                    if (nextUserList.length > 0) {
                                        state.showSelectPopup = true;
                                    } else {
                                        // 流程模块的提交
                                        let josndata = {
                                            taskDefinitionKey: definitionKey,
                                            selectNextUserList: [],
                                            processDefinitionId: id,
                                            processInstanceId: '',
                                            variables: state.formVariables,
                                            expressionVariables: state.Expression,
                                        }
                                        diyPost(AjaxApi.bpmProcessInstanceExtcreate, josndata).then(response => {
                                            if (response.data.code === '00000') {
                                                if (state.userInfo.humanCode !== "syt_visitor") {
                                                    showSuccessToast("提交成功")
                                                    router.push({
                                                        path: '/mine-dealt',
                                                        query: {isNavbar: true, setNewOldActiveTab: 2}
                                                    });
                                                    // state.submitshow = true;
                                                } else {
                                                    showSuccessToast("提交成功")
                                                    router.push({
                                                        path: state.backUrl,
                                                        query: {}
                                                    });
                                                    // state.submitshow = true;
                                                }
                                            } else {
                                                showNotify({type: 'warning', message: response.data.info});
                                                // state.submitshow = true;
                                            }
                                        })
                                    }
                                });
                            }
                            if (flag) {
                                // 流程模块的提交
                                let josndata = {
                                    taskDefinitionKey: definitionKey,
                                    selectNextUserList: [],
                                    processDefinitionId: id,
                                    processInstanceId: '',
                                    variables: state.formVariables,
                                    expressionVariables: state.Expression,
                                }
                                diyPost(AjaxApi.bpmProcessInstanceExtcreate, josndata).then(response => {
                                    if (response.data.code === '00000') {
                                        if (state.userInfo.humanCode !== "syt_visitor") {
                                            showSuccessToast("提交成功")
                                            router.push({
                                                path: '/mine-dealt',
                                                query: {isNavbar: true, setNewOldActiveTab: 2}
                                            });
                                            // state.submitshow = true;
                                        } else {
                                            showSuccessToast("提交成功")
                                            router.push({
                                                path: state.backUrl,
                                                query: {}
                                            });
                                            // state.submitshow = true;
                                        }
                                    } else {
                                        showNotify({type: 'warning', message: response.data.info});
                                        // state.submitshow = true;
                                    }
                                })
                            }
                        }
                    })

                }else {
                    // showFailToast('请按要求填写表单')
                    // state.submitshow = true;
                }


            }

            const handleSubmit = () => {
                let expression = {}
                if (state.processInstance.conditionIds || state.processInstance.processDefinition.conditionIds) {
                    let conditionIds = JSON.parse(state.processInstance.processDefinition ? state.processInstance.processDefinition.conditionIds : state.processInstance.conditionIds);

                    conditionIds.forEach(item => {
                        let data = null;
                        if(Array.isArray( state.setformObj[item])){
                            data = state.setformObj[item].join(',');
                        }else {
                            data = state.setformObj[item]
                        }
                        expression[item] = data
                    })
                }
                console.log(`state.setformObj`,state.setformObj)
                console.log(`expression`,expression)
                state.formVariables.mobilefields = state.setfieldArr;
                state.formVariables.fieldsValues = state.setformObj;

                const data = {
                    processDefinitionId: state.processInstance.id,
                    variables: state.formVariables,
                    expressionVariables: expression,
                }
                const context = state.resourData
                context.processInstance = data
                console.log('context=====', context)
                diyPost(AjaxApiPr.Edit, context).then(res => {
                    if (res.data.code == "00000") {
                        showSuccessToast("新增成功！");
                        router.push({
                            path: 'mine-service-recourse',
                            query: {
                                setNewOldActiveTab: '预约记录',
                                id: state.serviceLogBackId,
                                name: state.serviceLogBackName
                            }
                        });

                    } else {
                        showFailToast(res.data.info)
                    }
                })
            }

            const formdelete = (dicts,field) => {
                console.log(`dicts`,dicts)
                console.log(`field`,field)
                state.itemformdata = dicts;
                showConfirmDialog({
                    title: '',
                    message: '确定要删除?                                          ',
                }).then(() => {
                    state.itemformdata.xgfs = 'delete';
                    if(!state.dtxxlist[field.id]){
                        state.dtxxlist[field.id] = []
                    }
                    state.dtxxlist[field.id].push(state.itemformdata);
                    let newdata = {
                        tmform: state.changeformdata,
                        dtxxlist: state.dtxxlist
                    }
                    state.submitformdata = newdata;
                })
            }

            const formdedit = (dicts,item) => {
                console.log(`dicts`,dicts)
                console.log(`item`,item)
                state.itemformdata = dicts;
                state.changegid = item.id;
                let newArr = [];
                state.itemformlist = []
                item.dictFieldList.forEach(i=>{
                    let dicData = [];
                    if(i.fieldData || i.fieldAttribute){
                        if(!i.fieldData){
                            if(i.fieldAttribute){
                                let arr = i.fieldAttribute.split("#")
                                arr.forEach(i=>{
                                    dicData.push({
                                        label: i,
                                        value: i,
                                    })
                                })
                            }
                        }else {
                            if(i.fieldAttribute){
                                // dicUrl = item.fieldData;
                                let arr = i.fieldAttribute.split("#")
                                diyPost(i.fieldData, {}).then(res => {
                                    let resData = res.data;
                                    if (res.data.info && res.data.info.length>0) {
                                        resData.info.forEach(j=>{
                                            dicData.push({
                                                label: j[arr[0]],
                                                value: j[arr[1]],
                                            })
                                        })
                                        console.log(`dicData`,dicData)
                                    }
                                })
                            }

                        }
                    }
                    newArr.push({
                        type: i.fieldType,
                        values: null,
                        options:{
                            name: i.fielden,
                            label: i.fieldzh,
                            disabled: i.sfbj == '否',
                            optionItems: dicData ? dicData : [],
                        }
                    })
                })
                state.itemformlist = newArr;
                state.xgfs = 'edit'
                state.itemformshow = true;
            }

            const formdadd = (item,accountData) => {
                console.log(`item`,item)
                state.itemformdata = {
                    xgfs: 'add',
                };
                state.changegid = item.id;
                if(!accountData){
                    accountData = [];
                }
                state.addformlist = accountData
                let newArr = [];
                state.itemformlist = []

                item.dictFieldList.forEach(i=>{
                    let dicData = [];
                    if(i.fieldData || i.fieldAttribute){
                        if(!i.fieldData){
                            if(i.fieldAttribute){
                                let arr = i.fieldAttribute.split("#")
                                arr.forEach(i=>{
                                    dicData.push({
                                        label: i,
                                        value: i,
                                    })
                                })
                            }
                        }else {
                            if(i.fieldAttribute){
                                // dicUrl = item.fieldData;
                                let arr = i.fieldAttribute.split("#")
                                diyPost(i.fieldData, {}).then(res => {
                                    let resData = res.data;
                                    if (res.data.info && res.data.info.length>0) {
                                        resData.info.forEach(j=>{
                                            dicData.push({
                                                label: j[arr[0]],
                                                value: j[arr[1]],
                                            })
                                        })
                                        console.log(`dicData`,dicData)
                                    }
                                })
                            }

                        }
                    }
                    newArr.push({
                        type: i.fieldType,
                        values: null,
                        options:{
                            name: i.fielden,
                            label: i.fieldzh,
                            disabled: i.sfbj == '否',
                            optionItems: dicData ? dicData : [],
                        }
                    })
                })
                state.itemformlist = newArr;
                state.xgfs = 'add'
                state.itemformshow = true;
            }

            const onCancel = () => {
                state.itemformshow = false;
            }

            const onConfirm = () => {
                state.itemformdata.xgfs = state.xgfs;
                if(!state.dtxxlist[state.changegid]){
                    state.dtxxlist[state.changegid] = []
                }
                state.dtxxlist[state.changegid].push(state.itemformdata);
                if(state.xgfs == 'add') {
                    state.addformlist.push(state.itemformdata)
                    state.setfieldArr.forEach(item=>{
                        if(item.id == state.changegid){
                            if(!item.accountData){
                                item.accountData = [];
                                item.accountData.push(state.itemformdata)
                            }
                        }
                    })
                }
                let newdata = {
                    tmform: state.changeformdata,
                    dtxxlist: state.dtxxlist
                }
                state.submitformdata = newdata;
                state.itemformshow = false;
                console.log(`onConfirm`,state.changegid)

                console.log(`onConfirm`,state.itemformdata)
                console.log(`onConfirm`,state.setfieldArr)

            }

            const toformsubmit = (data) => {
                let param = {}
                param[data.key] = data.value
                // state.changeformdata
                if(!state.changeformdata[data.gid]){
                    state.changeformdata[data.gid] = {}
                }
                state.changeformdata[data.gid] = Object.assign(state.changeformdata[data.gid],param);
                let newdata = {
                    tmform: state.changeformdata,
                    dtxxlist: state.dtxxlist
                }
                state.submitformdata = newdata;
                console.log(`state.submitformdata========`,state.submitformdata)
            }

            return {
                ...toRefs(state),
                changeTab,
                handleAudit,
                handleSubmit,
                selectedUser,
                onCheckChange,
                formdelete,
                formdedit,
                formdadd,
                onCancel,
                onConfirm,
                toformsubmit
            }
        },
        computed: {
            ...
                mapState(["userInfo"]),
        },
    };
</script>

<style scoped>
    .boxSty{
        border: 1px solid rgba(220, 220, 220, 0.52);
        padding: 10px;
        border-radius: 10px;
        position: relative;
        margin-bottom: 10px;
    }
    .item_right_btn{
        position: absolute;
    }
    .wrap {
        width: 100%;
        height: 100vh;
        overflow-y: scroll;
        padding-bottom: 68px;
        background: #f5f5f5;
    }

    .van-tabs >>> .van-tabs__wrap {
        height: 50px;
    }

    .van-tabs >>> .van-tab {
        line-height: 50px;
        font-size: 16px;
    }

    .van-tabs >>> .van-tabs__content {
        background: #fff;
        border-top: 6px solid #f5f5f5;
    }


    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }

</style>

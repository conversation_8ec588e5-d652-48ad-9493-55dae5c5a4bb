<!-- src/views/Login.vue -->
<template>
    <div class="login-container">
        <div style="background-color: #00a5ec;height: 120px;" class="titleback">
            <p class="weui-form__tips" style="line-height: 120px;margin: 0 0 0 50px;color: white;font-size: 24px;font-weight: bold;">
<!--                请输入姓名身份证号验证身份-->
                身份校验
            </p>
        </div>
        <div class="form-container">
            <van-field
                v-model="xm"
                placeholder="姓名"
                label="姓名"
                required
            />
            <van-field
                v-model="sfzh"
                placeholder="身份证号"
                label="身份证号"
                required
            />
            <div class="captcha-container">
                <van-field
                    v-model="captchaInput"
                    placeholder="请输入验证码"
                    label="验证码"
                    required
                />
                <img :src="captchaUrl" alt="验证码" class="captcha-image" @click="refreshCaptcha" />
            </div>
            <div style="text-align: center;margin-top: 20px;">
                <van-button style="width: 100px;" type="primary" @click="handleSubmit">验证</van-button>
            </div>

            <van-toast v-model="errorMessage" type="fail" />
        </div>
    </div>
</template>

<script>
import {diyPost,diyGet2} from '@/service/home'
import AjaxApi from "@/service/dianhua";
import {mapState} from "vuex";
import {showToast} from "vant";
// import axios from 'axios'

export default {
    data() {
        return {
            username: '',
            password: '',
            captchaInput: '',
            captchaUrl: null,
            errorMessage: '',
            xm: '',
            sfzh: '',
        };
    },
    created() {
        this.refreshCaptcha()
    },
    computed: {
        ...mapState(["baseUrl"])
    },
    methods: {
        async refreshCaptcha(){
            await diyGet2('/platform/qywx/user/captcha').then(res=>{
                this.captchaUrl = res.data.data;
                console.log(`diyGet2`,this.captchaUrl)
            })
        },
        handleSubmit() {
            if(!this.sfzh){
                showToast({type: 'warning', message: '请输入身份证号'});
                return
            }
            if(!this.xm){
                showToast({type: 'warning', message: '请输入姓名'});
                return
            }
            if(!this.captchaInput){
                showToast({type: 'warning', message: '请输入验证码'});
                return
            }
            // 要编码的字符串
            const idCard = this.sfzh;
            const name = this.xm;

            // 合并字符串
            const inputString = `${idCard}&#${name}`;

            // 进行 URL 编码
            const urlEncodedString = encodeURIComponent(inputString);

            // 进行 Base64 编码
            const base64EncodedString = btoa(urlEncodedString);

            console.log(base64EncodedString);

            // let json = {
            //     validInfo: base64EncodedString,
            //     yzm: this.captchaInput,
            // }
            diyPost(AjaxApi.verify+`?validInfo=${base64EncodedString}&yzm=${this.captchaInput}`,).then(res => {
                console.log(res)
                if(res.data.status){
                    this.$router.push({path: '/dianhua2'})
                }else {
                    this.errorMessage = res.data.msg;
                    showToast({type: 'warning', message: res.data.msg});
                }
            })
        },
    },
};
</script>

<style scoped>
.titleback{
    background-image: url("../assets/u555.png");
    background-repeat: no-repeat;
    background-size: 50% 100%;
    background-position: right;
}
.login-container {
    /*padding: 20px;*/
}
.form-container {
    margin: 20px;
}
.captcha-container {
    display: flex;
    align-items: center;
}
.captcha-image {
    margin-left: 10px;
    cursor: pointer;
    width: 100px;
    height: 40px;
    border: 1px solid #ccc;
}
</style>

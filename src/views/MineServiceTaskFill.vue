<template>
    <div>
        <div class="product-list-content">
            <header class="category-header wrap">
                <i class="nbicon nbfanhui" @click="goBack"></i>
                <span style="width: 130px;padding: 0 6px;overflow: hidden;font-weight: bold;">{{currentName}}</span>
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入搜索关键词"
                           type="text"
                           class="search-title"
                           v-model="searchName"/>
                </div>
            </header>

            <van-tabs v-model:active="active" color="#48a3ea" title-active-color="#48a3ea"
                      @click-tab="changeTab">
                <van-tab v-for="tab in tabsOptions" :key="tab.value" :title="tab.text">
                </van-tab>
            </van-tabs>
        </div>
        <div class="content">
            <list style="margin-bottom: 60px;" :url="url" :queryObj="queryObj">
                <template v-slot:default="slotProps">
                    <van-cell v-for="(item,index) in slotProps.list" :key="index">
                         <span class="item_right_top">
                             {{dateFormat_YHMHM(item.createTime)}}
                         </span>
                        <div style="display: flex;justify-content: space-between;"
                             class="  cell-class-name">
                            <div>
                                <div class="list-item-name">
                                    <!--                                    [{{setNewCategoryVal(item.categoryId)}}] -->
                                    {{ item.name }}
                                </div>
                                <template v-if="item.startTime&& item.endTime">
                                    <div class="list-item-info">任务时间：</div>
                                    <div>
                                        {{item.startTime}}<br/>
                                    </div>
                                    <div>
                                        {{item.endTime}}<br/>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="list-item-info">任务时间：无限制</div>
                                </template>
                                <div class="list-item-info">审核流程:{{setNewProcessVal(item.processDefinitionKey)}}</div>
                                <div class="list-item-info">任务方式:{{item.style==='10'?'表单+流程':'表单'}}</div>
                                <div class="list-item-info">任务状态:{{item.runStatus==='1'?'进行中':'已暂停'}}</div>
                                <div class="list-item-info">填报状态:{{item.status==='F'?'未上报':'已上报'}}</div>
                                <div class="list-item-info">已读状态:{{item.readStatus==='1'?'已读':'未读'}}</div>
                                <div class="list-item-info">备注:{{item.remark}}</div>

                                <div class="item_right_btn">
                                    <van-tag @click="taskreported(item)" v-if="item.status==='F'&&item.runStatus==='1'"
                                             type="success" plain>
                                        上报
                                    </van-tag>
                                    <van-tag @click="taskhandleDetail(item)" v-if="item.status==='T'" type="primary"
                                             plain>
                                        查看
                                    </van-tag>
                                </div>
                            </div>
                        </div>
                    </van-cell>
                </template>
            </list>
        </div>
    </div>
</template>

<script>
    import {useRoute,} from "vue-router";
    import {onMounted, reactive, toRefs, watch} from "vue";
    import AjaxApi from "@/service/publicResource";
    import {diyPost} from '@/service/home'
    import Utils from "@/utils/momentWrap";
    import router from "@/router";
    import {getLocal} from '@/common/js/utils'
    import {mapState} from "vuex";
    import List from "@/components/List";
    import {getProcessDefinitionList, queryOne} from "@/service/bpmProcessDefinition";
    import {showFailToast,} from "vant";

    export default {
        components: {List},
        computed: {
            ...mapState(["baseUrl", "defaultSearchIcon"]),
        },
        setup() {
            const route = useRoute()
            const state = reactive({
                url: AjaxApi.sytTaskProjectQueryPage,
                queryObj: {},
                currentChkMark: 'current',

                currentId: null,
                currentName: null,
                searchName: null,
                showSearchPopup: false,
                userInfo: null,
                resourceKey: null,
                resourceId: null,
                categoryIdsData: null,
                processlist: null,
                active: 0,  // 转换为number类型
                tabsOptions: [
                    {text: "未开始", value: "3"},
                    {text: "进行中", value: "1"},
                    {text: "已暂停", value: "0"},
                    {text: "已结束", value: "2"},
                ],
            })
            onMounted(async () => {
                getSelectList()
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                const {taskId, name, setNewOldActiveTab} = route.query
                state.active = setNewOldActiveTab ? Number(setNewOldActiveTab) : 0;
                if (taskId) {
                    const index = taskId.split('=')
                    state.resourceKey = index[0]
                    state.resourceId = index[1]
                    state.currentId = taskId
                }
                state.currentName = name
                setQueryParam()
            })
            watch(() => state.taskId, () => {
                setQueryParam()
            })
            watch(() => state.searchName, () => {
                setQueryParam()
            })

            const getSelectList = () => {
                diyPost(AjaxApi.sytCodeTbrwQueryList, {}).then(res => {
                    if (res.status === 200) {
                        state.categoryIdsData = res.data.info;
                    }
                })
                getProcessDefinitionList({suspensionState: 1}).then(res => {
                    if (res.data.code == "00000") {
                        if (res.data.code == "00000") {
                            state.processlist = res.data.info
                        }
                    }
                })
            }
            const setNewCategoryVal = (val) => {
                let newVal = []
                if (val && state.categoryIdsData) {
                    newVal = state.categoryIdsData.filter(item => {
                        return item["id"] === val.toString()
                    })
                }
                let newStr = newVal.length > 0 ? newVal[0].name : ''
                return newStr;
            }
            const setNewProcessVal = (val) => {
                let newVal = []
                if (val && state.processlist) {
                    newVal = state.processlist.filter(item => {
                        return item["key"] === val.toString()
                    })
                }
                let newStr = newVal.length > 0 ? newVal[0].name : ''
                return newStr;
            }
            const dateFormat_YHMHM = (o) => {
                return Utils.dateFormat_YHMHM(o);
            }
            const goBack = () => {
                router.push({path: 'mine-service'})
            }

            const setQueryParam = () => {
                let newTabType = state.tabsOptions[state.active].value
                state.queryObj = {
                    queryParam: {
                        isStyle: "0,2",
                        runStatus: newTabType
                    }
                }
                if (state.resourceKey === 'projectId') state.queryObj.queryParam.id = state.resourceId
                if (state.resourceKey === 'groupBy') state.queryObj.queryParam.categoryId = state.resourceId
                state.queryObj.queryParam.name = state.searchName
            }

            const taskreported = (item) => {
                queryOne({projectId: item.id}, '/sytTaskPerson').then(response => {
                    if (response.data.code == "00000") {
                        if (response.data.info.project) {
                            router.push({
                                path: "/mine-service-task-edit",
                                query: {
                                    project: JSON.stringify(response.data.info.project),
                                    name: item.name,
                                    backId: state.currentId,
                                    activeType: '任务填报',
                                    backUrl: '/mine-service-taskFill'
                                }
                            })
                        }
                    } else {
                        showFailToast(response.data.info);
                    }
                })
            }
            const taskhandleDetail = (item) => {
                queryOne({projectId: item.id}, '/sytTaskFillRecord').then(res => {
                    if (!res.data.info) {
                        showFailToast("未查询到填报记录")
                    } else if (res.data.code === "00000") {
                        if (item.style === "10") {// 带流程
                            router.push({
                                path: '/mine-dealt-detail',
                                query: {
                                    name: '填报详情-' + item.name,
                                    currentId: res.data.info.processInstId,
                                    backId: state.currentId,
                                    backName: item.name,
                                    activeTab: state.active,
                                    activeType: '任务填报',
                                    backUrl: '/mine-service-taskFill'
                                }
                            });
                        } else if (item.style === "20") {// 表单
                            router.push({
                                path: "/mine-service-task-detail",
                                query: {
                                    name: '填报详情-' + item.name,
                                    id: item.id,
                                    taskId: state.currentId,
                                    activeTab: state.active,
                                    activeType: 4,
                                    backUrl: '/mine-service-taskFill'
                                }
                            });
                        }
                    } else if (res.data.code === "00001") {
                        showFailToast(res.data.info)
                    }
                })
                // return Utils.dateFormat_YHMHM(o);
            }

            const changeTab = (tab) => {
                state.active = Number(tab.name)
                setQueryParam()
            }
            return {
                ...toRefs(state),
                changeTab,
                goBack,
                dateFormat_YHMHM,
                setNewCategoryVal,
                setNewProcessVal,
                taskreported,
                taskhandleDetail,
            }
        }
    }
</script>

<style lang="less">
    @import '../common/style/mixin';

    .cell-class-name {
        color: #2c3e50;
        text-align: left;
    }

    .content {
        height: calc(~"(100vh - 90px)");
        overflow: hidden;
        overflow-y: scroll;
        margin-top: 98px;
        /*margin-bottom: 76px;*/
    }

    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }

    .tqm-div {
        display: inline-block;
        width: 46%;
        margin-right: 10px;
        margin-bottom: 6px;
    }

    .tqm-div-time {
        width: 100%;
        height: 30px;
        border-radius: 5px;
        line-height: 30px;
        text-align: center;
        /*color: #868686;*/
    }

    .van-button--default {
        border: var(--van-button-border-width) solid var(--van-button-default-border-color) !important;
    }

    .mark-div {
        display: flex;
        justify-content: space-around;
        width: 100%;
        margin-top: 15px;

        .mark-div-content {
            display: inline-block;
            width: 100%;

            .mark-div-content-color {
                height: 15px;
                width: 15px;
                display: inline-block;
                border-radius: 2px
            }

            .mark-div-content-span {
                vertical-align: top;
            }
        }
    }

    .product-list-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;

        .category-header {
            .fj();
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding: 0 15px;
            .boxSizing();
            font-size: 15px;
            color: #656771;
            z-index: 10000;

            &.active {
                background: @primary;
            }

            .icon-left {
                font-size: 25px;
                font-weight: bold;
            }

            .header-search {
                display: flex;
                width: 88%;
                height: 20px;
                line-height: 20px;
                margin: 10px 0;
                padding: 5px 0;
                color: #232326;
                background: #F7F7F7;
                .borderRadius(20px);

                .nbSearch {
                    padding: 0 5px 0 20px;
                    font-size: 17px;
                }

                .search-title {
                    font-size: 14px;
                    color: #666;
                    background: #F7F7F7;
                }
            }

            .icon-More {
                font-size: 20px;
            }

            .header-right {
                display: flex;
                width: 16%;
                margin-left: 20px;

                .right-icon {
                    margin-top: 15px;
                }

                .right-div {
                    font-size: 14px;
                    color: #666;
                    padding-top: 1px;
                }
            }
        }
    }

    .checktime {
        margin: 15px;

        .chktime-span {
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
        }

        span {
            vertical-align: -webkit-baseline-middle;
        }

        .van-button {
            float: right;
            border: 1px solid #dcdee0 !important;
            margin-left: 5px;
        }
    }

    .contents_box_timeQuantunMark {
        width: 15px;
        height: 15px;
        display: inline-block;
        margin-right: 4px
    }

    .van-tag--plain::before {
        border: unset !important;
    }

    .popup_content {
        padding: 15px;

        .popup_content_title {
            border-bottom: 1px solid rgba(138, 144, 156, 0.37);
        }

        .popup_checktime {
            margin-top: 10px;

            .chktime-span {
                font-size: 14px;
                font-weight: bold;
                margin: 10px 0;
            }

            span {
                vertical-align: -webkit-baseline-middle;
            }

            .van-button {
                float: right;
                border: 1px solid #dcdee0 !important;
                margin-left: 5px;
            }
        }
    }


</style>

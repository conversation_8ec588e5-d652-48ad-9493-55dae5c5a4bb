<template>
    <div class="product-list-wrap">
        <div>
            <s-header :name="name"></s-header>
        </div>
        <div class="product-list-content">
            <header class="category-header wrap">
                <div class="header-left" @click="showAddPopup=true;itemformdata = {}">
                    <van-icon class="right-icon" name="add" size="18" />
                    <div class="right-div">
                        添加
                    </div>
                </div>
                <div class="header-right" @click="showSearchPopup=true">
                    <van-icon class="right-icon" :name="defaultSearchIcon" size="18"/>
                    <div class="right-div">
                        筛选
                    </div>
                </div>
            </header>
        </div>
        <div class="content">
            <van-cell :key="index" v-for="(item,index) in list">
                <div>
                    <div class="list-item-info">账号：{{item.humancode}}</div>
                    <div v-for="field in List" :key="field.id" class="list-item-info">{{field.fieldzh}}：{{item[field.fielden]}}</div>
                    <div class="item_right_btn">
                        <van-tag plain type="danger" @click.stop="deleteitem(item)">
                            删除
                        </van-tag>
                        <van-tag plain type="primary" @click.stop="handleOptDetail(item)">
                            编辑
                        </van-tag>
                    </div>
                </div>
            </van-cell>
        </div>
        <van-popup v-model:show="showAddPopup" position="top" :style="{height:'auto',width:'100%',top: '44px'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <ZZ_Form style="padding-bottom: 80px;" ref="form"
                             :fields="itemformlist"
                             :setformObj="itemformdata"></ZZ_Form>
                </van-cell-group>
            </div>
            <div class="van-submit-bar-search">
                <van-button type="primary" round size="small" @click="handleAdd"> 保&nbsp;&nbsp;存
                </van-button>
            </div>
        </van-popup>
        <van-popup v-model:show="showSearchPopup" position="top" :style="{height:'auto',width:'100%',top: '44px'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <div>
                        <van-field label="账号" readonly/>
                        <van-field label="" v-model="jsh" placeholder="请输入账号"/>
                    </div>
                </van-cell-group>
            </div>
            <div class="van-submit-bar-search">
                <van-button type="primary" round size="small" @click="handleSearch"> 搜&nbsp;&nbsp;索
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
// import navBar from '@/components/NavBar'
import AjaxApi from "@/utils/api";
import Utils from "@/utils/momentWrap";
import {diyGet2, diyPost} from '@/service/home'
import { useRouter,useRoute} from "vue-router";
import {onMounted, reactive, toRefs} from "vue";
import {mapState} from "vuex";
import {showConfirmDialog, showFailToast, showSuccessToast} from "vant";
import sHeader from '@/components/SimpleHeader'
import ZZ_Form from "@/components/form/ZZ_Form";
// import {remindRuntimeTask} from "@/service/bpmProcessInstance";
// import router from "@/router";

export default {
    components: {
        // navBar,
        sHeader,
        ZZ_Form
    },
    computed: {
        ...mapState(["baseUrl", "defaultSearchIcon"]),
    },
    setup() {
        const router = useRouter()
        const route = useRoute()
        const state = reactive({
            minDate: new Date(2000, 0, 1),
            zxdate: [],
            list: [],
            itemformlist: [],
            itemformdata: {},
            jsh: null,
            processDefinitionId: null,
            hfztlist: [
                {
                    text: '已回复',
                    value: '已回复',
                },
                {
                    text: '待回复',
                    value: '待回复',
                }
            ],
            fl: null,
            activeTab: null,
            showhfzt: false,
            pageTotal: 0,
            url: AjaxApi.sytAccountListInfo,
            queryObj: {
                queryParam: {}
            },
            addobj:{

            },
            showtypesearch: false,
            theme: '',
            humanName: '',
            searchfl: null,
            searchflid: null,
            hfzt: null,
            createDate: null,
            sytSysDictKeyVal: [],
            searchName: null,
            showSearchPopup: false,
            showtype: false,
            showAddPopup: false,
            currentDate: new Date(),
            showKjDate: null,
            kssj: null,
            jssj: null,
            name: '',
            processDefinitionName: '',
            beginCreateTime: null,
            endCreateTime: null,
            showkssjPicker: false,
            showjssjPicker: false,
            categoryId: null,
            orgid: null,
            categoryIdsData: null,
            processlist: null,
            project: null,
            typelist: [],
            userInfo: null,
            code: null,
            List: [],
            changeitemdata: {},

        })
        onMounted(async () => {
            if(route.query.name){
                state.name = route.query.name;
            }
            if(route.query.code){
                state.code = route.query.code;
            }
            diyGet2(AjaxApi.allAndField).then(res => {
                let resData = res.data.data;
                resData.forEach(item=>{
                    if(item.code == state.code){
                        state.List = item.dictFieldList;
                        let newArr = [
                            {
                                // type: 'input',
                                values: null,
                                options:{
                                    name: 'humancode',
                                    label: '账号',
                                }
                            }
                        ];
                        item.dictFieldList.forEach(i=>{
                            let dicData = [];
                            if(i.fieldData || i.fieldAttribute){
                                if(!i.fieldData){
                                    if(i.fieldAttribute){
                                        let arr = i.fieldAttribute.split("#")
                                        arr.forEach(i=>{
                                            dicData.push({
                                                label: i,
                                                value: i,
                                            })
                                        })
                                    }
                                }else {
                                    if(i.fieldAttribute){
                                        // dicUrl = item.fieldData;
                                        let arr = i.fieldAttribute.split("#")
                                        diyPost(i.fieldData, {}).then(res => {
                                            let resData = res.data;
                                            if (res.data.info && res.data.info.length>0) {
                                                resData.info.forEach(j=>{
                                                    dicData.push({
                                                        label: j[arr[0]],
                                                        value: j[arr[1]],
                                                    })
                                                })
                                                console.log(`dicData`,dicData)
                                            }
                                        })
                                    }

                                }
                            }
                            newArr.push({
                                type: i.fieldType,
                                values: null,
                                options:{
                                    name: i.fielden,
                                    label: i.fieldzh,
                                    disabled: i.sfbj == '否',
                                    optionItems: dicData ? dicData : [],
                                }
                            })
                        })
                        state.itemformlist = newArr;
                    }
                })


            })
            getdata()
        })

        const getdata = () => {
            state.list = [];
            diyGet2(AjaxApi.sytAccountListInfo + `?gcode=` + state.code + `&current=1&size=99`).then(res => {
                let resData = res.data.data.records;
                state.list = resData;
            })
        }

        const onhfztConfirm = ({selectedIndexes,selectedOptions}) => {
            console.log(selectedIndexes,selectedOptions)
            state.hfzt = selectedOptions[0].value
            state.showhfzt = false
        };

        const ontypesearchConfirm = ({selectedIndexes,selectedOptions}) => {
            console.log(selectedIndexes,selectedOptions)
            state.searchflid = selectedOptions[0].value
            state.searchfl = selectedOptions[0].text
            state.showtypesearch = false
        };

        const ontypeConfirm = ({selectedIndexes,selectedOptions}) => {
            console.log(selectedIndexes,selectedOptions)
            state.addobj.flid = selectedOptions[0].value
            state.addobj.flmc = selectedOptions[0].text
            state.showtype = false
        };

        const onConfirm = () => {
            state.createDate = state.zxdate.join('-') +" " + '00:00:00';
            console.log(state.createDate)
            state.showkssjPicker = false
        };
        const onCancel = () => {
            state.showkssjPicker = false
        };
        const updatePageData = (e) => {
            state.pageTotal = e.total;
        }
        const formatTime = (val) => {
            let tmp = "";
            if (!!val.startTime && !!val.endTime) {
                tmp += Utils.dateFormat_date(val.startTime);
                tmp += " -- ";
                tmp += Utils.dateFormat_date(val.endTime);
            } else {
                tmp += "无限制";
            }
            return tmp;
        }

        const handleOptDetail = (item) => {
            console.log(`item`,item)
            state.itemformdata = item;
            state.showAddPopup = true;

        }

        const getDate = (o) => {
            return Utils.getDate(o);
        }
        const dateFormat_YMD = (o) => {
            return Utils.dateFormat_YMD(o);
        }
        const dateFormat_date = (o) => {
            return Utils.dateFormat_date(o);
        }
        const handleSearch = () => {
            state.list = [];
            diyGet2(AjaxApi.sytAccountListInfo + `?gcode=` + state.code + `&humancode=`+ state.jsh +`&current=1&size=99`).then(res => {
                let resData = res.data.data.records;
                state.list = resData;
            })
            state.showSearchPopup = false;
        }

        const handleAdd = () => {
            state.itemformdata.gcode = state.code
            diyPost(AjaxApi.sytAccountListInfosubmit, state.itemformdata).then((response) => {
                if (response.data.code === 200 && response.data.data) {
                    showSuccessToast('提交成功');
                    state.showAddPopup = false;
                    getdata()
                }else {
                    showFailToast(response.data.info);
                    state.showAddPopup = false;

                }
            }).catch(error => {
                console.log(error);
            })
        }

        const viewitem = (item) => {
            router.push({
                path: '/zxdetail',
                query:{isNavbar: false,json: JSON.stringify(item)}
            });
        };

        const replyitem = (item) => {
            router.push({
                path: '/zxdetail',
                query:{isNavbar: false,json: JSON.stringify(item),isreply: true}
            });
        };

        const deleteitem = (item) => {
            showConfirmDialog({
                title: '',
                message: '确定要删除?                                          ',
            }).then(() => {
                diyPost(AjaxApi.sytAccountListInforemove+ `?ids=` + item.id ).then((response) => {
                    if (response.data.code === 200 && response.data.data) {
                        showSuccessToast('删除成功');
                        getdata()
                    }else {
                        showFailToast(response.data.info);

                    }
                })
            })
        };

        return {
            ...toRefs(state),
            formatTime,
            handleOptDetail,
            onConfirm,
            onCancel,
            getDate,
            dateFormat_YMD,
            dateFormat_date,
            handleSearch,
            updatePageData,
            ontypeConfirm,
            handleAdd,
            ontypesearchConfirm,
            onhfztConfirm,
            deleteitem,
            viewitem,
            replyitem
        }
    },
};
</script>

<style lang="less" scoped>
@import '../../common/style/mixin';

.product-list-content {
    position: fixed;
    left: 0;
    top: 44px;
    width: 100%;
    z-index: 1000;
    background: #fff;

    .category-header {
        .fj();
        width: 100%;
        height: 50px;
        line-height: 50px;
        //padding: 0 15px;
        .boxSizing();
        font-size: 15px;
        color: #656771;
        z-index: 10000;

        &.active {
            background: @primary;
        }

        .icon-left {
            font-size: 25px;
            font-weight: bold;
        }

        .header-search {
            display: flex;
            width: 60%;
            height: 20px;
            line-height: 20px;
            margin: 10px 0;
            padding: 5px 0;
            color: #232326;
            background: #F7F7F7;
            .borderRadius(20px);

            .nbSearch {
                padding: 0 5px 0 20px;
                font-size: 17px;
            }

            .search-title {
                font-size: 14px;
                color: #666;
                background: #F7F7F7;
            }
        }

        .icon-More {
            font-size: 20px;
        }
        .header-left {
            display: flex;
            width: 50%;
            //margin-left: 20px;
            background-color: rgba(158, 158, 158, 0.13);
            justify-content: center;
            //border-radius: 5px;
            .right-icon {
                margin-top: 15px;
                margin-right: 10px;
            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
        .header-right {
            display: flex;
            width: 50%;
            //margin-left: 20px;
            justify-content: center;
            background-color: rgba(158, 158, 158, 0.13);
            //border-radius: 5px;
            .right-icon {
                margin-top: 15px;
                margin-right: 10px;

            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
    }
}

::v-deep .van-tabs .van-tabs__content {
    background: #fff;
    border-top: 5px solid #f5f5f5;
}
.content {
    height: calc(~"(100vh - 90px)");
    overflow: hidden;
    overflow-y: scroll;
    margin-top: 50px;
    margin-bottom: 76px;
}

.cell-class-name {
    color: #2c3e50;
    text-align: left;
}

.van-tag--plain::before {
    border: unset !important;
}

.van-dropdown-menu::after {
    border: none;
}
.list-item-info{
    text-align: left;
}
</style>

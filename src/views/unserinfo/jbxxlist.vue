<template>
    <div class="product-list-wrap">
        <div>
            <s-header :name="'人员列表'"></s-header>
        </div>
        <van-list v-model:loading="loading"
                  :finished="finished"
                  finished-text="没有更多了"
                  @load="onLoad">
            <van-cell v-for="item in list" :key="item.id">
                <div style="text-align: left;">
<!--                    <div class="list-item-name">{{item.humanname}}-{{item.humancode}}</div>-->
                    <div class="list-item-info">姓名：{{item.humanname}}</div>
                    <div class="list-item-info">账号：{{item.humancode}}</div>
                    <div class="list-item-info">性别：{{item.sex}}</div>
                    <div class="item_right_btn">
                        <van-tag plain type="danger" @click.stop="deleteitem(item)">
                            删除
                        </van-tag>
                        <van-tag plain type="primary" @click.stop="edititem(item)">
                            编辑
                        </van-tag>
                        <van-tag  plain type="success" @click="viewitem(item)">
                            查看
                        </van-tag>
                    </div>
                </div>
            </van-cell>
        </van-list>

    </div>
</template>

<script>
// import navBar from '@/components/NavBar'
import AjaxApi from "@/utils/api";
import {diyGet2, diyPost} from '@/service/home'
import { useRoute,useRouter} from "vue-router";
import {onMounted, reactive, toRefs} from "vue";
import {mapState} from "vuex";
import sHeader from '@/components/SimpleHeader'
import {showConfirmDialog, showFailToast, showSuccessToast} from "vant";


export default {
    components: {
        // navBar,
        sHeader,
    },
    computed: {
        ...mapState(["baseUrl", "defaultSearchIcon"]),
    },
    setup() {
        const router = useRouter()
        const route = useRoute()
        const state = reactive({
            name: null,
            current: 1,
            size: 10,
            loading: false,
            finished: false,
            list: [],
        })
        onMounted(async () => {
            if(route.query.name){
                state.name = route.query.name;
            }
        })

        const onLoad = () => {
            diyGet2(AjaxApi.sytAccountlist + `?usertype=teacher&current=`+state.current+`&size=10`).then(res => {
                let resData = res.data.data;
                state.list = state.list.concat(resData.records);
                console.log(`sytAccountlist`,resData)
                // state.list = [...resData.records]

                state.loading = false;
                state.current++
                if(resData.total == 0 ){
                    state.finished = true
                }else {
                    state.finished = resData.pages <= resData.current - 1
                }
            })
        }

        const viewitem = (item) => {
            router.push({
                path: '/userinfojbxx',
                query: {
                    // code: item.code,
                    // name: item.name,
                    humancode: item.humancode,
                    isbj: 'false',
                }
            });
        }

        const edititem = (item) => {
            router.push({
                path: '/userinfojbxx',
                query: {
                    // code: item.code,
                    // name: item.name,
                    humancode: item.humancode,
                    isbj: 'true',
                }
            });
        }

        const deleteitem = (item) => {
            showConfirmDialog({
                title: '',
                message: '确定要删除?                                          ',
            }).then(() => {
                diyPost(AjaxApi.sytPermissionAccountdelete,{id: item.id}).then(response => {
                    if (response.data.code === '00000' && response.data.info == 'success') {
                        showSuccessToast('删除成功');
                        router.go(0)
                    }else {
                        showFailToast(response.data.info);

                    }
                })
            })

        }

        return {
            ...toRefs(state),
            onLoad,
            viewitem,
            edititem,
            deleteitem
        }
    },
};
</script>

<style lang="less" scoped>
@import '../../common/style/mixin';

.product-list-content {
    position: fixed;
    left: 0;
    top: 44px;
    width: 100%;
    z-index: 1000;
    background: #fff;

    .category-header {
        .fj();
        width: 100%;
        height: 50px;
        line-height: 50px;
        //padding: 0 15px;
        .boxSizing();
        font-size: 15px;
        color: #656771;
        z-index: 10000;

        &.active {
            background: @primary;
        }

        .icon-left {
            font-size: 25px;
            font-weight: bold;
        }

        .header-search {
            display: flex;
            width: 60%;
            height: 20px;
            line-height: 20px;
            margin: 10px 0;
            padding: 5px 0;
            color: #232326;
            background: #F7F7F7;
            .borderRadius(20px);

            .nbSearch {
                padding: 0 5px 0 20px;
                font-size: 17px;
            }

            .search-title {
                font-size: 14px;
                color: #666;
                background: #F7F7F7;
            }
        }

        .icon-More {
            font-size: 20px;
        }
        .header-left {
            display: flex;
            width: 50%;
            //margin-left: 20px;
            background-color: rgba(158, 158, 158, 0.13);
            justify-content: center;
            //border-radius: 5px;
            .right-icon {
                margin-top: 15px;
                margin-right: 10px;
            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
        .header-right {
            display: flex;
            width: 50%;
            //margin-left: 20px;
            justify-content: center;
            background-color: rgba(158, 158, 158, 0.13);
            //border-radius: 5px;
            .right-icon {
                margin-top: 15px;
                margin-right: 10px;

            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
    }
}

::v-deep .van-tabs .van-tabs__content {
    background: #fff;
    border-top: 5px solid #f5f5f5;
}
.content {
    height: calc(~"(100vh - 90px)");
    overflow: hidden;
    overflow-y: scroll;
    margin-top: 50px;
    margin-bottom: 76px;
}

.cell-class-name {
    color: #2c3e50;
    text-align: left;
}

.van-tag--plain::before {
    border: unset !important;
}

.van-dropdown-menu::after {
    border: none;
}
.list-item-info{
    text-align: left;
}
</style>

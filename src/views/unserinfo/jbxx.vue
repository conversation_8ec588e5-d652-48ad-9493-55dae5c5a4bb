<template>
    <div class="product-list-wrap" style="bottom: 100px;">
        <div>
            <s-header :name="'基本信息'"></s-header>
        </div>
        <div style="overflow-y: auto;height: 100%;">
            <van-collapse v-model="activeNames">
                <van-collapse-item v-for="(item,index) in setfieldArr" :key="index" :title="item.name" :name="index+1">
                    <ZZ_Form style="padding-bottom: 80px;" ref="form" v-if="item.sfdt == '否' &&setformObj" :issubform="issubform"
                             :fields="item.moblielist"
                             :setformObj="setformObj"
                             :gid="item.id"
                             @toformsubmit="toformsubmit"
                             v-on:formsubmit="onFormSubmit"></ZZ_Form>
                    <div v-if="item.sfdt == '是' && setformObj">
                        <div v-if="item.dictFieldList.length > 0 && item.accountData && item.accountData.length>0" >
                            <div v-for="(dicts,index) in item.accountData" :key="index" class="boxSty">
                                <div v-for="(field,index) in item.dictFieldList" :key="index" class="list-item-info">{{field.fieldzh}}：{{dicts[field.fielden]}}</div>
                            </div>
                        </div>
                    </div>
                </van-collapse-item>
            </van-collapse>
        </div>

        <div v-if="isbj" class="van-submit-bar">
            <van-button class="van-button--square" type="primary"
                        size="large" @click="submitform">保存
            </van-button>
        </div>
    </div>
</template>

<script>
// import navBar from '@/components/NavBar'
import AjaxApi from "@/utils/api";
import { diyGet2, diyPost} from '@/service/home'
import { useRoute} from "vue-router";
import {onMounted, reactive, toRefs} from "vue";
import {mapState} from "vuex";
import sHeader from '@/components/SimpleHeader'
import {getLocal} from "@/common/js/utils";
import ZZ_Form from "@/components/form/ZZ_Form";
import {showFailToast, showSuccessToast} from "vant";


export default {
    components: {
        // navBar,
        sHeader,
        ZZ_Form
    },
    computed: {
        ...mapState(["baseUrl", "defaultSearchIcon"]),
    },
    setup() {
        // const router = useRouter()
        const route = useRoute()
        const state = reactive({
            name: null,
            current: 1,
            size: 10,
            loading: false,
            finished: false,
            list: [],
            userInfo: {},
            isbj: true,
            setfieldArr: [],
            setformObj: {},
            submitformdata: {},
            activeNames: [1,2,3,4,5,6,7],
        })
        onMounted(async () => {
            if(route.query.humancode){
                state.userInfo.humancode = route.query.humancode;
            }else {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo;
            }
            if(route.query.isbj == 'false'){
                state.isbj = false;
            }
            inituserFormObj();
        })

        const inituserFormObj = () => {
            // let arr = [];
            let fieldArr = [];
            let newFormObj = {};
            diyGet2(AjaxApi.getDetailbycode + `?humancode=` + (state.userInfo.humanCode ? state.userInfo.humanCode : state.userInfo.humancode)).then(res => {
                newFormObj = res.data.data;
                state.setformObj = newFormObj;
                console.log(`newFormObj===`,newFormObj)
            })

            diyGet2(AjaxApi.allAndField).then(res => {
                let resData = res.data.data;
                fieldArr = resData;
                fieldArr.forEach(item=>{
                    let newArr = [];
                    if(item.dictFieldList.length>0){
                        if(item.sfdt == '否'){
                            item.dictFieldList.forEach(field=>{
                                let dicData = [];
                                if(field.fieldData || field.fieldAttribute){
                                    if(!field.fieldData){
                                        if(field.fieldAttribute){
                                            let arr = field.fieldAttribute.split("#")
                                            arr.forEach(i=>{
                                                dicData.push({
                                                    label: i,
                                                    value: i,
                                                })
                                            })
                                        }
                                    }else {
                                        if(field.fieldAttribute){
                                            // dicUrl = item.fieldData;
                                            let arr = field.fieldAttribute.split("#")
                                            diyPost(field.fieldData, {}).then(res => {
                                                let resData = res.data;
                                                console.log(`resData`,resData)

                                                if (res.data.info && res.data.info.length>0) {
                                                    resData.info.forEach(i=>{
                                                        dicData.push({
                                                            label: i[arr[0]],
                                                            value: i[arr[1]],
                                                        })
                                                    })
                                                    console.log(`dicData`,dicData)
                                                }
                                            })
                                        }

                                    }
                                }
                                newArr.push({
                                    type: field.fieldType == 'upload' ? 'file-upload' : field.fieldType,
                                    values: null,
                                    options:{
                                        name: field.fielden,
                                        label: field.fieldzh,
                                        disabled: field.sfbj == '否',
                                        optionItems: dicData ? dicData : [],
                                    }
                                })
                            })
                            item.moblielist = newArr;
                        }else {
                            item.accountData = state.setformObj.accountListMap[item.id]
                        }

                    }
                    // item.moblielist = newArr;
                })
                state.setfieldArr = fieldArr;
            })
        }

        const toformsubmit = () => {
            // let param = {}
            // param[data.key] = data.value
            // // state.changeformdata
            // if(!state.changeformdata[data.gid]){
            //     state.changeformdata[data.gid] = {}
            // }
            // state.changeformdata[data.gid] = Object.assign(state.changeformdata[data.gid],param);
            // let newdata = {
            //     tmform: state.changeformdata,
            //     dtxxlist: state.dtxxlist
            // }
            // state.submitformdata = newdata;
            console.log(`state.setformObj========`,state.setformObj)
        }

        const submitform = () => {
            diyPost(AjaxApi.sytAccountedit, state.setformObj).then(response => {
                if (response.data.code === '00000' && response.data.info == 'success') {
                    showSuccessToast('提交成功');
                    inituserFormObj();
                }else {
                    showFailToast(response.data.info);

                }
            })
        }

        return {
            ...toRefs(state),
            toformsubmit,
            submitform
        }
    },
};
</script>

<style lang="less" scoped>
@import '../../common/style/mixin';

.product-list-content {
    position: fixed;
    left: 0;
    top: 44px;
    width: 100%;
    z-index: 1000;
    background: #fff;

    .category-header {
        .fj();
        width: 100%;
        height: 50px;
        line-height: 50px;
        //padding: 0 15px;
        .boxSizing();
        font-size: 15px;
        color: #656771;
        z-index: 10000;

        &.active {
            background: @primary;
        }

        .icon-left {
            font-size: 25px;
            font-weight: bold;
        }

        .header-search {
            display: flex;
            width: 60%;
            height: 20px;
            line-height: 20px;
            margin: 10px 0;
            padding: 5px 0;
            color: #232326;
            background: #F7F7F7;
            .borderRadius(20px);

            .nbSearch {
                padding: 0 5px 0 20px;
                font-size: 17px;
            }

            .search-title {
                font-size: 14px;
                color: #666;
                background: #F7F7F7;
            }
        }

        .icon-More {
            font-size: 20px;
        }
        .header-left {
            display: flex;
            width: 50%;
            //margin-left: 20px;
            background-color: rgba(158, 158, 158, 0.13);
            justify-content: center;
            //border-radius: 5px;
            .right-icon {
                margin-top: 15px;
                margin-right: 10px;
            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
        .header-right {
            display: flex;
            width: 50%;
            //margin-left: 20px;
            justify-content: center;
            background-color: rgba(158, 158, 158, 0.13);
            //border-radius: 5px;
            .right-icon {
                margin-top: 15px;
                margin-right: 10px;

            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
    }
}

::v-deep .van-tabs .van-tabs__content {
    background: #fff;
    border-top: 5px solid #f5f5f5;
}
.content {
    height: calc(~"(100vh - 90px)");
    overflow: hidden;
    overflow-y: scroll;
    margin-top: 50px;
    margin-bottom: 76px;
}

.cell-class-name {
    color: #2c3e50;
    text-align: left;
}

.van-tag--plain::before {
    border: unset !important;
}

.van-dropdown-menu::after {
    border: none;
}
.list-item-info{
    text-align: left;
}
</style>

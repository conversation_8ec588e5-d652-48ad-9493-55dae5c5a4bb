<template>
    <div>
        <s-header :name="name"></s-header>
        <van-tabs v-if="categoryList" v-model="active2" @click-tab="changeTabs">
            <van-tab :title="item.name" v-for="(item,index) in categoryList" :name="item" :key="index"  />
        </van-tabs>
        <div v-if="list && list.length > 0" class="newContent4">
            <ul class="newUl" style="margin-top: 10px;">
                <li v-for="(items,index) in list" :key="index" @click="showNewsDetail(items)">
<!--                    <div class="point" style="height: 29px;line-height: 29px;padding: 0 20px;" @click="showNewsDetail(items)">-->
<!--                                    <span class="newLiTitle" :title="items.title" :style="{color: '#333' }">-->
<!--                                        {{items.title}}</span>-->
<!--                        <span class="newLiTime">{{ items.createDate }}</span>-->
<!--                    </div>-->
                    <div style="padding: 0 20px;margin: 15px 0;">
                        <span class="newLiTitle" style="line-height: 20px;margin-bottom: 3px;width:100%;white-space: pre-wrap;display: -webkit-box;-webkit-line-clamp: 1; /* 显示的行数 */-webkit-box-orient: vertical;overflow: hidden;" :title="items.title" :style="{color: '#333' }">
                                {{items.title}}</span>
                        <div style="height: 20px;">
                            <span class="newLiorgName">{{ items.orgName }}</span>
                            <span class="newLiTime">{{ items.createDate }}</span>
                        </div>
                    </div>

                </li>
            </ul>
        </div>
        <div v-else>
            <div style="text-align: center;font-size: 18px;color: silver">
                <img style="display: block;height: 50px;margin: 20px auto;" :src="adress">
                暂无数据
            </div>
        </div>
        <van-popup v-model:show="detailShow" position="center"
                   :style="{height: '80%',width:'96%',overflow:'hidden'}">
            <div>
                <h2 class="popupH2">{{ curNews.title }}</h2>
                <p class="popupP">
                    发布时间：{{ curNews.createDate }}</p>
                <div v-html="curNews.content" class="popupDiv"></div>
            </div>
        </van-popup>
        <div v-if="popupClose" @click="detailShowFn()">
            <div class="shuxian"></div>
            <van-icon class="clickClose" name="close"/>
        </div>
        <!--        <div v-if="active == 0 && serviceData && serviceData.length > 0" class="category-list">-->
        <!--            <div v-for="(item, index) in serviceData" :key="index"-->
        <!--                 @click="showDetail(item)">-->
        <!--                <img :src="item.imgUrl.url?(baseUrl+item.imgUrl.url):defaultIcon" :onerror="defaultIcon">-->
        <!--                <span>{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</span>-->
        <!--            </div>-->
        <!--        </div>-->
    </div>
</template>

<script>
import {mapState} from "vuex";
import { onMounted, reactive, toRefs} from "vue";
import sHeader from "@/components/SimpleHeader";
// import {GetAppletList} from "@/service/applet";
// import {useRouter} from "vue-router";
import {useRoute} from 'vue-router';

import {getDataByUrl, GetDataCategory, GetNewsList, SetReadList} from '@/service/portal';
import {getLocal} from "@/common/js/utils";


export default {
    name: "yingyong",
    props: [
        "tztabs",
    ],
    components: {
        sHeader
    },
    setup() {
        // const router = useRouter()
        const route = useRoute()
        const state = reactive({
            // active: null,
            // categoryList: null,
            istype: 'yingyongPT',
            tabs: [
                '平台应用',
                '集成系统',
                '我的服务',
                '友情链接'

            ],
            name: '咨询',
            serviceData: null,
            // list: null,
            detailShow: false,
            popupClose: false,
            active2: '',
            curNews: {},
            list: [],
            active: '',
            moreUrl: "",
            clickUrl: "",
            readlist: [],
            userInfo: null,
            categoryList: null,
            category: null,
            categoryId: null,
            adress: require("../assets/ksj.png"),
        })

        onMounted(async () => {
            const userInfo = JSON.parse(getLocal('userInfo'))
            state.userInfo = userInfo
            getData();

        })

        const changeTabs = (tab) => {
            console.log(`tab`,tab)
            getnewsData(tab.name);
        }
        const getData = () => {
            console.log(JSON.parse(route.query.tztabs))
            if(route.query.name){
                state.name = route.query.name
            }
            if(JSON.parse(route.query.tztabs)){
                console.log(JSON.parse(route.query.tztabs))
                state.categoryList = JSON.parse(route.query.tztabs);
                getnewsData(state.categoryList[0]);
            }else {
                GetDataCategory({model: 'xinwen'}).then(res => {
                    state.categoryList = res.data.info;
                    getnewsData(state.categoryList[0]);
                });
            }

        }

        const showNewsDetail = (item) => {
            console.log(`showNewsDetail`,item)
            let data = {
                "key": item.title,
                "type":"校内通知"
            }
            SetReadList(data).then(()=>{})
            if (item.url && item.url !== '') {
                if (state.userInfo) {
                    let openUrl = item.clickUrl ? item.clickUrl : item.url.replace("#{humancode}", state.userInfo.humanCode) + (item.id ? item.id : '');
                    if (state.clickUrl && state.clickUrl !== '') {
                        openUrl = state.clickUrl + encodeURIComponent(openUrl);
                        getDataByUrl(openUrl, {}).then(res => {
                            if (res.data.code == "00000") {
                                window.location.href =res.data.info;
                                // console.log(res.data);
                            } else {
                                // this.$modal.msgWarning(res.data.info);
                            }
                        });
                    } else {
                        // window.open(openUrl, '_blank')
                        window.location.href = openUrl;
                    }

                }
            } else {
                if(item.detailLinks && item.detailLinks !== ''){
                    window.location.href = item.detailLinks;
                }else {
                    state.curNews = item;
                    state.detailShow = true;
                    state.popupClose = true;
                }

            }
        }

        const detailShowFn = () => {
            state.detailShow = false;
            state.popupClose = false;
        }

        const getnewsData = (item) => {

            if (item.dataSources && item.dataSources !== '') {
                getDataByUrl(
                    item.dataSources + (item.dataSources.indexOf('?') != -1 ? '&' : '?') + `page=1&pageSize=` + item.limit,
                    {code: state.userInfo ? state.userInfo.humanCode : ''}
                ).then(res => {
                    if (res.data.code === 200) {
                        if (Object.prototype.hasOwnProperty.call(res.data.data, "list")) {
                            state.list = res.data.data.list;
                            state.list.forEach(item=>{
                                state.readlist.forEach(read=>{
                                    if( item.title === read.key){
                                        item.isread = true
                                    }
                                })
                            })
                        } else {
                            let type = item.dataSources.split("/");
                            state.list = res.data.data[type[type.length - 1]];
                            state.list.forEach(item=>{
                                state.readlist.forEach(read=>{
                                    if( item.title === read.key){
                                        item.isread = true
                                    }
                                })
                            })
                        }
                    }
                });
            } else {
                const context = {
                    page: 1,
                    pageSize: 99,
                    // pageSize: 1,
                    queryParam: {
                        categoryId: item.id,
                    },
                };
                GetNewsList(context).then(res => {
                    // this.$store.commit("SET_LOADING", {name: "news", flag: false});
                    // this.obj.news = res.data.info;
                    state.list = res.data.info.records;
                    // if(state.readlist.length > 0){
                    //     state.list.forEach(item=>{
                    //         state.readlist.forEach(read=>{
                    //             if( item.title === read.key){
                    //                 item.isread = true
                    //             }
                    //         })
                    //     })
                    // }
                });

            }

        }

        return {
            ...toRefs(state),
            changeTabs,
            showNewsDetail,
            detailShowFn
        }
    },
    computed: {
        ...mapState(["baseUrl"])
    }
};
</script>

<style lang="less" scoped>
@import '../common/style/mixin';
@import '../common/style/home';
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding-bottom: 10px;
}
.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    //height: 29px;
    //line-height: 29px;
    //padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    //float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    //white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}
.popupH2 {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}
.newLiorgName{
    float: left;
    /*width: 80px;*/
    white-space: nowrap;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: right;
    font-size: 14px;
    color: #999;
}
.popupP {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
    border-bottom: 1px solid #eeeeee;
    padding-bottom: 20px;
    text-align: center;
}

.popupDiv {
    padding: 0px 20px;
    height: 63vh;
    overflow-y: scroll;
}

.clickClose {
    font-size: 40px;
    position: fixed;
    left: calc(50% - 20px);
    top: 92%;
    color: #fff;
    z-index: 3000;
}

.shuxian {
    border-left: 2px solid #fff;
    height: 2.2%;
    width: 0px;
    position: fixed;
    left: calc(50% - 1px);
    top: 90%;
    color: #fff;
    z-index: 3000;
}


.newContent4 {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
}

.newContent1 {
    padding: 5px 10px;
}

.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    //height: 29px;
    //line-height: 29px;
    //padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    //float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}
//.thebox{
//    height: 100%;
//    width: 100%;
//    background-color: #fff;
//    border-radius: 20px;
//    padding-bottom: 15px;
//}

::v-deep .van-tabs__underline {
    transform: translateX(-50%);
}

</style>

<template>
    <div class="box" style="background-color: #F7F8FF;">
        <div class="product-list-content">
            <s-header name="通讯录"></s-header>
            <header class="category-header wrap" style="justify-content: center;">
                <div class="header-search">
                    <i class="nbicon nbSearch"></i>
                    <input placeholder="请输入姓名"
                           type="text"
                           class="search-title"
                           v-model="humanName"/>
                </div>
                <div class="header-right" @click="showSearchPopup=true">
                    <van-icon class="right-icon" :name="defaultSearchIcon" size="18"/>
                    <div class="right-div">
                        筛选
                    </div>
                </div>
            </header>
        </div>

        <list style="margin-bottom: 60px;margin-top: 100px;padding: 0 10px;" :url="url" :queryObj="queryObj">
            <template v-slot:default="slotProps">
                <van-cell :key="item.id" v-for="item in slotProps.list" style="border: 1px solid rgba(153,153,153,0.09);border-radius: 10px;margin: 5px 0;">
                    <div class="cell-class-name">
                        <div class="list-item-name">
                            {{ item.humanName }} - {{ item.humanCode }}
                        </div>
<!--                        <div class="list-item-name">-->
<!--                            {{ item.humanName }}-->
<!--                        </div>-->
                        <div class="list-item-info">部门名称：{{item.department}}</div>
                        <div class="list-item-info">固定电话：{{item.telePhone}}</div>
                        <div class="list-item-info">办公地点：{{item.textarea}}</div>
                        <div class="list-item-info">岗位：{{item.gwmc}}</div>
                    </div>
                </van-cell>
            </template>
        </list>

        <van-popup v-model:show="showSearchPopup" position="top" :style="{height:'auto',width:'100%'}">
            <div style="margin-bottom: 30px; ">
                <van-cell-group>
                    <van-field label="工号" readonly/>
                    <van-field label="" v-model="humanCode" placeholder="请输入工号"/>
                    <van-field label="姓名" readonly/>
                    <van-field label="" v-model="humanName" placeholder="请输入姓名"/>
<!--                    <van-field label="固定电话" readonly/>-->
<!--                    <van-field label="" v-model="telePhone" placeholder="请输入固定电话"/>-->
                </van-cell-group>
            </div>
            <div class="van-submit-bar-search">
                <van-button type="primary" round size="small" @click="handleSearch"> 搜&nbsp;&nbsp;索
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
import List from "@/components/List";
import sHeader from "@/components/SimpleHeader";
import {onMounted, reactive, toRefs, watch} from "vue";
// import {useRouter} from "vue-router";
// import {diyPost} from "@/service/home";
import {mapState} from "vuex";

export default {
    name: "table",
    components: {
        sHeader,
        List
    },
    computed: {
        ...mapState(["baseUrl", "defaultSearchIcon"]),
    },
    setup() {
        // const router = useRouter()
        const state = reactive({
            url: '/sytAddressBook/queryPage',
            queryObj: {
                queryParam:{
                    txllx: 'renyuan'
                }

            },
            list: [],
            showSearchPopup: false,
            type: 'sytAddressBook',
            humanCode: '',
            humanName: '',
            telePhone: '',
        })
        watch(() => state.humanName, () => {
            // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
           state.queryObj = Object.assign(state.queryObj,{
               queryParam:{
                   txllx: 'renyuan',
                   humanCode: state.humanCode,
                   humanName: state.humanName,
                   telePhone: state.telePhone,
               }

           })
        })
        onMounted(async () => {

            // let context = {
            //     page: 1,
            //     pageSize: 10,
            //     queryParam: {
            //
            //     },
            // }
            // diyPost('/' + state.type +'/queryPage',context ).then(response => {
            //     const data = response.data.info;
            //     state.list = data.content;
            // })

        })
        const handleSearch = () => {
            state.queryObj = Object.assign(state.queryObj,{
                queryParam:{
                    txllx: 'renyuan',
                    humanCode: state.humanCode,
                    humanName: state.humanName,
                    telePhone: state.telePhone,
                }
            })
            state.showSearchPopup = false;
        }

        return {
            ...toRefs(state),
            handleSearch
        }
    }
}
</script>

<style lang="less" scoped>
@import '../common/style/mixin';
.cell-class-name {
    color: #2c3e50;
    text-align: left;
}
.product-list-content {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: #fff;

    .category-header {
        .fj();
        width: 100%;
        height: 50px;
        line-height: 50px;
        padding: 0 15px;
        .boxSizing();
        font-size: 15px;
        color: #656771;
        z-index: 10000;

        &.active {
            background: @primary;
        }

        .icon-left {
            font-size: 25px;
            font-weight: bold;
        }

        .header-search {
            display: flex;
            width: 88%;
            height: 20px;
            line-height: 20px;
            margin: 10px 0;
            padding: 5px 0;
            color: #232326;
            background: #F7F7F7;
            .borderRadius(20px);

            .nbSearch {
                padding: 0 5px 0 20px;
                font-size: 17px;
            }

            .search-title {
                font-size: 14px;
                color: #666;
                background: #F7F7F7;
            }
        }

        .icon-More {
            font-size: 20px;
        }

        .header-right {
            display: flex;
            width: 16%;
            margin-left: 20px;

            .right-icon {
                margin-top: 15px;
            }

            .right-div {
                font-size: 14px;
                color: #666;
                padding-top: 1px;
            }
        }
    }
}
</style>
<template>
    <section>
        <div class="product-list-content" style="position: absolute;top: 0;">
            <s-header :name="'消息'" :back="'/user'"></s-header>
        </div>
        <div style="margin-top: 44px;">
            <van-cell :key="index" v-for="(item,index) in messageList"
                      @click="handleOptDetail(item)">
            <span v-if="item.NUM !== 0" class="item_right_top van-badgeNum">
<!--                {{item.NUM>99?'99+':item.NUM}}-->
                {{item.NUM}}
            </span>
                <div style="display: flex;justify-content: space-between;" class="cell-class-name">
                    <div>
                        <div class="list-item-name" style="color: #fff;font-size: 15px;">
                            {{item.TYPE}}
                        </div>
                        <!--                    <div class="list-item-info">-->
                        <!--                        <div class="child">{{item.TITLE}}</div>-->
                        <!--                    </div>-->
                    </div>
                </div>
            </van-cell>
        </div>

<!--        <nav-bar/>-->
    </section>
</template>

<script>
    // import navBar from '@/components/NavBar'
    import Utils from "@/utils/momentWrap";
    import AjaxApi from "@/utils/api";
    import {useRouter} from "vue-router";
    import {onMounted, reactive, toRefs} from "vue";
    import {diyPost} from '@/service/home'
    import sHeader from '@/components/SimpleHeader'

    export default {
        components: {
            // navBar,
            sHeader
        },
        setup() {
            const router = useRouter()
            const state = reactive({
                messageList: null,
                queryObj: {},
            })
            onMounted(async () => {
                state.queryObj = {
                    queryParam: {
                        status: 0
                    }
                }
                getSytMessageSend()
            })
            const getSytMessageSend = () => {
                diyPost(AjaxApi.sytMessageSend, state.queryObj).then(res => {
                    if (res.status === 200 && res.data.code === '00000') {
                        state.messageList = res.data.info
                    }
                })
            }
            const handleOptDetail = (item) => {
                router.push({
                    path: '/mine-message',
                    query: {
                        currentType: item.TYPE,
                    }
                });
            }
            const dateFormat_date = (o) => {
                return Utils.dateFormat_date(o);
            }
            const goBack = () => {
                router.go(-1)
            }
            return {
                ...toRefs(state),
                goBack,
                dateFormat_date,
                handleOptDetail,
            }
        },
    };
</script>

<style scoped>
    .van-badgeNum {
        display: inline-block;
        box-sizing: border-box;
        min-width: var(--van-badge-size);
        padding: var(--van-badge-padding);
        color: var(--van-badge-color);
        font-weight: var(--van-badge-font-weight);
        font-size: var(--van-badge-font-size);
        font-family: var(--van-badge-font);
        line-height: 1.2;
        text-align: center;
        background: var(--van-badge-background);
        border: var(--van-badge-border-width) solid var(--van-background-2);
        border-radius: var(--van-radius-max);
    }

    .cell-class-name {
        color: #2c3e50;
        text-align: left;
    }

    .child {
        width: 336px;
        height: 66px;
        border-radius: 4px;
    }

    ::v-deep .van-cell__value{
        background: linear-gradient(to right, #ff7e5f, #feb47b);
        border-radius: 10px;
        padding: 10px;
    }

</style>

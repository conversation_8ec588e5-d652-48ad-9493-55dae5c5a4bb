<template>
    <div style="height: 100%;background-color: #F0FDFD;">
        <div class="content-view">
            <div class="back-view"></div>
            <div style="margin: 15px;border-radius: 10px;overflow: hidden;">
                <van-swipe class="my-swipe" :autoplay="3000" indicator-color="#1baeae">
                    <van-swipe-item v-for="(item, index) in carouselData" :key="index">
                        <img :src="baseUrl+item.url" @error="handleImageError" @click="showNewsDetail(item)">
                    </van-swipe-item>
                </van-swipe>
            </div>
            <div style="margin: 15px;border-radius: 10px;overflow: hidden;">
                <div class="thebox">
<!--                    <van-tabs v-model="active" type="card" @click-tab="newsTabs">-->
<!--                        <van-tab :title="item" v-for="item in serviceDatatabs" :key="item" />-->
<!--                    </van-tabs>-->
                    <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
                        推荐服务
                    </div>
                    <div class="service-row" style="margin-top: 20px;">
                        <div class="index-sml" v-for="(item) in serviceData" :key="item.id" @click="toService(item)">
                            <img :src="baseUrl+'/file/view/'+item.icon" @error="handleImageError2"/>
                            <p style="white-space: nowrap; /* 防止文本换行 */overflow: hidden; /* 隐藏超出部分 */text-overflow: ellipsis;">{{item.name}}</p>
                        </div>
                        <div @click="showDetailmore()" class="index-sml">
                            <img src="../assets/u238.svg"/>
                            <p>更多</p>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin: 15px;border-radius: 10px;overflow: hidden;">
                <div style="width: 100%;height: 130px;color: #fff;position: relative;" class="backimg" @click="tonewtongji">
                    <img :src="defaultIconback" style="height: 130px;position: absolute;right: 0;z-index: 1;opacity: 0.34;">
                    <div style="border-radius: 7.5px;background-color: #e6a23c;width: 15px;height: 15px;line-height: 15px;text-align: center;position: absolute;left: 110px;top: 17px;">
                        <van-icon name="arrow" />
                    </div>
                    <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
                        网厅数据
                    </div>
                    <div class="mineDesktopDataWrap">
                        <div class="mineDesktopData">
                            <p class="mineDesktopDataNumber">{{wtsj}}</p>
                            <div class="mineDesktopDataTitle">服务事项总数</div>
                        </div>
                        <div class="mineDesktopData">
                            <p class="mineDesktopDataNumber">{{fwcs}}</p>
                            <div class="mineDesktopDataTitle">累计访问次数</div>
                        </div>
                        <div class="mineDesktopData">
                            <p class="mineDesktopDataNumber">{{fqcs}}</p>
                            <div class="mineDesktopDataTitle">累计服务人数</div>
                        </div>
                        <div class="mineDesktopData">
                            <p class="mineDesktopDataNumber">{{bjcs}}</p>
                            <div class="mineDesktopDataTitle">累计办结事项</div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin: 15px;border-radius: 10px;overflow: hidden;background-color: #fff;">
                <div style="line-height: 50px;font-size: 18px;font-weight: bold;margin-left: 15px;">
                    部门办事
                </div>
                <div style="display: flex;justify-content: left;flex-wrap: wrap;margin: 10px 15px;">
                    <div v-for="i in OrgDatalist" :key="i.id" class="banshibox" @click="todeptservice(i)">
                        <div style="font-size: 14px;font-weight: bold;white-space: nowrap; /* 防止文本换行 */overflow: hidden; /* 隐藏超出部分 */text-overflow: ellipsis;padding: 0 5px;">{{i.orgname}}</div>
                        <div style="font-size: 12px;color: #999;">({{i.hasSize}}项服务)</div>
                    </div>
                </div>
            </div>
            <div class="good" style="margin-top: 60px">
                <header class="good-header"></header>
            </div>

        </div>
    </div>
</template>

<script>
import {reactive, onMounted, toRefs, nextTick} from 'vue'
import {useRouter} from 'vue-router'
import {
    // mapMutations,
    mapState,
    // mapMutations
    // useStore
} from "vuex";

// import {GetAppletList} from "@/service/applet";
import {closeToast, showFailToast, showLoadingToast} from "vant";
import {GetCarousel} from "@/service/portal";
import {getLocal} from "@/common/js/utils";
import {diyPost, hitCount} from "@/service/home";
import {GetRoleInfo} from "@/service/settings";
import AjaxApi from "@/utils/api";
import {getActiveDefinitionId} from "@/service/bpmProcessDefinition";

// import AjaxApi from "@/utils/api";

export default {
    name: 'home',
    components: {

    },
    computed: {
        ...mapState(["baseUrl"])
    },
    setup() {
        // const store = useStore()
        const router = useRouter()
        const state = reactive({
            desktop: {},
            defaultIcon: require("../assets/lunbo.png"),
            defaultIcon2: require("../assets/isnull.png"),
            defaultIconback: require("../assets/u524.svg"),
            serviceData: [],
            userInfo: null,
            active: 0,
            carouselData: [],
            serviceDatatabs: [
                '教师办事',
                '学生办事'
            ],
            OrgDatalist: [],
            serviceList: [
                {text: "全部", value: "all"},
                {text: "流程服务", value: "0"},
                {text: "应用服务", value: "1"},
                {text: "预约资源", value: "2"},
                {text: "填报任务", value: "3"},
            ],
            categoryList: [],

            wtsj: null,
            fwcs: null,
            bjcs: null,
            fqcs: null,
            roleList: null,//所属部门

        })

        onMounted(async () => {

            // const token = getLocal('token')
            // if (token) await init();
            const userInfo = JSON.parse(getLocal('userInfo'))
            state.userInfo = userInfo
            console.log(`userInfo`,userInfo)
            // await getRoleInfo;

            await getData()
            await load();
        })

        const load = () => {
            GetCarousel({type: null}).then(res => {
                state.carouselData = res.data.info;
            });
        }

        const tonewtongji = () => {
            router.push({
                path: '/new-tongji-index',
                query: {
                    // orgid: item.id
                    isNavBar: true
                }
            })
        }

        const todeptservice = (item) => {
            console.log(`todeptservice`,item)
            router.push({
                path: '/new-mine-service',
                query: {
                    orgid: item.id
                }
            })
        }

        // nextTick(() => {
        //     window.addEventListener('scroll', () => {
        //         let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
        //         scrollTop > 100 ? state.headerScroll = true : state.headerScroll = false
        //     })
        // })

        // 访问次数
        const setHitCount = (hitData) => {
            hitCount(hitData).then(res => {
                console.log(res)
            })
        }

        const toService = (service) => {
            nextTick(() => {
                let role = []
                if (service.roleId) {
                    service.roleId.forEach(item => {
                        state.roleList.forEach(val => {
                            if (item == val.id) {
                                role.push(val.rolename)
                            }
                        })
                    })
                }

                if(service.popWindow == '否'){
                    let hitData = {
                        id: service.id
                    };
                    if (service.linkType == '0') {
                        getActiveDefinitionId({processDefinitionKey: service.processDefinitionId}).then(res => {
                            if (res.data.code == "00000") {
                                setHitCount(hitData)
                                router.push({
                                    path: "/mine-dealt-create",
                                    query: {
                                        id: res.data.info, name: service.name,
                                        activeTab: service.linkType,
                                        backUrl: '/new-mine-service',
                                        // backId: state.currentId,
                                        // backName: state.name,
                                    }
                                })
                            } else {
                                showFailToast(res.data.info);
                            }
                        })
                    } else if (service.linkType == '1') {
                        // 访问次数
                        setHitCount(hitData)
                        let routeData = service.link
                        // window.open(routeData, '_blank')
                        window.location.href = routeData

                    } else if (service.linkType == '2') {
                        // 访问次数
                        setHitCount(hitData)
                        router.push({path: "/mine-service-recourse", query: {id: service.resourceId, name: service.name}})
                    } else if (service.linkType == '3') {
                        // 访问次数
                        setHitCount(hitData)
                        router.push({path: "/mine-service-taskFill", query: {taskId: service.taskId, name: service.name}})
                    }
                }else {
                    console.log(`toService`,service,state.roleList)
                    let linkTypeName = service.linkType ? state.serviceList.find(item => {
                        return item.value === service.linkType;
                    })['text'] : '';
                    service.linkTypeName = linkTypeName
                    let categoryName = service.categoryId ? state.categoryList.find(item => {
                        return item.value === service.categoryId;
                    })['text'] : '';
                    service.categoryName = categoryName
                    service.rolename = role.join()
                    router.push({
                        path: 'mine-service-details',
                        query: {
                            isNavBar: false,
                            service: JSON.stringify(service),
                        }
                    });
                }


            })
        }


        const handleImageError = (event) => {
            event.target.src = state.defaultIcon;
        }
        const handleImageError2 = (event) => {
            event.target.src = state.defaultIcon2;
        }

        const getData = () => {
            GetRoleInfo().then((res) => {
                state.roleList = res.data.info;
                console.log(`getRoleInfo`,state.roleList)
            });
            let json = {
                model: "wodefuwu"
            }
            diyPost(AjaxApi.GetSytDataCategoryList, json).then(res => {
                if (res.status === 200 && res.data.code === "00000") {
                    let rData = res.data.info;
                    let newArray = [{
                        text: '全部',
                        value: 'all',
                    }]
                    if (rData.length > 0) {
                        rData.forEach(e => {
                            newArray.push({
                                text: e.name,
                                value: e.id,
                            })
                        })
                        state.categoryList = newArray;
                    }
                }
            })
            diyPost('/sytServiceCenter/list',{
                tab: "all",
                showType: "mobile"
            } ).then(res => {
                let content = res.data.info;
                for (let i = 0; i < 7 && i < content.length; i++) {
                    state.serviceData.push(content[i]);
                }
            })
            diyPost('/data/api/hallStat/getServeAllCount',{
                humanCode: state.userInfo.humanCode,
                roleId: state.userInfo.roleId
            } ).then(res => {
                let content = res.data.data.value;
                state.wtsj = content
                console.log(`content===`,content)
            })
            diyPost('/data/api/hallStat/getServeVisitCount',{
                humanCode: state.userInfo.humanCode,
                roleId: state.userInfo.roleId
            } ).then(res => {
                let content = res.data.data.value;
                state.fwcs = content
            })
            diyPost('/data/api/hallStat/getProcessFinishCount',{
                humanCode: state.userInfo.humanCode,
                roleId: state.userInfo.roleId
            } ).then(res => {
                let content = res.data.data.value;
                state.bjcs = content
            })
            diyPost('/data/api/hallStat/getProcessAllCount',{
                humanCode: state.userInfo.humanCode,
                roleId: state.userInfo.roleId
            } ).then(res => {
                let content = res.data.data.value;
                state.fqcs = content
            })
            diyPost('/sytServiceCenter/queryOrgDataInSC',{} ).then(res => {
                let content = res.data.info;
                state.OrgDatalist = content
                console.log(`content===`,content)
            })
        }

        // const showDetail = (app) => {
        //     // window.open(app.redirectUrl, '_blank')
        //     showLoadingToast({
        //         message: '正在跳转...',
        //         forbidClick: true,
        //         duration: 0
        //     });
        //     if (app.redirectUrl && app.redirectUrl !== '') {
        //         if (state.userInfo) {
        //             let openUrl = app.redirectUrl.replace("#{humancode}", state.userInfo.humanCode);
        //             // window.open(openUrl, '_blank')
        //             window.location.href = openUrl
        //         }
        //     } else {
        //         window.location.href = app.redirectUrl
        //     }
        //     setTimeout(()=>{
        //         closeToast()
        //     },2000)
        // }
        const showNewsDetail = (item) => {
            if (item.detail && item.detail !== '') {
                // window.open(item.detail, '_blank');
                window.location.href = item.detail
            }
        }
        const showDetailmore = () => {
            router.push({path: '/new-mine-service',query:{isNavBar: true}})
        }

        const goTo = (app) => {
            showLoadingToast({
                message: '正在跳转...',
                forbidClick: true,
                duration: 0
            });
            if (app.redirectUrl && app.redirectUrl !== '') {
                if (state.userInfo) {
                    let openUrl = app.redirectUrl.replace("#{humancode}", state.userInfo.humanCode);
                    // window.open(openUrl, '_blank')
                    window.location.href = openUrl
                }
            } else {
                window.location.href = app.redirectUrl
            }
            setTimeout(()=>{
                closeToast()
            },2000)
        }


        return {
            ...toRefs(state),
            goTo,
            getData,
            showNewsDetail,
            // showDetail,
            showDetailmore,
            handleImageError,
            toService,
            todeptservice,
            handleImageError2,
            tonewtongji
        }
    },
}
</script>

<style lang="less" scoped>
@import '../common/style/mixin';
@import '../common/style/home';

.content-view {
    margin-bottom: 56px;
    padding: 20px 0 60px;
    //background-color: #F4F9FD;
    //height: 100%;
    background-color: #F4F9FD;
    //background-image: url("../assets/u164.png");
    //background-repeat: no-repeat;
    //background-size: 100%;
    //background: linear-gradient(180deg, #3396FA 50%, rgba(240, 253, 253, 1) 100%);
}
.back-view {
    //padding: 20px 0 60px;
    //background-color: #F4F9FD;
    height: 170px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    //background-image: url("../assets/u164.png");
    //background-repeat: no-repeat;
    //background-size: 100%;
    background: linear-gradient(180deg, #3875C6 50%, rgba(240, 253, 253, 1) 100%);
    z-index: 0;
}

.banshibox {
    text-align: center;
    /* margin-right: 2%; */
    padding: 15px 0px;
    border-radius: 5px;
    /* margin-top: 10px; */
    background-color: #E6F6F7;
    width: 32%;
    margin: 3px 0.5%;
}

.mineDesktopDataWrap{
    display: flex;
    justify-content: space-around;
}

.mineDesktopData{
    text-align: center;
}

.mineDesktopDataNumber{
    font-size: 23px;
    font-weight: bold;
    margin: 5px 0;
}

.mineDesktopDataTitle{
    font-size: 12px;
    margin: 0;
}
//
//.content-view {
//    margin-bottom: 56px;
//    padding: 20px 0 60px;
//    height: 180px;
//    //background-color: #F4F9FD;
//    background: linear-gradient(180deg, #3396FA 50%, rgba(240, 253, 253, 1) 100%);
//    border: none;
//    border-radius: 0px;
//    -moz-box-shadow: none;
//    -webkit-box-shadow: none;
//    box-shadow: none;
//}
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
}

.service-row {
    overflow: hidden;
    font-size: 12px;
}

.title {
    border-top: 1px solid #eee;
}

.title span {
    margin: 10px 4px 0 10px;
    display: inline-block;
    height: 10px;
    width: 3px;
    background: #1a9fe4;
}

.title p {
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    display: inline-block;
}

.index-sml {
    width: 25%;
    height: 60px;
    float: left;
    text-align: center;
    margin-bottom: 20px;
}

.index-sml img {
    width: 26px;
    height: 26px;
}

.index-sml p {
    margin-top: 10px;
}


.newUl {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newUl li {
    /*width: 100%;*/
    //height: 29px;
    //line-height: 29px;
    //padding: 0 10px;
}

.newUl li a {
    color: #666;
}

.newLiTitle {
    float: left;
    width: calc(100% - 80px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
}

.newLiTitle .van-tag {
    margin-right: 5px;
    padding-top: 2px !important;
}

.newLiTime {
    float: right;
    /*width: 80px;*/
    text-align: right;
    font-size: 14px;
    color: #999;
}
//.my-swipe {
//    img {
//        width: 100%;
//        height: 200px;
//    }
//}
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
}
.service-row {
    overflow: hidden;
    font-size: 12px;
}

.title {
    border-top: 1px solid #eee;
}

.title span {
    margin: 10px 4px 0 10px;
    display: inline-block;
    height: 10px;
    width: 3px;
    background: #1a9fe4;
}

.title p {
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    display: inline-block;
}

.index-sml {
    width: 25%;
    height: 60px;
    float: left;
    text-align: center;
    margin-bottom: 20px;
}

.index-sml img {
    width: 26px;
    height: 26px;
}

.index-sml p {
    margin-top: 10px;
}

::v-deep .van-tabs__wrap{
    height: 50px;
}

::v-deep .van-tabs__nav--card{
    height: 50px;
    margin: 0;
    border: none;
    color: #333;
    background-color: #DBF9FB;
    border: none;
}

::v-deep .van-tab__text{
    color: #333;
    font-size: 20px;font-weight: bold;
}

::v-deep .van-tab--active{
   background-color: #fff;
    border: none;
}

::v-deep .van-tab{
    border: none;
}
.backimg{
    //background-image: url("../assets/u522.svg");
    //background-repeat: no-repeat;
    //background-size: 100%;
    //background: #3875C6;
    background: linear-gradient(110.756deg, #3875C6 1%, rgba(46, 114, 204, 0.3) 100%);
}
.my-swipe {
    height: 145px;
    img {
        width: 100%;
        height: 145px;
    }
}
</style>

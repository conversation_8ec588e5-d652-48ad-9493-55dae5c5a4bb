<template>
    <div>
        <header class="home-header wrap" :class="{'active' : headerScroll}">
            <router-link tag="i" to="./category"><i class="nbicon nbmenu2"></i></router-link>
            <div class="header-search">
                <span class="app-name">新蜂商城</span>
                <i class="iconfont icon-search"></i>
                <router-link tag="span" class="search-title" to="./product-list?from=home">山河无恙，人间皆安</router-link>
            </div>
            <router-link class="login" tag="span" to="./login" v-if="!isLogin">登录</router-link>
            <router-link class="login" tag="span" to="./user" v-else>
                <van-icon name="manager-o"/>
            </router-link>
        </header>
<!--        <nav-bar/>-->
        <template v-if="blockslist">
            <div v-for="(item,index) in blockslist" :key="index">
                <!--            轮播图-->
                <div v-if="item.model === 'lunbotu'">
                    <swiper :list="swiperList"></swiper>
                </div>
                <!--            我的服务-->
                <div v-if="item.model === 'wodefuwu'" class="applyServiceWrap">
                    <header class="good-header"></header>
                    <p class="applyServiceTitle" @click="handleServices('fw')">我的服务
                        <van-icon class="lookMoreDiy" name="arrow"/>
                    </p>
                    <div class="serviceContent">
                        <div class="service" v-for="(item, index) in sytServiceCenterList" :key="index"
                             @click="to('serviceDetails', {title:''})">
                            <img :src="item.icon?(baseUrl+'/file/view/'+item.icon):defaultIcon" alt="">
                            <p>{{item.name}}</p>
                        </div>
                    </div>
                </div>
                <!--我的应用-->
                <div v-if="item.model === 'yingyongPT'" class="applyServiceWrap">
                    <header class="good-header"></header>
                    <p class="applyServiceTitle" @click="handleServices('yy')">我的应用
                        <van-icon class="lookMoreDiy" name="arrow"/>
                    </p>
                    <div class="serviceContent">
                        <div class="service" v-for="applet in appletList.records" :key="applet.id"
                             @click="showDetailYY(applet)">
                            <img :src="applet.imgUrl.url?(baseUrl+applet.imgUrl.url):defaultIcon" alt="">
                            <p>{{applet.name.length >5?applet.name.substring(0,5)+"..":applet.name}}</p>
                        </div>
                    </div>
                </div>
                <div v-if="item.model === 'yingyongPT'" class="applyServiceWrap">
                    <header class="good-header"></header>
                    <p class="applyServiceTitle" @click="handleServices('yy')">我的应用
                        <van-icon class="lookMoreDiy" name="arrow"/>
                    </p>
                    <div class="serviceContent">
                        <div class="service" v-for="applet in appletList.records" :key="applet.id"
                             @click="showDetailYY(applet)">
                            <img :src="applet.imgUrl.url?(baseUrl+applet.imgUrl.url):defaultIcon" alt="">
                            <p>{{applet.name.length >5?applet.name.substring(0,5)+"..":applet.name}}</p>
                        </div>
                    </div>
                </div>
                <!--我的数据-->
                <div v-if="item.model === 'wodexinxi'" class="mineData">
                    <header class="good-header"></header>
                    <p class="mineDataTitle" @click="handleServices('sj')">我的数据
                        <van-icon class="lookMoreDiy" name="arrow"/>
                    </p>
                    <div class="dataContent">
                        <div style="width: 710px;">
                            <div class="data" v-for="(item,index) in appList" :key="index">
                                <p>{{item.name}}</p>
                                <div><span class="colorRed">{{ item.count }}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--新闻资讯-->
                <div v-if="item.model === 'xinwen'" class="newWrap">
                    <van-icon class="lookMore" name="arrow" @click="handleServices('xw')"/>
                    <van-tabs v-model="active" @click="onClick(active,item)" animated>
                        <van-tab v-for="( e, eindex) in item.tabs" :key="eindex" :title="e.name">
                        </van-tab>
                    </van-tabs>
                    <template v-for="(news,newindex) in newsList">
                        <div :key="newindex" v-if="newindex<3" class="new">
                            <!--   <p><span class="newTag"></span> {{ news.content }}</p> -->
                            <div class="date">
                                <!--                                <span class="newBlock">最新</span>-->
                                <van-tag plain type="danger">最新</van-tag>&nbsp;
                                <span class="newFrom">{{ news.title}}</span>{{ news.createDate }}
                                <!--                                <span class="newFrom">{{ // news.title.substring(0, 17) + "..." }}</span>{{ news.createDate }}-->
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </template>

        <!--        <div class="category-list">-->
        <!--            <div v-for="item in categoryList" v-bind:key="item.categoryId" @click="tips">-->
        <!--                <img :src="item.imgUrl">-->
        <!--                <span>{{item.name}}</span>-->
        <!--            </div>-->
        <!--        </div>-->
        <!--        <div class="good">-->
        <!--            <header class="good-header">新品上线</header>-->
        <!--            <van-skeleton title :row="3" :loading="loading">-->
        <!--                <div class="good-box">-->
        <!--                    <div class="good-item" v-for="item in newGoodses" :key="item.goodsId" @click="goToDetail(item)">-->
        <!--                        <img :src="$filters.prefix(item.goodsCoverImg)" alt="">-->
        <!--                        <div class="good-desc">-->
        <!--                            <div class="title">{{ item.goodsName }}</div>-->
        <!--                            <div class="price">¥ {{ item.sellingPrice }}</div>-->
        <!--                        </div>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </van-skeleton>-->
        <!--        </div>-->
        <!--        <div class="category-list">-->
        <!--            <div v-for="item in categoryList" v-bind:key="item.categoryId" @click="tips">-->
        <!--                <img :src="item.imgUrl">-->
        <!--                <span>{{item.name}}</span>-->
        <!--            </div>-->
        <!--        </div>-->
        <!--        <div class="good">-->
        <!--            <header class="good-header">热门商品</header>-->
        <!--            <van-skeleton title :row="3" :loading="loading">-->
        <!--                <div class="good-box">-->
        <!--                    <div class="good-item" v-for="item in hots" :key="item.goodsId" @click="goToDetail(item)">-->
        <!--                        <img :src="$filters.prefix(item.goodsCoverImg)" alt="">-->
        <!--                        <div class="good-desc">-->
        <!--                            <div class="title">{{ item.goodsName }}</div>-->
        <!--                            <div class="price">¥ {{ item.sellingPrice }}</div>-->
        <!--                        </div>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </van-skeleton>-->
        <!--        </div>-->
        <!--        <div class="category-list">-->
        <!--            <div v-for="item in categoryList" v-bind:key="item.categoryId" @click="tips">-->
        <!--                <img :src="item.imgUrl">-->
        <!--                <span>{{item.name}}</span>-->
        <!--            </div>-->
        <!--        </div>-->
        <!--        <div class="good" :style="{ paddingBottom: '100px'}">-->
        <!--            <header class="good-header">最新推荐</header>-->
        <!--            <van-skeleton title :row="3" :loading="loading">-->
        <!--                <div class="good-box">-->
        <!--                    <div class="good-item" v-for="item in recommends" :key="item.goodsId" @click="goToDetail(item)">-->
        <!--                        <img :src="$filters.prefix(item.goodsCoverImg)" alt="">-->
        <!--                        <div class="good-desc">-->
        <!--                            <div class="title">{{ item.goodsName }}</div>-->
        <!--                            <div class="price">¥ {{ item.sellingPrice }}</div>-->
        <!--                        </div>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </van-skeleton>-->
        <!--        </div>-->
        <!--        <div class="category-list">-->
        <!--            <div v-for="item in categoryList" v-bind:key="item.categoryId" @click="tips">-->
        <!--                <img :src="item.imgUrl">-->
        <!--                <span>{{item.name}}</span>-->
        <!--            </div>-->
        <!--        </div>-->
    </div>
</template>

<script>
    import {reactive, onMounted, toRefs, nextTick} from 'vue'
    import {useRouter} from 'vue-router'
    import swiper from '@/components/Swiper'
    // import navBar from '@/components/NavBar'
    import {
        login1, user, getAllDesktop, getDesktopById, bannerList, sytServiceCenterList,
        diyGet, GetNews, GetappletList
    } from '@/service/home'
    import {getLocal} from '@/common/js/utils'
    import {Toast} from 'vant'
    import {mapState, mapMutations} from "vuex";
    import {useStore} from 'vuex'
    import {setLocal} from '@/common/js/utils'

    export default {
        name: 'home',
        components: {
            swiper,
            // navBar
        },

        computed: {
            ...mapState(["baseUrl"])
        },
        setup() {
            const store = useStore()
            const router = useRouter()
            const state = reactive({
                desktop: {},
                defaultIcon: require("../assets/isnull.png"),
                active: 0,
                images: [],
                appletList: [],
                appList: [],
                sytServiceCenterList: [],
                newsList: [],

                swiperList: [
                    {
                        "url": "https://newbee-mall.oss-cn-beijing.aliyuncs.com/images/banner-p50-pocket.png",
                        "redirectUrl": "https://juejin.im/book/6844733826191589390"
                    },
                    {
                        "url": "https://newbee-mall.oss-cn-beijing.aliyuncs.com/images/banner-iphone13.png",
                        "redirectUrl": "https://juejin.im/book/6844733826191589390"
                    },
                    {
                        "url": "https://newbee-mall.oss-cn-beijing.aliyuncs.com/images/banner-mate40.png",
                        "redirectUrl": "https://juejin.im/book/6844733826191589390"
                    }
                ], // 轮播图列表
                // swiperList: [], // 轮播图列表
                isLogin: false, // 是否已登录
                headerScroll: false, // 滚动透明判断
                loading: true,
                userInfo: {},
                blockslist: [],
            })
            // 1.手动的映射和绑定
            const mutations = mapMutations(["setUser", "setToken"])
            const newMutations = {}
            Object.keys(mutations).forEach(key => {
                newMutations[key] = mutations[key].bind({$store: store})
            })
            const {setUser, setToken} = newMutations


            onMounted(async () => {
                let setData = new FormData();
                setData.append('username', '0430');
                // setData.append('username', '10001');
                setData.append('password', 'sanyth123!');
                setData.append('uid', 'b0baee9d279d34fa1dfd71aadb908c3f');
                const {data} = await login1(setData)
                console.log(data.info.token)
                setLocal('token', data.info.token)
                setToken(data.info)
                state.loading = false

                const token = getLocal('token')
                if (token) {
                    state.isLogin = true
                    getUser();
                    init();
                }
            })

            nextTick(() => {
                window.addEventListener('scroll', () => {
                    let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
                    scrollTop > 100 ? state.headerScroll = true : state.headerScroll = false
                })
            })

            const init = async () => {
                let json = {
                    type: '移动端',
                }
                const {data} = await getAllDesktop(json)
                let Desktoplist = [];
                let desktop = {}
                if (data.code === "00000") {
                    Desktoplist = data.info
                }
                Desktoplist.forEach(item => {
                    if (item.type == '移动端') desktop = item
                })
                state.desktop = desktop
                await GetDesktopById()

            }
            const GetDesktopById = async () => {
                let Desktoplist = []
                const {data} = await getDesktopById({id: state.desktop.id})
                if (data.code === "00000") {
                    Desktoplist = data.info[0].blocks
                }
                Desktoplist.forEach(item => {
                    if (item.model === 'lunbotu') {
                        getBannerList(item);
                    }
                    if (item.model === 'wodefuwu') {
                        getSytServiceCenterList(item);
                    }
                    if (item.model === 'xinwen') {
                        console.log("humanCode====", store.state.humanCode)
                        getNewsList(item);
                    }
                    if (item.model === 'yingyongPT') {
                        getAppletList(item);
                    }
                    if (item.model === 'wodexixin') {
                        getAppletListdata(item);
                    }
                })
                state.blockslist = Desktoplist
            }

            const getAppletListdata = async () => {
                let json = {
                    page: 1,
                    pageSize: 8,
                    queryParam: {type: "小应用"}
                };
                const {data} = await GetappletList(json)
                console.log(`getAppletListdata`, data)
                // if (data.code === "00000") {
                //     state.swiperList = data.info
                // }
            }
            const getUser = async () => {
                const {data} = await user({})
                if (data.code === "00000") {
                    state.userInfo = data.info
                    setUser(data.info)
                    setLocal('userInfo', JSON.stringify(data.info))
                }
            }
            const getBannerList = async () => {
                const {data} = await bannerList({})
                if (data.code === "00000") {
                    // state.swiperList = data.info
                }
            }
            const getAppletList = async () => {
                let json = {
                    page: 1,
                    pageSize: 8,
                    queryParam: {type: "应用平台"}
                };
                const {data} = await GetappletList(json)
                if (data.code === "00000") {
                    state.appletList = data.info
                }
            }
            const getSytServiceCenterList = async (item) => {
                const {data} = await sytServiceCenterList({limit: item.limit})
                if (data.code === "00000") {
                    state.sytServiceCenterList = data.info
                }
            }
            const getNewsList = async (item) => {
                onClick(item)
            }
            const onClick = async (item) => {
                let key = state.active;
                let url = "";
                if (item.tabs && item.tabs.length > 0) {
                    url = item.tabs[key].dataSources;
                } else {
                    item.tabs.push({
                        name: "",
                        type: "",
                    });
                    if (item.dataSources && item.dataSources !== '') {
                        url = item.dataSources;
                    }
                }
                //配置的数据来源
                if (url && url !== '') {
                    let json = {page: 1, pageSize: item.limit, code: store.state.humanCode}
                    console.log(json)
                    const {data} = await diyGet(url, json)
                    console.log("diyGet====", data)
                    // let userInfo = this.$store.state.userInfo;
                    // const {data} = await diyGet(url,{limit: item.limit})

                    // http.post(url + (url.indexOf('?') != -1 ? '&' : '?') + `page=1&pageSize=` + item.limit, {code: userInfo ? userInfo.humancode : ''}).then(res => {
                    //     console.log(`newsList`, res)
                    //     if (res.status === 200 && res.data.code === "00000") {
                    //         // this.newsList = res.data.info
                    //         if (res.data.data.hasOwnProperty("list")) {
                    //             state.list = res.data.data.list;
                    //         } else {
                    //             let type = url.split("/");
                    //             state.list = res.data.data[type[type.length - 1]];
                    //         }
                    //     }
                    // }).

                } else {  //系统内部新闻发布
                    let params = {
                        categoryId: item.tabs[key] ? item.tabs[key].categoryId : "",
                        limit: item.limit
                    }
                    const {data} = await GetNews(params)
                    if (data.code === "00000") {
                        state.newsList = data.info
                    }
                    console.log("GetNews====", data)
                }
            }

            const handleServices = (tap) => {
                let path = '';
                switch (tap) {
                    case 'fw':
                        path = 'mine-service'
                        break
                    case 'sj':
                        path = 'mine-dealt'
                        break
                    case 'yy':
                        path = 'application'
                        break
                    case 'xw':
                        path = 'news-center'
                        break
                }
                router.push({path: path})
            }

            const tips = () => {
                Toast('敬请期待');
            }

            return {
                ...toRefs(state),
                handleServices,
                onClick,
                tips,
            }
        },
        // setup() {
        //     // const store = useStore()
        //     const router = useRouter()
        //     const state = reactive({
        //         swiperList: [
        //             {
        //                 "url":"https://newbee-mall.oss-cn-beijing.aliyuncs.com/images/banner-p50-pocket.png",
        //                 "redirectUrl":"https://juejin.im/book/6844733826191589390"
        //             },
        //             {
        //                 "url":"https://newbee-mall.oss-cn-beijing.aliyuncs.com/images/banner-iphone13.png",
        //                 "redirectUrl":"https://juejin.im/book/6844733826191589390"
        //             },
        //             {
        //                 "url":"https://newbee-mall.oss-cn-beijing.aliyuncs.com/images/banner-mate40.png",
        //                 "redirectUrl":"https://juejin.im/book/6844733826191589390"
        //             }
        //         ], // 轮播图列表
        //         isLogin: false, // 是否已登录
        //         headerScroll: false, // 滚动透明判断
        //         hots: [],
        //         newGoodses: [],
        //         recommends: [],
        //         categoryList: [
        //             {
        //                 name: '新蜂超市',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E8%B6%85%E5%B8%82%402x.png',
        //                 categoryId: 100001
        //             }, {
        //                 name: '新蜂服饰',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E6%9C%8D%E9%A5%B0%402x.png',
        //                 categoryId: 100003
        //             }, {
        //                 name: '全球购',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E5%85%A8%E7%90%83%E8%B4%AD%402x.png',
        //                 categoryId: 100002
        //             }, {
        //                 name: '新蜂生鲜',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E7%94%9F%E9%B2%9C%402x.png',
        //                 categoryId: 100004
        //             }, {
        //                 name: '新蜂到家',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E5%88%B0%E5%AE%B6%402x.png',
        //                 categoryId: 100005
        //             }, {
        //                 name: '充值缴费',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E5%85%85%E5%80%BC%402x.png',
        //                 categoryId: 100006
        //             }, {
        //                 name: '9.9元拼',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/9.9%402x.png',
        //                 categoryId: 100007
        //             }, {
        //                 name: '领劵',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E9%A2%86%E5%88%B8%402x.png',
        //                 categoryId: 100008
        //             }, {
        //                 name: '省钱',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E7%9C%81%E9%92%B1%402x.png',
        //                 categoryId: 100009
        //             }, {
        //                 name: '全部',
        //                 imgUrl: 'https://s.yezgea02.com/1604041127880/%E5%85%A8%E9%83%A8%402x.png',
        //                 categoryId: 100010
        //             }
        //         ],
        //         loading: true
        //     })
        //     onMounted(async () => {
        //         // const token = getLocal('token')
        //         // if (token) {
        //         //     state.isLogin = true
        //         //     // 获取购物车数据.
        //         //     store.dispatch('updateCart')
        //         // }
        //         // Toast.loading({
        //         //     message: '加载中...',
        //         //     forbidClick: true
        //         // });
        //         // const { data } = await getHome()
        //         // state.swiperList = data.carousels
        //         // state.newGoodses = data.newGoodses
        //         // state.hots = data.hotGoodses
        //         // state.recommends = data.recommendGoodses
        //         // state.loading = false
        //         // Toast.clear()
        //     })
        //
        //     nextTick(() => {
        //         window.addEventListener('scroll', () => {
        //             let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
        //             console.log("scrollTop======",scrollTop)
        //             scrollTop > 100 ? state.headerScroll = true : state.headerScroll = false
        //         })
        //     })
        //
        //     const goToDetail = (item) => {
        //         router.push({ path: `/product/${item.goodsId}` })
        //     }
        //
        //     const tips = () => {
        //         Toast('敬请期待');
        //     }
        //
        //     return {
        //         ...toRefs(state),
        //         goToDetail,
        //         tips
        //     }
        // },
    }
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';

    .home-header {
        position: fixed;
        left: 0;
        top: 0;
        .wh(100%, 50px);
        .fj();
        line-height: 50px;
        padding: 0 15px;
        .boxSizing();
        font-size: 15px;
        color: #fff;
        z-index: 10000;

        .nbmenu2 {
            color: @primary;
        }

        &.active {
            background: @primary;

            .nbmenu2 {
                color: #fff;
            }

            .login {
                color: #fff;
            }
        }

        .header-search {
            display: flex;
            .wh(100%, 20px);
            line-height: 20px;
            margin: 10px 0;
            padding: 5px 0;
            color: #232326;
            background: rgba(255, 255, 255, .7);
            border-radius: 20px;

            .app-name {
                padding: 0 10px;
                color: @primary;
                font-size: 20px;
                font-weight: bold;
                border-right: 1px solid #666;
            }

            .icon-search {
                padding: 0 10px;
                font-size: 17px;
            }

            .search-title {
                font-size: 14px;
                color: #666;
                line-height: 21px;
            }
        }

        .icon-iconyonghu {
            color: #fff;
            font-size: 22px;
        }

        .login {
            color: @primary;
            line-height: 52px;

            .van-icon-manager-o {
                font-size: 20px;
                vertical-align: -3px;
            }
        }
    }

    .category-list {
        display: flex;
        flex-shrink: 0;
        flex-wrap: wrap;
        width: 100%;
        padding-bottom: 13px;

        div {
            display: flex;
            flex-direction: column;
            width: 20%;
            text-align: center;

            img {
                .wh(36px, 36px);
                margin: 13px auto 8px auto;
            }
        }
    }

    .good {
        .good-header {
            background: #f9f9f9;
            height: 50px;
            line-height: 50px;
            text-align: center;
            color: @primary;
            font-size: 16px;
            font-weight: 500;
        }

        .good-box {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;

            .good-item {
                box-sizing: border-box;
                width: 50%;
                border-bottom: 1PX solid #e9e9e9;
                padding: 10px 10px;

                img {
                    display: block;
                    width: 120px;
                    margin: 0 auto;
                }

                .good-desc {
                    text-align: center;
                    font-size: 14px;
                    padding: 10px 0;

                    .title {
                        color: #222333;
                    }

                    .price {
                        color: @primary;
                    }
                }

                &:nth-child(2n + 1) {
                    border-right: 1PX solid #e9e9e9;
                }
            }
        }
    }

    .floor-list {
        width: 100%;
        padding-bottom: 50px;

        .floor-head {
            width: 100%;
            height: 40px;
            background: #F6F6F6;
        }

        .floor-content {
            display: flex;
            flex-shrink: 0;
            flex-wrap: wrap;
            width: 100%;
            .boxSizing();

            .floor-category {
                width: 50%;
                padding: 10px;
                border-right: 1px solid #dcdcdc;
                border-bottom: 1px solid #dcdcdc;
                .boxSizing();

                &:nth-child(2n) {
                    border-right: none;
                }

                p {
                    font-size: 17px;
                    color: #333;

                    &:nth-child(2) {
                        padding: 5px 0;
                        font-size: 13px;
                        color: @primary;
                    }
                }

                .floor-products {
                    .fj();
                    width: 100%;

                    img {
                        .wh(65px, 65px);
                    }
                }
            }
        }
    }


    .mineData {
        width: 100%;
        height: 140px;
        background: #fff;
        padding-left: 14px;
    }

    .mineDataTitle {
        height: 45px;
        line-height: 45px;
        color: #1baeae;
        padding-left: 2px;
        font-size: 15px;
        font-weight: bold;
        font-family: "微软雅黑";
        margin-top: 8px;
    }

    .dataContent {
        width: 100%;
        height: 80px;
        overflow-x: auto;
        margin-top: -12px;
    }

    .colorRed {
        color: #f94a59;
        font-size: 16px;
    }

    .colorBlue {
        color: #319cfe;
        font-size: 16px;
    }

    .newWrap {
        margin-top: 8px;
        background: #fff;
        padding: 5px 15px;
        position: relative;
    }

    .lookMoreDiy {
        float: right;
        right: 15px;
        top: 12px;
        font-size: 16px;
        color: #999;
    }

    .lookMore {
        position: absolute;
        right: 15px;
        top: 18px;
        font-size: 16px;
        color: #999;
        z-index: 1000;
    }

    .new {
        width: 100%;
        height: 25px;
        margin-bottom: 13px;
    }

    .newTitle {
        font-size: 14px;
        color: #333;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .newTag {
        float: left;
        width: 5px;
        height: 5px;
        background: #72bcfb;
        border-radius: 50%;
        margin-top: 7.5px;
        margin-right: 5px;
    }

    .date {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
    }


    .van-row {
        padding: 0;
    }

    .newRow {
        display: flex;
        padding-top: 3px;
        margin-bottom: 10px;
    }

    .newRow .titleWrap {
        flex-grow: 5;
    }

    .newRow .newImgWrap {
        flex-grow: 1;
        padding: 4px;
        padding-right: 0;
    }

    .newTitle1 {
        font-size: 14px;
        line-height: 22px;
        color: #333;
        display: block;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 5px;
    }

    .newElse {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
    }

    .newBlock {
        display: inline-block;
        color: #da3b35;
        width: 30px;
        height: 15px;
        line-height: 14px;
        border: 1px solid #da3b35;
        text-align: center;
        border-radius: 2px;
        margin-right: 10px;
    }

    .newFrom {
        font-size: 14px;
        color: #666;
        margin-right: 3px;
    }

    .van-col--8 {
        padding-left: 10px;
    }

    .newImg {
        width: 100px;
        height: 100%;
        border-radius: 5px;
    }


    .applyServiceWrap {
        width: 100%;
        height: 210px;
        margin-top: 6px;
        background: #fff;
    }


    .applyServiceTitle {
        height: 45px;
        line-height: 45px;
        color: #333;
        padding-left: 16px;
        font-size: 15px;
        font-weight: bold;
        font-family: "微软雅黑";
        margin: 5px 0px 0px 0px;
    }


    .serviceContent {
        width: 100%;
        height: 100px;
    }

    .service {
        float: left;
        width: 25%;
        text-align: center;
    }

    .service img {
        width: 30px;
        height: 30px;
    }

    .service p {
        font-size: 14px;
        color: #666;
    }

    .my-process-designer {
        height: 200px;
    }
</style>

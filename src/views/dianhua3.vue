<!-- src/views/Login.vue -->
<template>
    <div class="login-container">
        <!--        <van-nav-bar title="修改手机号" />-->
        <div style="background-color: #00a5ec;height: 100px;padding-top: 20px;" class="titleback">
            <p class="weui-form__tips" style="text-align: center;margin: 0 0 0 0;color: white;font-size: 24px;font-weight: bold;">
                <!--                请输入姓名身份证号验证身份-->
                最后一步<br/>
                请务必关注企业微信
            </p>
        </div>
<!--        <p class="weui-form__tips" style="margin: 20px;text-align: center;">-->
<!--            最后一步<br />请务必关注企业微信-->
<!--        </p>-->
        <div class="form-container">
            <div class="qrcode" style="text-align: center;">
                <img :src="qrcode_url" alt="">
            </div>
            <div class="sb-qrcode" style="text-align: center;">
<!--                <img src="${pageContext.request.contextPath}/style/sytxggl/platform/zunyi_qywx/update_info/img/zw.png" />-->
                <p>长按识别二维码关注</p>
            </div>
        </div>
    </div>
</template>

<script>
import {diyPost} from '@/service/home'
import AjaxApi from "@/service/dianhua";
import {mapState} from "vuex";

export default {
    data() {
        return {
            username: '',
            password: '',
            captchaInput: '',
            captchaUrl: '',
            errorMessage: '',
            xm: '',
            sfzh: '',
            sjh: '',
            qrcode_url: '',
            xsjh: '',
            imgshow: true
        };
    },
    created() {
        this.refreshCaptcha()
    },
    computed: {
        ...mapState(["baseUrl"])
    },
    methods: {
        refreshCaptcha(){
            let json = {

            }
            diyPost(AjaxApi.getQrcodeUrl,json).then(res => {
                console.log(res)
                if( res.data ){
                    this.qrcode_url = res.data.data
                }
            })
        },
    },
};
</script>

<style scoped>
.login-container {
    /*padding: 20px;*/
}
.form-container {
    margin: 20px;
}
.captcha-container {
    display: flex;
    align-items: center;
}
.captcha-image {
    margin-left: 10px;
    cursor: pointer;
    width: 100px;
    height: 40px;
    border: 1px solid #ccc;
}
</style>

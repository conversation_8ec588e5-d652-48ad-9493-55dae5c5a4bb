<template>
    <section>
        <div class="product-list-content">
            <s-header :name="'待办详情'" :newParams="params"></s-header>

            <van-tabs v-model:active="active" color="#48a3ea" title-active-color="#48a3ea"
                      @click-tab="changeTab">
                <van-tab v-for="(tab,index) in tabsOptions"
                         :title="tab.text"
                         :name="tab.value"
                         :key="index">
                </van-tab>
            </van-tabs>
        </div>
        <div class="content">
            <ZZ_Form ref="form" v-if="active==='sq'&&setformObj && processInstance && processInstance.formVariables && processInstance.processDefinition.formType == 10" :issubform="issubform"
                     :key="zzindex"
                     :fields="setfieldArr"
                     :setformObj="setformObj"
                     v-on:formsubmit="onFormSubmit"></ZZ_Form>
            <div v-if="active==='sq' && processInstance && processInstance.processDefinition && processInstance.processDefinition.formType == 20">
                <van-collapse v-model="activeNames">
                    <van-collapse-item v-for="(item,index) in allfieldlist" :key="index" :title="item.name" :name="index+1">
                        <div v-if="item.sfdt == '是' && active==='sq'&&setformObj && activeType==='0'">
                            <div v-if="item.dictFieldList.length > 0 && setfieldArr" >
                                <div v-for="(dicts,dictsindex) in setfieldArr.dtxxlist[item.id]" :key="dictsindex" class="boxSty">
                                    <div class="list-item-info">审批状态：{{subapprovedata.dtxxList[item.id] ? subapprovedata.dtxxList[item.id][dictsindex]['spzt'] : ''}}</div>
                                    <div class="list-item-info">修改方式：{{dicts.xgfs == 'add' ? '新增' : (dicts.xgfs == 'edit' ? '编辑' : (dicts.xgfs == 'delete' ? '删除' : '')) }}</div>
                                    <div v-for="(field,index) in item.dictFieldList" :key="index" class="list-item-info">{{field.fieldzh}}：{{dicts[field.fielden]}}</div>
                                    <div class="item_right_btn">
                                        <van-tag style="margin-right: 8px;" plain type="primary" @click="formdsp(item,dicts,'同意','dtxxList',dictsindex)">
                                            同意
                                        </van-tag>
                                        <van-tag style="margin-right: 8px;" plain type="danger" @click="formdsp(item,dicts,'不同意','dtxxList',dictsindex)">
                                            不同意
                                        </van-tag>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="item.sfdt == '是' && active==='sq'&&setformObj && activeType!=='0'">
                            <div v-if="item.dictFieldList.length > 0 && item.accountData && item.accountData.length>0" style="font-weight: bold;padding: 5px;margin-left: 5px;">
                                <van-tag plain type="primary" style="padding: 5px;" @click="formdadd(item,item.accountData)">
                                    新增
                                </van-tag>
                            </div>
                            <div v-if="item.dictFieldList.length > 0 && item.accountData && item.accountData.length>0" >
                                <div v-for="(dicts,index) in item.accountData" :key="index" class="boxSty">
<!--                                    <div class="list-item-info">审批状态：{{}}</div>-->
                                    <div class="list-item-info">修改方式：{{dicts.xgfs == 'add' ? '新增' : (dicts.xgfs == 'edit' ? '编辑' : (dicts.xgfs == 'delete' ? '删除' : '')) }}</div>
                                    <div v-for="(field,index) in item.dictFieldList" :key="index" class="list-item-info">{{field.fieldzh}}：{{dicts[field.fielden]}}</div>
                                    <div class="item_right_btn">
                                        <van-tag style="margin-right: 8px;" plain type="danger" @click="formdelete(dicts,item)">
                                            删除
                                        </van-tag>
                                        <van-tag style="margin-right: 8px;" plain type="primary" @click="formdedit(dicts,item)">
                                            编辑
                                        </van-tag>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="item.sfdt == '否' && active==='sq'&&setformObj && activeType==='0'">
                            <div v-if="item.dictFieldList.length > 0 && setfieldArr" >
                                <div v-for="(dicts,index) in Object.keys(setfieldArr.tmform[item.id])" :key="index" class="boxSty">
                                    <div class="list-item-info">审批状态：{{subapprovedata.baseList[item.id] && subapprovedata.baseList[item.id][index] ? subapprovedata.baseList[item.id][index]['spzt'] : ''}}</div>
                                    <div v-for="(field,is) in item.dictFieldList" :key="is">
                                        <div v-if="field.fielden == dicts" class="list-item-info">字段：{{field.fieldzh}}</div>
                                    </div>
                                    <div class="list-item-info">修改前：{{setformObj[dicts]}}</div>
                                    <div class="list-item-info">修改值：{{setfieldArr.tmform[item.id][dicts]}}</div>
                                    <div class="item_right_btn">
                                        <van-tag style="margin-right: 8px;" plain type="primary" @click="formdsp(item,dicts,'同意','baseList')">
                                            同意
                                        </van-tag>
                                        <van-tag style="margin-right: 8px;" plain type="danger" @click="formdsp(item,dicts,'不同意','baseList')">
                                            不同意
                                        </van-tag>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ZZ_Form style="padding-bottom: 80px;" ref="form" v-if="item.sfdt == '否' && active==='sq'&&setformObj && activeType!=='0'"
                                 :issubform="issubform"
                                 :fields="item.moblielist"
                                 :setformObj="setformObj"
                                 :gid="item.id"
                                 @toformsubmit="toformsubmit"
                                 v-on:formsubmit="onFormSubmit"></ZZ_Form>
                    </van-collapse-item>
                </van-collapse>
            </div>

            <approve-info-detail v-if="active==='sp'" :activeTab="active" :currentId="currentId"
                                 :processInstanceData="processInstance"
                                 :approveInfo="approveInfo"></approve-info-detail>

            <!-- 引入流程图组件-->
            <div v-if="active==='lct'" style="background-color: #fff;">
                <my-process-viewer v-if="bpmnShow" key="designer" v-model="bpmnXML" :bpmnXML="bpmnXML" :activityData="activityList" :processInstanceData="processInstance" :taskData="tasks"
                                   v-bind="bpmnControlForm"/>
            </div>
        </div>
        <div class="van-submit-bar" v-if="activeType==='0' || (activeType==='2'&&currentStatus!=='2')">
            <template v-if="activeType==='0'">
                <van-button class="van-button-diy" round type="primary" size="small" @click="tobanli()">办理流程
                </van-button>
            </template>
            <template v-if="activeType==='2'">
                <!--                <van-button round type="info" size="small">审&nbsp;&nbsp;&nbsp;批</van-button>-->
                <van-button round type="primary" size="small" @click="handleRemind">催&nbsp;&nbsp;&nbsp;办</van-button>
                <van-button round type="warning" size="small" @click="dialogRemindCancel=true">撤&nbsp;&nbsp;&nbsp;销</van-button>
                <van-button round type="danger" size="small" @click="dialogRemindDelete=true">删&nbsp;&nbsp;&nbsp;除
                </van-button>
            </template>
            <!--            <van-button round type="default" size="small" @click="handleCancleBack">返&nbsp;&nbsp;&nbsp;回</van-button>-->
        </div>
        <div class="van-submit-bar" v-if="(activeType==='2'&&currentStatus!=='1')">
            <template v-if="activeType==='2'">
                <!--                <van-button round type="info" size="small">审&nbsp;&nbsp;&nbsp;批</van-button>-->
                <van-button round type="primary" size="small" v-if="currentStatus=='1'" @click="handleRemind">催&nbsp;&nbsp;&nbsp;办</van-button>
                <van-button round type="primary" size="small" v-if="currentStatus!=='1'" @click="handleAudit">重新发起</van-button>
                <van-button round type="warning" size="small" v-if="currentStatus=='1'" @click="dialogRemindCancel=true">撤&nbsp;&nbsp;&nbsp;销</van-button>
                <van-button round type="danger" size="small" v-if="currentStatus=='1'" @click="dialogRemindDelete=true">删&nbsp;&nbsp;&nbsp;除
                </van-button>
            </template>
            <!--            <van-button round type="default" size="small" @click="handleCancleBack">返&nbsp;&nbsp;&nbsp;回</van-button>-->
        </div>
        <!-- 审批信息 -->
        <van-popup title="流程办理" v-model:show="dialogVisible" position="bottom" :style="{height:'66%'}"
                   :close-on-popstate="true">
            <div class="stuInfoWrap" v-if="processInstance&&runningTasks.length>0">
                <h4 class="van-doc-demo-block__title" style="text-align: center;">流程办理</h4>
                <van-cell-group>
                    <van-field label="当前节点" readonly v-model="runningTasks[0].name"/>
                    <van-field label="流程名" readonly v-model="processInstance.name"/>
                    <van-field v-if="processInstance.startUser" label="流程发起人" readonly
                               v-model="processInstance.startUser.humanName"
                               :error-message="processInstance.startUser.organizationnames"/>
                    <van-field readonly
                               clickable
                               label="审批意见"
                               v-model="reason"
                               placeholder="请选择常用意见"
                               @click="showPickerReason = true"/>
                    <van-popup v-model:show="showPickerReason" round position="bottom">
                        <van-picker show-toolbar
                                    :columns="reasonColumns"
                                    @cancel="showPickerReason = false"
                                    @confirm="onConfirmReason"/>
                    </van-popup>
                    <van-field label=" " v-model="reasonStr" type="textarea" placeholder="请输入审批意见" rows="4" autosize/>
                </van-cell-group>
                <div class="van-submit-bar">
                    <template v-for="(btn,index) in approveButtons">
                        <van-button round size="small"
                                    :key="index"
                                    :disabled="submitshow"
                                    :type="btn.type"
                                    v-if="index < 2"
                                    @click="handleClick(runningTasks[0],btn.click)">{{btn.name}}<span v-if="submitshow">({{countdown}})</span>
                        </van-button>
                    </template>
                    <van-button @click="dialogVisible = false" round size="small" type="primary"> 取消</van-button>
                    <template v-if="approveButtons.length>2">
                        <van-button round size="small" type="primary" style="width: 22%;"
                                    @click="moreBtnVisible=true">更多...
                        </van-button>
                    </template>
                </div>
            </div>
        </van-popup>
        <!--更多操作-->
        <van-popup v-model:show="moreBtnVisible" position="bottom" :style="{height:'auto',width:'100%'}">
            <div style="margin-bottom: 60px; ">
                <div style="display: inline-block;width: 50%;" v-for="(btn,index) in approveButtons"
                     :key="index">
                    <van-button round block style="width: 90%; margin: 16px;"
                                :type="btn.type==='success'?'info':btn.type"
                                v-if="index > 1"
                                @click="handleClick(runningTasks[0],btn.click)">{{btn.name}}
                    </van-button>
                </div>
            </div>
            <div class="van-submit-bar">
                <span style="border: unset;font-size: 16px;" @click="moreBtnVisible = false"> 取消
                </span>
            </div>
        </van-popup>
        <!--取消流程-->
        <van-popup title="取消流程" v-model:show="dialogRemindCancel" position="bottom" :style="{height:'66%'}"
                   :close-on-popstate="true">
            <div class="stuInfoWrap">
                <h4 class="van-doc-demo-block__title" style="text-align: center;">取消流程</h4>
                <van-cell-group>
                    <van-field label-width="22em" label="此操作将结束该流程，是否继续？" readonly/>
                    <van-field required v-model="cancelReason" type="textarea" placeholder="请输入取消原因" rows="6" autosize/>
                </van-cell-group>
                <div class="van-submit-bar">
                    <van-button @click="dialogRemindCancel = false" round size="small"> 取消</van-button>
                    <van-button round size="small" type="primary"
                                @click="handleRemindCancel">确定
                    </van-button>
                </div>
            </div>
        </van-popup>
        <!--删除流程-->
        <van-popup title="删除流程" v-model:show="dialogRemindDelete" position="bottom" :style="{height:'66%'}"
                   :close-on-popstate="true">
            <div class="stuInfoWrap">
                <h4 class="van-doc-demo-block__title" style="text-align: center;">删除流程</h4>
                <van-cell-group>
                    <van-field label-width="22em" label="此操作将永久删除该流程，是否继续?" readonly/>
                    <van-field required v-model="deleteReason" type="textarea" placeholder="请输入删除原因" rows="6" autosize/>
                </van-cell-group>
                <div class="van-submit-bar">
                    <van-button @click="dialogRemindDelete = false" round size="small"> 取消</van-button>
                    <van-button round size="small" type="primary"
                                @click="handleRemindDelete">确定
                    </van-button>
                </div>
            </div>
        </van-popup>
        <!-- 人员选择弹窗 -->
        <van-popup v-model:show="showSelectPopup" position="bottom" :style="{ height: '80%',width:'101%' }">
            <SetRang :code="code"
                     :rang="rang"
                     :setMark="currentSelectType"
                     :nextNodesList="nextNodesList"
                     @update:checkChange="onCheckChange"
                     @update:handleRangCancle="selectedUser"></SetRang>
        </van-popup>
        <!-- 下环节办理人 -->

        <van-popup v-model:show="itemformshow" position="bottom" :style="{ height: '80%',width:'101%' }">
            <ZZ_Form style="padding-bottom: 80px;" ref="form"
                     :fields="itemformlist"
                     :setformObj="itemformdata"
                     v-on:formsubmit="onFormSubmit"></ZZ_Form>
            <div class="van-submit-bar">
                <van-button round size="small" type="default" @click="subonCancel">取消</van-button>
                <van-button round size="small" type="success" @click="subonConfirm">确认</van-button>
            </div>
        </van-popup>
        <van-popup v-model:show="showReturnPopup" position="bottom" :style="{ height: '66%',width:'100%' }">
            <van-field v-model="result"
                       is-link
                       readonly
                       name="picker"
                       label="驳回节点"
                       placeholder="点击选择驳回节点"
                       @click="showPicker = true"/>
            <van-popup v-model:show="showPicker" position="bottom">
                <van-picker :columns="columns"
                            :columns-field-names="customFieldName"
                            @confirm="onConfirm"
                            @cancel="showPicker = false"/>
            </van-popup>
            <van-field label="审批方式">
                <template #input>
                    <van-radio-group v-model="approveWay" direction="horizontal">
                        <van-radio name="1">逐级审批</van-radio>
                        <van-radio name="2">跳过审批</van-radio>
                    </van-radio-group>
                </template>
            </van-field>

            <div class="div-tips">说明：<br>
                1、逐级审批：驳回到某个节点重新提交后，需要从该节点起重新逐级走审批流程（流程严谨）<br>
                2、跳过审批：驳回到某个节点重新提交后，跳过中间的节点直接回到本节点审批（节省时间）<br>
                (注：如驳回的节点存在多个条件分支，该节点办理人修改提交后又满足了新的条件分支，则按新的分支走向逐级审批，可能不会回到原节点)"
            </div>
            <div class="van-submit-bar">
                <van-button round size="small" type="primary" @click="handleBack"> 提交</van-button>
                <van-button @click="showReturnPopup = false" round size="small"> 取消</van-button>
            </div>
        </van-popup>
    </section>
</template>

<script>
    import sHeader from '@/components/SimpleHeader'
    import AjaxApi from "@/utils/api";
    import ZZ_Form from "@/components/form/ZZ_Form";
    import ApproveInfoDetail from "@/components/ApproveInfoDetail";
    import {GetBpmVariabledata} from "@/service/bpmProcessDefinition";
    import {diyGet, diyGet2, diyPost, user} from '@/service/home'
    import {remindRuntimeTask, cancelProcessInstance, deleteProcessInstance,getActivityList} from '@/service/bpmProcessInstance'
    import {mapState} from 'vuex'
    import {forEachFormJson,} from "@/utils/forEachFormJson"
    import {useRoute,} from "vue-router";
    import {computed, onMounted, reactive, toRefs, watch,} from "vue";
    import processMethod from '@/utils/mineDealDetail.js'
    import SetRang from '@/components/Rang'
    import router from "@/router";
    import {showConfirmDialog, showToast, showFailToast, showNotify} from "vant";
    import {getLocal} from "@/common/js/utils";

    export default {
        components: {
            ZZ_Form,
            sHeader,
            ApproveInfoDetail,
            SetRang
        },
        setup() {
            const route = useRoute()
            const state = reactive({
                dialogRemindCancel: false,
                submitshow: false,
                activeNames: [1,2,3,4,5,6,7],
                subapprovedata: {
                    baseList: {},
                    dtxxList: {},
                },
                cancelReason: null,
                dialogRemindDelete: false,
                deleteReason: null,
                issubform: false,
                currentId: null,
                processDefinitionId: null,
                DefinitionId: null,
                tasks: null,
                currentTask: null,
                InstanceId: null,
                DefinitionKey: null,
                zzindex: 0,
                activeTab: null,
                activeType: null,
                currentStatus: null,
                activityList: [],
                user: {},
                pageTotal: 0,
                active: 'sq',
                tabsOptions: [
                    {text: '申请信息', value: 'sq'},
                    {text: '审批记录', value: 'sp'},
                    {text: '流程图', value: 'lct'},
                ],
                processInstance: null,
                formObj: {},
                xmlString: null,
                formVariables: {},
                bpmnControlForm: {
                    prefix: "activiti"
                },
                taskId: '',
                bpmnXML: '',
                bpmnShow: false,
                fieldsPermission: {},
                moreBtnVisible: false,
                dialogVisible: false,
                approveButtons: [],
                approveInfo: null,
                runningTasks: [],
                showPickerReason: false,
                reason: null,
                reasonStr: null,
                reasonColumns: [], // 我的常用意见
                allfieldlist:[],
                reasonList: null,//我的常用意见list
                auditForms: [],
                setfieldArr: null,
                setformObj: null,
                params: null,
                showSelectPopup: false,
                code: [],
                rang: [],
                currentSelectType: null,
                handleUpdate: {},
                formCustomCreateData: {},
                returnTask: {},
                showReturnPopup: false,
                showPicker: false,
                columns: [],
                customFieldName: {
                    text: 'name',
                    value: 'id',
                },
                dtxxlist: {},
                changeformdata: {},
                submitformdata: {},
                result: null,
                approveWay: null,
                returnId: null,
                backUrl: null,
                serviceLogBackId: null,
                serviceLogBackName: null,
                selectNextUserList: null,
                approveShow: false,
                nextuserOpen: false,
                nextNodesList: null,
                countdown: 5, // 倒计时初始值为5秒
                timer: null,
                changegid: null,
                itemformdata: {},
                itemformshow: false,
                itemformlist: [],
            })

            onMounted(async () => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                const {currentId, processDefinitionId, activeTab, activeType, backUrl, currentStatus, backId, backName,taskId} = route.query
                state.currentId = currentId
                state.processDefinitionId = processDefinitionId
                state.currentStatus = currentStatus
                state.activeTab = activeTab
                state.activeType = activeType
                state.backUrl = backUrl ? backUrl : '/mine-dealt'
                state.serviceLogBackId = backId
                state.serviceLogBackName = backName
                state.taskId = taskId
                const {data} = await user({})
                state.user = data

                await getBpmProcessInstanceExt()
                // await getBpmTask()
                // await getsptzdata()
                getMyCommentList()
            })

            watch(() => state.activeType, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal === '抄送' || newVal === '催办') {
                    state.params = JSON.stringify({currentType: newVal, currentTab: state.activeTab})
                } else if (newVal === '预约记录') {
                    state.params = JSON.stringify({
                        setNewOldActiveTab: newVal,
                        id: state.serviceLogBackId,
                        name: state.serviceLogBackName
                    })
                } else if (newVal === '任务填报') {
                    state.params = JSON.stringify({
                        taskId: state.serviceLogBackId,
                        name: state.serviceLogBackName,
                        setNewOldActiveTab: state.activeTab,
                    })
                } else {
                    state.params = JSON.stringify({setNewOldActiveTab: Number(newVal)})
                }
            })

            const codes = computed(() => {
                let arr = [];
                if (state.rang) {
                    state.rang.forEach(item => {
                        arr.push(item.id)
                    })
                }
                return arr
            })

            const getRegExp = function (validatorName) {
                const commonRegExp = {
                    number: /^\\d+(\\.\\d+)?$/,
                    letter: /^[A-Za-z]+$/,
                    letterAndNumber: /^[A-Za-z0-9]+$/,
                    mobilePhone: /^[1][3-9][0-9]{9}$/,
                    letterStartNumberIncluded: /^[A-Za-z]+[A-Za-z\\d]*$/,
                    noChinese: /^[^\u4e00-\u9fa5]+$/,
                    chinese: /^[\u4e00-\u9fa5]+$/,
                    email: /^([-_A-Za-z0-9.]+)@([_A-Za-z0-9]+\\.)+[A-Za-z0-9]{2,3}$/,
                    url: '/^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$/',
                }
                return commonRegExp[validatorName]
            }

            const tobanli = () => {
                let submit = true
                if( state.processInstance.processDefinition.formType == 10 ){
                    state.setfieldArr.forEach(item =>{
                        if( item.options.required ){
                            if(item.options.validation  && item.options.validation!==''){
                                if(!getRegExp(item.options.validation).test(state.setformObj[item.options.name])){
                                    submit = false
                                }
                            }
                            if(!state.setformObj[item.options.name] || state.setformObj[item.options.name] == '' || state.setformObj[item.options.name] == []){

                                submit = false
                            }

                        }
                    })
                }

                if(submit){
                    state.dialogVisible = true;
                }else {
                    showFailToast('请按要求填写表单')
                }

            }

            const getsptzdata = () => {
                let list = []
                list.push(state.setformObj)
                GetBpmVariabledata({formFields: list}).then(res => {
                    if (res.data.code != "00000") {
                        this.$modal.msgError(res.data.info);
                        return;
                    }else {
                        state.setformObj = JSON.parse(res.data.info)[0];
                        // console.log(`state.setformObj55555555555555555555555`,state.setformObj)
                        state.zzindex++
                    }
                })
            }

            const getBpmTask = () => {
                state.runningTasks = [];
                let json = {processInstanceId: state.currentId}
                diyPost(AjaxApi.getBpmTask, json).then(response => {
                    // 审批记录
                    if (!response.data) {
                        return;
                    }
                    if (response.data.code != "00000" || !response.data.info) {
                        return;
                    }
                    state.tasks = response.data.info;
                    state.approveInfo = response.data.info;
                    console.log(`state.approveInfo==========================================================`,state.approveInfo)
                    // 排序，将未完成的排在前面，已完成的排在后面；
                    state.tasks.sort((a, b) => {
                        // 有已完成的情况，按照完成时间倒序
                        if (a.endTime && b.endTime) {
                            return b.endTime - a.endTime;
                        } else if (a.endTime) {
                            return 1;
                        } else if (b.endTime) {
                            return -1;
                            // 都是未完成，按照创建时间倒序
                        } else {
                            return b.createTime - a.createTime;
                        }
                    });
                    // 需要审核的记录
                    let user = state.user.info;
                    if (user) {
                        // const humancode = user.humanName;
                        // const userId = user.humanId;
                        state.tasks.forEach(task => {
                            if (task.id !== state.taskId) {
                                return;
                            }
                            // if (task.result !== 1 && task.result !== 6 && task.result !== 7 || task.endTime != null) { // 只有待处理1 委派6 转办7才需要
                            //     return;
                            // }
                            state.currentTask = task
                            let flag = true;


                            // let isOrg = false;
                            // let isOrgAttribute = false;
                            //
                            // if (task.groupIds) {
                            //     user.organizations.forEach(o => {
                            //         if (task.groupIds.indexOf(o.id) !== -1 && task.groupIds.indexOf("HEAD") === -1) {
                            //             isOrg = true;
                            //         }
                            //     });
                            //     if (user.orgAttributes) {
                            //         user.orgAttributes.forEach(o => {
                            //             if (task.groupIds.indexOf(o) !== -1) {
                            //                 isOrgAttribute = true;
                            //             }
                            //         });
                            //     }
                            // }
                            // //判断用户是自己，角色、部门包含自己的
                            // //1.非多实例，选择角色 组织机构，assigneeUser可能为空
                            // //2.多实例，assigneeUser和groupIds 都需要判断
                            // if (task.assigneeUser && task.groupIds) { //多实例
                            //     if ((isOrg || isOrgAttribute || (task.groupIds && (task.groupIds.indexOf(user.roleId) !== -1 || task.groupIds.indexOf(userId) !== -1 || task.groupIds.indexOf(humancode) !== -1)
                            //     ) && task.assigneeUser.humanCode === humancode || task.assigneeUser.humanCode === humancode)) {
                            //         flag = true;
                            //     }
                            // } else {  //非多实例
                            //     if (isOrg || isOrgAttribute || (task.assigneeUser && task.assigneeUser.humanCode === humancode) ||
                            //         (task.groupIds && (task.groupIds.indexOf(user.roleId) !== -1 || task.groupIds.indexOf(userId) !== -1 || task.groupIds.indexOf(humancode) !== -1)
                            //         )) {
                            //         flag = true;
                            //     }
                            // }
                            if (flag) {
                                state.runningTasks.push({...task});
                                state.auditForms.push({
                                    reason: ''
                                });
                                state.InstanceId = task.processInstance.id;
                                state.DefinitionId = task.processInstance.processDefinitionId;
                                state.DefinitionKey = task.definitionKey;
                                state.approveButtons = task.buttonsJson;
                                if (state.processInstance.processDefinition && state.processInstance.processDefinition.formType == 10) {
                                    if (task.fieldsPermissionJson && task.fieldsPermissionJson.length > 0) {
                                        state.fieldsPermission = JSON.parse(task.fieldsPermissionJson[0].value);
                                        state.fieldsPermission.forEach(item=>{
                                            if(state.setfieldArr && state.setfieldArr.length > 0){
                                                state.setfieldArr.forEach(data=>{
                                                    if(data.type == "sub-form"){
                                                        console.log(`state.setformObj[data.options.name]=====================================================`,state.setformObj[data.options.name])
                                                        state.setformObj[data.options.name].forEach(field=>{
                                                            field.forEach(wid=>{
                                                                if(wid.id == item.name){
                                                                    wid.options.disabled = item.disabled;
                                                                    wid.options.required = item.required;
                                                                    wid.options.hidden = item.hidden;
                                                                    if(wid.options.isHidden){
                                                                        wid.options.hidden = wid.options.isHidden;
                                                                    }
                                                                    if(wid.options.isRequired){
                                                                        wid.options.required = wid.options.isRequired;
                                                                    }
                                                                }
                                                            })

                                                        })
                                                        data.widgetList.forEach(wid=>{
                                                            if(wid.id == item.name){
                                                                wid.options.disabled = item.disabled;
                                                                wid.options.required = item.required;
                                                                wid.options.hidden = item.hidden;
                                                                if(wid.options.isHidden){
                                                                    wid.options.hidden = wid.options.isHidden;
                                                                }
                                                                if(wid.options.isRequired){
                                                                    wid.options.required = wid.options.isRequired;
                                                                }
                                                            }
                                                        })
                                                        data.fieldSchemaData.forEach(field=>{
                                                            field.forEach(wid=>{
                                                                if(wid.id == item.name){
                                                                    wid.options.disabled = item.disabled;
                                                                    wid.options.required = item.required;
                                                                    wid.options.hidden = item.hidden;
                                                                    if(wid.options.isHidden){
                                                                        wid.options.hidden = wid.options.isHidden;
                                                                    }
                                                                    if(wid.options.isRequired){
                                                                        wid.options.required = wid.options.isRequired;
                                                                    }
                                                                }
                                                            })

                                                        })
                                                    }
                                                    if(data.options.name == item.name){
                                                        data.options.disabled = item.disabled;
                                                        data.options.required = item.required;
                                                        data.options.hidden = item.hidden;
                                                        if(data.options.isHidden){
                                                            data.options.hidden = data.options.isHidden;
                                                        }
                                                        if(data.options.isRequired){
                                                            data.options.required = data.options.isRequired;
                                                        }
                                                        if(  item.defaultValue !== undefined && item.defaultValue !== null && item.defaultValue !== "" && item.defaultValue.length !== 0 ){
                                                            // data.options.defaultValue = item.defaultValue;
                                                            state.setformObj[item.name] = item.defaultValue;

                                                        }
                                                    }
                                                })
                                            }


                                            if(item.reason){
                                                if(item.name.includes('spzj')){
                                                    if(state.setformObj && state.setformObj[item.name]){
                                                        let spzj = JSON.parse(state.setformObj[item.name])
                                                        if(spzj ){
                                                            spzj.spsj = '${date.now,yyyy-MM-dd}'
                                                            spzj.spr = '${currentUser.humanname}'
                                                            spzj.qm = '${currentUser.signature}'

                                                        }
                                                        state.setformObj[item.name] = JSON.stringify(spzj);
                                                    }
                                                }
                                            }
                                        })
                                    }
                                    getsptzdata()
                                }
                            }
                        });
                    }
                    // 取消加载中
                    state.tasksLoad = false;
                })
            }
            const getMyCommentList = () => {
                diyPost(AjaxApi.myCommentList, {}).then(res => {
                    if (res.data.code === '00000') {
                        let rData = res.data.info
                        if (rData.length > 0) {
                            rData.forEach(e => {
                                state.reasonColumns.push({
                                    text: e.reason,
                                    value: e.reason
                                })
                            })
                        }
                        state.reasonList = rData;
                    }
                })
            }

            const handleAudit = () => {
                let expression = {}
                if (state.processInstance.conditionIds) {
                    let conditionIds = JSON.parse(state.processInstance.conditionIds);
                    conditionIds.forEach(item => {
                        expression[item] = state.setformObj[item]
                    })
                }
                state.formVariables.mobilefields = state.setfieldArr;
                state.formVariables.fieldsValues = state.setformObj;
                if(state.processInstance.formType == 10){
                    state.formVariables.mobilefields = state.setfieldArr;
                    state.formVariables.fieldsValues = state.setformObj;
                }else {
                    state.formVariables.formCustomCreateData = state.submitformdata;
                    state.formVariables.mobilefields = state.processInstance.mobilefields;
                    state.formVariables.formConfig = state.processInstance.formConfig;
                    state.formVariables.widgetList = state.processInstance.widgetList;
                    state.formVariables.fieldsValues = state.processInstance.fieldsValues;

                }
                const data = {
                    processDefinitionId: state.processDefinitionId,
                    variables: state.formVariables,
                    expressionVariables: expression,
                }
                diyPost(AjaxApi.bpmProcessInstanceExtcreate, data).then(response => {
                    if (response.data.code === '00000') {
                        if (state.userInfo.humanCode !== "syt_visitor") {
                            router.push({path: '/mine-dealt',query:{isNavbar: true}})
                        } else {
                            router.push({
                                path: state.backUrl,
                                query: {}
                            });
                        }
                    } else {
                        showNotify({type: 'warning', message: response.data.info});
                    }
                })
            }

            const getBpmProcessInstanceExt = () => {
                let json = {
                    id: state.currentId
                }
                diyPost(AjaxApi.getBpmProcessInstanceExt, json).then(res => {
                    if (res.status === 200 && res.data.code === '00000') {
                        state.processInstance = res.data.info
                        //服务-我的预约记录，查看详情，重新赋值（流程图ID）
                        state.processDefinitionId = state.processDefinitionId ? state.processDefinitionId : res.data.info.processDefinition.id
                        // if (res.data.info) initFormObj();
                        if (res.data.info && state.processInstance.processDefinition.formType == 20 && state.processInstance.processDefinition.formCustomViewPath == "userInfoapproveform") {
                            inituserFormObj();
                        } else {
                            initFormObj();
                        }
                    }
                })
            }

            const inituserFormObj = () => {
                let fieldArr = [];
                let newFormObj = {};
                if (state.processInstance) {
                    state.formCustomCreateData = state.processInstance.formVariables.formCustomCreateData;
                    diyGet(AjaxApi.getDetailbycode + `?humancode=` + state.userInfo.humanCode).then(res => {
                        newFormObj = res.data.data;
                        state.setformObj = newFormObj;
                        console.log(`newFormObj===`,newFormObj)
                    })

                    diyGet2(AjaxApi.allAndField).then(res => {
                        let resData = res.data.data;
                        fieldArr = resData;
                        fieldArr.forEach(item=>{
                            let newArr = [];
                            if(item.dictFieldList.length>0){
                                if(item.sfdt == '否'){
                                    item.dictFieldList.forEach(field=>{
                                        let dicData = [];
                                        if(field.fieldData || field.fieldAttribute){
                                            if(!field.fieldData){
                                                if(field.fieldAttribute){
                                                    let arr = field.fieldAttribute.split("#")
                                                    arr.forEach(i=>{
                                                        dicData.push({
                                                            label: i,
                                                            value: i,
                                                        })
                                                    })
                                                }
                                            }else {
                                                if(field.fieldAttribute){
                                                    // dicUrl = item.fieldData;
                                                    let arr = field.fieldAttribute.split("#")
                                                    diyPost(field.fieldData, {}).then(res => {
                                                        let resData = res.data;
                                                        console.log(`resData`,resData)

                                                        if (res.data.info && res.data.info.length>0) {
                                                            resData.info.forEach(i=>{
                                                                dicData.push({
                                                                    label: i[arr[0]],
                                                                    value: i[arr[1]],
                                                                })
                                                            })
                                                            console.log(`dicData`,dicData)
                                                        }
                                                    })
                                                }

                                            }
                                        }
                                        newArr.push({
                                            type: field.fieldType == 'upload' ? 'file-upload' : field.fieldType,
                                            values: null,
                                            options:{
                                                name: field.fielden,
                                                label: field.fieldzh,
                                                disabled: field.sfbj == '否',
                                                optionItems: dicData ? dicData : [],
                                            }
                                        })
                                    })
                                    item.moblielist = newArr;
                                }else {
                                    if(state.setformObj && state.setformObj.accountListMap && state.setformObj.accountListMap[item.id]){

                                        item.accountData = state.setformObj.accountListMap[item.id]
                                    }
                                    console.log(`item.id`,item.id)
                                }

                            }
                            // item.moblielist = newArr;
                        })
                        state.allfieldlist = fieldArr;
                        state.setfieldArr = state.processInstance.formVariables.formCustomCreateData;
                        console.log(`state.setfieldArr`,state.setfieldArr)
                    })
                    getBpmTask()

                }
            }

            const initFormObj = () => {
                let arr = [];
                let fieldArr = [];
                let newFormObj = {}
                if (state.processInstance) {
                    // console.log(`state.processInstance`,state.processInstance)
                    state.formVariables = state.processInstance.formVariables;
                    GetBpmVariabledata({formFields: state.processInstance.formVariables.mobilefields}).then(res => {
                        if (res.data.code != "00000") {
                            this.$modal.msgError(res.data.info);
                            return;
                        }else {
                            // console.log(`GetBpmVariabledata`,JSON.parse(res.data.info))
                            state.processInstance.formVariables.mobilefields = JSON.parse(res.data.info);
                            if (state.processInstance.formVariables.mobilefields) {
                                arr = state.processInstance.formVariables.mobilefields
                                let newObj = {}
                                if (typeof arr[0] == 'string') {
                                    arr.forEach(item => {
                                        newObj = JSON.parse(item)
                                        newObj.readonly = state.activeType === '0' ? false : true
                                        fieldArr.push(newObj)
                                    })
                                } else {
                                    arr.forEach(item => {
                                        newObj = item
                                        newObj.readonly = state.activeType === '0' ? false : true
                                        fieldArr.push(newObj)
                                    })
                                }
                                // console.log(`state.processInstance.formVariables.fieldsValues`,state.processInstance.formVariables.fieldsValues)
                                if (state.processInstance.formVariables.fieldsValues == String) {
                                    newFormObj = JSON.parse(state.processInstance.formVariables.fieldsValues);
                                } else {
                                    newFormObj = state.processInstance.formVariables.fieldsValues;
                                }
                            } else {
                                forEachFormJson(state.processInstance.formVariables.widgetList, arr)
                                let newObj = {}
                                if (typeof arr[0] == 'string') {
                                    arr.forEach(item => {
                                        newObj = JSON.parse(item)
                                        newObj.readonly = state.activeType === '0' ? false : true
                                        let setVal = newObj.values
                                        if (newObj.type === "radio" || newObj.type === "checkbox" || newObj.type === "select") setVal = newObj.values ? String(newObj.values) : null
                                        fieldArr.push(newObj)
                                        newFormObj[newObj.id] = setVal;
                                    })
                                } else {
                                    arr.forEach(item => {
                                        newObj = item
                                        newObj.readonly = state.activeType === '0' ? false : true
                                        let setVal = newObj.values
                                        if (newObj.type === "radio" || newObj.type === "checkbox" || newObj.type === "select") setVal = newObj.values ? String(newObj.values) : null
                                        fieldArr.push(newObj)
                                        newFormObj[newObj.id] = setVal;
                                    })
                                }
                            }
                            state.setfieldArr = fieldArr;
                            state.setformObj = newFormObj;
                            // console.log(`111111111`,state.setfieldArr,state.setformObj)
                            getBpmTask()

                        }
                    })
                    // if (state.processInstance.formVariables.mobilefields) {
                    //     arr = state.processInstance.formVariables.mobilefields
                    //     let newObj = {}
                    //     if (typeof arr[0] == 'string') {
                    //         arr.forEach(item => {
                    //             newObj = JSON.parse(item)
                    //             newObj.readonly = state.activeType === '0' ? false : true
                    //             fieldArr.push(newObj)
                    //         })
                    //     } else {
                    //         arr.forEach(item => {
                    //             newObj = item
                    //             newObj.readonly = state.activeType === '0' ? false : true
                    //             fieldArr.push(newObj)
                    //         })
                    //     }
                    //     if (state.processInstance.formVariables.fieldsValues == String) {
                    //         newFormObj = JSON.parse(state.processInstance.formVariables.fieldsValues);
                    //     } else {
                    //         newFormObj = state.processInstance.formVariables.fieldsValues;
                    //     }
                    // } else {
                    //     forEachFormJson(state.processInstance.formVariables.widgetList, arr)
                    //     let newObj = {}
                    //     if (typeof arr[0] == 'string') {
                    //         arr.forEach(item => {
                    //             newObj = JSON.parse(item)
                    //             newObj.readonly = state.activeType === '0' ? false : true
                    //             let setVal = newObj.values
                    //             if (newObj.type === "radio" || newObj.type === "checkbox" || newObj.type === "select") setVal = newObj.values ? String(newObj.values) : null
                    //             fieldArr.push(newObj)
                    //             newFormObj[newObj.id] = setVal;
                    //         })
                    //     } else {
                    //         arr.forEach(item => {
                    //             newObj = item
                    //             newObj.readonly = state.activeType === '0' ? false : true
                    //             let setVal = newObj.values
                    //             if (newObj.type === "radio" || newObj.type === "checkbox" || newObj.type === "select") setVal = newObj.values ? String(newObj.values) : null
                    //             fieldArr.push(newObj)
                    //             newFormObj[newObj.id] = setVal;
                    //         })
                    //     }
                    // }
                }
                // state.setfieldArr = fieldArr;
                // state.setformObj = newFormObj
            }
            const getBpmnXml = () => {
                let json = {
                    id: state.processDefinitionId
                }
                diyPost(AjaxApi.getBpmnXml, json).then(res => {
                    if (res.status === 200 && res.data.code === '00000') {
                        state.bpmnXML = res.data.info;
                        let json = state.processInstance.id
                        getActivityList( json).then(res => {
                            if (!res.data) {
                                // this.$message.error('查询不到活动列表！');
                                return;
                            }
                            if (res.data.code != "00000" || !res.data.info) {
                                // this.$message.error('查询不到活动列表！');
                                return;
                            }
                            state.activityList = res.data.info;
                            state.bpmnShow = true
                        })
                    }
                })
            }
            const changeTab = (tab) => {
                state.active = tab.name
                if (tab.name === 'lct') {
                    getBpmnXml();
                    // togetActivityList();
                } else if (tab.name === 'sp') {
                    // getBpmProcessInstanceExt();
                }
            }
            const onConfirmReason = (value) => {
                state.reason = value.selectedValues[0];
                state.reasonStr = value.selectedValues[0];
                state.showPickerReason = false
            }

            // const startCountdown = () => {
            //     state.timer = setInterval(() => {
            //         if (state.countdown > 0) {
            //             state.countdown--;
            //         } else {
            //             clearInterval(state.timer); // 清除定时器
            //         }
            //     }, 1000);
            // }

            const handleClick = (item, o) => {
                state.submitshow = true;
                state.countdown = 5;
                state.timer = setInterval(() => {
                    if (state.countdown > 0) {
                        state.countdown--;
                    } else {
                        clearInterval(state.timer); // 清除定时器
                    }
                }, 1000);
                setTimeout(()=>{
                    state.submitshow = false;
                    // state.countdown = 5
                    // startCountdown
                },5000)
                let split = o.split(",");
                if (split.length > 1) {
                    let func = split[0];
                    let param = split[1] && split[1] !== '' ? split[1] : '';
                    processMethod[func](item, param, state.processInstance, state.formVariables, state.setfieldArr, state.setformObj, state.reasonStr,state.DefinitionId,state.DefinitionKey,state.subapprovedata,state.formCustomCreateData)
                    let processMethoddata = processMethod[func](item, param, state.processInstance, state.formVariables, state.setfieldArr, state.setformObj, state.reasonStr,state.DefinitionId,state.DefinitionKey,state.subapprovedata,state.formCustomCreateData)
                    if(processMethod[func](item, param, state.processInstance, state.formVariables, state.setfieldArr, state.setformObj, state.reasonStr,state.DefinitionId,state.DefinitionKey,state.subapprovedata,state.formCustomCreateData)){
                        state.handleUpdate = processMethod[func](item, param, state.processInstance, state.formVariables, state.setfieldArr, state.setformObj, state.reasonStr,state.DefinitionId,state.DefinitionKey,state.subapprovedata,state.formCustomCreateData)
                        let data = state.handleUpdate.data
                        data.id = item.id
                        diyPost(AjaxApi.getNextNodesByProcessDefinitionIdAndTaskDefinitionKey, data).then(response => {
                            if (response.data.code === '00000') {
                                let info = response.data.info
                                if (info.length > 0) {
                                    let nextUserList = [];
                                    info.forEach((item)=>{
                                        nextUserList.push({
                                            taskKey : item.id,
                                            taskName : item.name,
                                            userTaskFormVO : {
                                                dataType: 'USERS',
                                                assignee: '',
                                                candidateUsers: '',
                                                candidateGroups: '',
                                                text: '',
                                            }
                                        })
                                    })
                                    state.selectNextUserList = nextUserList;
                                    // console.log(`nextUserList`,nextUserList)
                                    // console.log(`item.extendedPropertiesJson`,item.extendedPropertiesJson)

                                    state.approveShow = item.extendedPropertiesJson && item.extendedPropertiesJson[0] ? item.extendedPropertiesJson[0].changeuser : false;
                                    state.nextNodesList = info;
                                    if (state.approveShow && nextUserList.length > 0) {
                                        state.showSelectPopup = true;
                                    } else {
                                        // this.dialogVisible = false;

                                        let param = {
                                            id: processMethoddata.id,
                                            reason: processMethoddata.comment,
                                            formVariables: processMethoddata.formVariables,
                                            expressionVariables: processMethoddata.data.expressionVariables,
                                        }
                                        diyPost(AjaxApi.approveTask, param).then(response => {
                                            if (response.data.code === '00000') {
                                                router.push({path: '/mine-dealt',query:{isNavbar: true}})
                                                // state.submitshow = false;
                                            } else {
                                                showToast({type: 'warning', message: response.data.info});
                                                // state.submitshow = false;
                                            }
                                        })
                                    }
                                }else {
                                    let param = {
                                        id: processMethoddata.id,
                                        reason: processMethoddata.comment,
                                        formVariables: processMethoddata.formVariables,
                                        expressionVariables: processMethoddata.data.expressionVariables,
                                    }
                                    diyPost(AjaxApi.approveTask, param).then(response => {
                                        if (response.data.code === '00000') {
                                            router.push({path: '/mine-dealt',query:{isNavbar: true}})
                                            // state.submitshow = false;

                                        } else {
                                            showToast({type: 'warning', message: response.data.info});
                                            // state.submitshow = false;

                                        }
                                    })
                                }
                            } else {
                                showToast({type: 'warning', message: response.data.info});
                                // state.submitshow = false;

                            }
                        })
                    }

                } else {
                    state.handleUpdate = processMethod[o](item, state.reasonStr, state.processInstance, state.formVariables, state.setfieldArr, state.setformObj)
                    if (state.handleUpdate && state.handleUpdate.title === '驳回任务') {
                        const data = {
                            id: state.handleUpdate.id,
                        }
                        diyPost(AjaxApi.returnList, data).then(res => {
                            if (res.data.info.length > 0) {
                                state.handleUpdate.returnTaskList = res.data.info;
                                state.showReturnPopup = state.handleUpdate.open
                                state.columns = state.handleUpdate.returnTaskList;
                                state.submitshow = false;

                            } else {
                                showToast({type: 'warning', message: "没有可驳回节点"});
                                state.submitshow = false;

                            }
                        })
                    } else {
                        state.showSelectPopup = state.handleUpdate ? state.handleUpdate.open : false;
                        state.submitshow = false;

                    }
                }
            }
            const onConfirm = ({selectedOptions}) => {
                state.result = selectedOptions[0]?.name;
                state.returnId = selectedOptions[0]?.id;
                state.showPicker = false;
            };

            const selectedUser = () => {
                let users = [];
                state.rang.forEach(item => {
                    users.push(item.humancode)
                })
                let updateAssigneeform = {
                    assigneeUserId: users.toString(),
                    comment: state.handleUpdate.comment,
                    id: state.handleUpdate.id
                }
                if (state.handleUpdate.title === '审批通过') {
                    if(state.selectNextUserList && state.selectNextUserList.length > 0){
                        state.selectNextUserList.forEach(item=>{
                            if (state.rang.length === 1) {
                                let data = state.rang[0];
                                item.userTaskFormVO.assignee = data.humancode;
                                item.userTaskFormVO.text = data.humanname;
                                item.userTaskFormVO.candidateUsers = null;

                            } else {
                                item.userTaskFormVO.candidateUsers = state.rang.map(k => k.humancode).join() || null;
                                item.userTaskFormVO.text = state.rang.map(k => k.humanname).join() || null;
                                item.userTaskFormVO.assignee = null;
                            }
                        })
                    }

                    let param = {
                        id: state.currentTask.id,
                        reason: state.handleUpdate.comment,
                        formVariables: state.handleUpdate.formVariables,
                        expressionVariables: state.handleUpdate.data.expressionVariables,
                        returnTaskDefKey: "",
                        definitionKey: state.handleUpdate.data.definitionKey,
                        definitionId: state.handleUpdate.data.definitionId,
                        instanceId: state.InstanceId,
                        selectNextUserList: state.selectNextUserList,
                    }
                    diyPost(AjaxApi.approveTask, param).then(response => {
                        if (response.data.code === '00000') {
                            router.push({path: '/mine-dealt',query:{isNavbar: true}})
                        } else {
                            showToast({type: 'warning', message: response.data.info});
                        }
                    })

                }
                if (state.handleUpdate.title === '转办任务') {
                    diyPost(AjaxApi.updateTaskAssignee, updateAssigneeform).then(response => {
                        if (response.data.code === '00000') {
                            router.push({path: '/mine-dealt',query:{isNavbar: true}})
                            state.showSelectPopup = false
                        } else {
                            showToast({type: 'warning', message: response.data.info});
                            state.showSelectPopup = false
                        }
                    })
                }
                if (state.handleUpdate.title === "委派任务") {
                    diyPost(AjaxApi.delegateTask, updateAssigneeform).then(response => {
                        if (response.data.code === '00000') {
                            router.push({path: '/mine-dealt',query:{isNavbar: true}})
                            state.showSelectPopup = false
                        } else {
                            showToast({type: 'warning', message: response.data.info});
                            state.showSelectPopup = false
                        }
                    })
                }
            }

            const onCheckChange = (e) => {
                let userCode = e.id;
                if (codes.value.includes(userCode)) {
                    state.rang.splice(codes.value.indexOf(userCode), 1)
                } else {
                    state.rang.push(e)
                }
            }
            // 审批处理退回
            const handleBack = () => {
                if (!state.returnId) {
                    showToast({type: 'warning', message: "请选择驳回节点！"});
                } else {
                    const data = {
                        id: state.runningTasks[0].id,
                        reason: state.reasonStr,
                        targetKey: state.returnId,
                        approveWay: state.approveWay,
                    }
                    diyPost(AjaxApi.returnTask, data).then(res => {
                        if (res.data.code === '00000') {
                            router.push({path: '/mine-dealt',query:{isNavbar: true}})
                            showToast({type: 'success', message: "驳回任务成功！"});
                        } else {
                            showToast({type: 'warning', message: res.data.info});
                        }
                    });
                }
            }
            //流程催办
            const handleRemind = () => {
                showConfirmDialog({
                    title: '流程催办',
                    message: '确定要进行催办提醒?                                          ',
                }).then(() => {
                    let json = {
                        id: state.currentId
                    }
                    remindRuntimeTask(json).then(res => {
                        if (res.data.code === '00000') {
                            showToast({type: 'success', message: "催办成功！"});
                            router.push({path: '/mine-dealt', query: state.params ? JSON.parse(state.params) : {}})
                        } else {
                            showToast({message: res.data.info ? res.data.info : "催办失败！", wordBreak: 'break-all',});
                        }
                    })
                }).catch(() => {
                    // on cancel
                });
            }
            const handleRemindCancel = () => {
                if (!state.cancelReason) {
                    showFailToast('请输入取消原因');
                    return false;
                }
                let json = {
                    id: state.currentId,
                    reason: state.cancelReason
                }
                cancelProcessInstance(json).then(res => {
                    if (res.data.code === '00000') {
                        showToast({type: 'success', message: "取消成功！"});
                        router.push({path: '/mine-dealt', query: state.params ? JSON.parse(state.params) : {}})
                    } else {
                        showToast({message: "取消失败！", wordBreak: 'break-all',});
                    }
                })
            }
            const handleRemindDelete = () => {
                if (!state.deleteReason) {
                    showFailToast('请输入删除原因');
                    return false;
                }
                let json = {
                    id: state.currentId,
                    reason: state.deleteReason
                }
                deleteProcessInstance(json).then(res => {
                    if (res.data.code === '00000') {
                        showToast({type: 'success', message: "删除成功！"});
                        router.push({path: '/mine-dealt', query: state.params ? JSON.parse(state.params) : {}})
                    } else {
                        showToast({message: res.data.info ? res.data.info : "删除失败！", wordBreak: 'break-all',});
                    }
                })
            }

            const formdsp = (item,dicts,type,lx,index) => {
                console.log(`dicts`,dicts)
                console.log(`item`,item)
                console.log(`type`,type)
                console.log(`lx`,lx)
                console.log(`index`,index)
                if(lx == 'baseList'){
                    if(!state.subapprovedata.baseList[item.id]){
                        state.subapprovedata.baseList[item.id] = [];
                    }
                    if(state.subapprovedata.baseList[item.id].some(item => item.field === dicts)){
                        state.subapprovedata.baseList[item.id].forEach(i=>{
                            if(i.field == dicts){
                                i.spzt = type
                            }
                        })
                    }else {
                        state.subapprovedata.baseList[item.id].push({
                            field: dicts,
                            newValue: state.setfieldArr.tmform[item.id][dicts],
                            oldValue: state.setformObj[dicts],
                            spzt: type
                        })
                    }
                }else {
                    dicts.spzt = type;
                    if(!state.subapprovedata.dtxxList[item.id]){
                        state.subapprovedata.dtxxList[item.id] = state.setfieldArr.dtxxlist[item.id];
                    }
                    state.subapprovedata.dtxxList[item.id][index] = dicts
                }
                console.log(`state.setfieldArr===`,state.setfieldArr)
                console.log(`state.subapprovedata===`,state.subapprovedata)
            }

            const toformsubmit = (data) => {
                let param = {}
                param[data.key] = data.value
                state.changeformdata
                if(!state.changeformdata[data.gid]){
                    state.changeformdata[data.gid] = {}
                }
                state.changeformdata[data.gid] = Object.assign(state.changeformdata[data.gid],param);
                let newdata = {
                    tmform: state.changeformdata,
                    dtxxlist: state.dtxxlist
                }
                state.submitformdata = newdata;
                console.log(`state.submitformdata========`,state.submitformdata)
            }

            const formdelete = (dicts,field) => {
                console.log(`dicts`,dicts)
                console.log(`field`,field)
                state.itemformdata = dicts;
                showConfirmDialog({
                    title: '',
                    message: '确定要删除?                                          ',
                }).then(() => {
                    state.itemformdata.xgfs = 'delete';
                    if(!state.dtxxlist[field.id]){
                        state.dtxxlist[field.id] = []
                    }
                    state.dtxxlist[field.id].push(state.itemformdata);
                    let newdata = {
                        tmform: state.changeformdata,
                        dtxxlist: state.dtxxlist
                    }
                    state.submitformdata = newdata;
                })
            }

            const formdedit = (dicts,item) => {
                console.log(`dicts`,dicts)
                console.log(`item`,item)
                state.itemformdata = dicts;
                state.changegid = item.id;
                let newArr = [];
                item.dictFieldList.forEach(i=>{
                    let dicData = [];
                    if(i.fieldData || i.fieldAttribute){
                        if(!i.fieldData){
                            if(i.fieldAttribute){
                                let arr = i.fieldAttribute.split("#")
                                arr.forEach(i=>{
                                    dicData.push({
                                        label: i,
                                        value: i,
                                    })
                                })
                            }
                        }else {
                            if(i.fieldAttribute){
                                // dicUrl = item.fieldData;
                                let arr = i.fieldAttribute.split("#")
                                diyPost(i.fieldData, {}).then(res => {
                                    let resData = res.data;
                                    if (res.data.info && res.data.info.length>0) {
                                        resData.info.forEach(j=>{
                                            dicData.push({
                                                label: j[arr[0]],
                                                value: j[arr[1]],
                                            })
                                        })
                                        console.log(`dicData`,dicData)
                                    }
                                })
                            }

                        }
                    }
                    newArr.push({
                        type: i.fieldType,
                        values: null,
                        options:{
                            name: i.fielden,
                            label: i.fieldzh,
                            disabled: i.sfbj == '否',
                            optionItems: dicData ? dicData : [],
                        }
                    })
                })
                state.itemformlist = newArr;
                state.xgfs = 'edit'
                state.itemformshow = true;
            }

            const formdadd = (item,accountData) => {
                console.log(`item`,item)
                state.itemformdata = {};
                state.changegid = item.id;
                state.addformlist = accountData
                let newArr = [];
                item.dictFieldList.forEach(i=>{
                    let dicData = [];
                    if(i.fieldData || i.fieldAttribute){
                        if(!i.fieldData){
                            if(i.fieldAttribute){
                                let arr = i.fieldAttribute.split("#")
                                arr.forEach(i=>{
                                    dicData.push({
                                        label: i,
                                        value: i,
                                    })
                                })
                            }
                        }else {
                            if(i.fieldAttribute){
                                // dicUrl = item.fieldData;
                                let arr = i.fieldAttribute.split("#")
                                diyPost(i.fieldData, {}).then(res => {
                                    let resData = res.data;
                                    if (res.data.info && res.data.info.length>0) {
                                        resData.info.forEach(j=>{
                                            dicData.push({
                                                label: j[arr[0]],
                                                value: j[arr[1]],
                                            })
                                        })
                                        console.log(`dicData`,dicData)
                                    }
                                })
                            }

                        }
                    }
                    newArr.push({
                        type: i.fieldType,
                        values: null,
                        options:{
                            name: i.fielden,
                            label: i.fieldzh,
                            disabled: i.sfbj == '否',
                            optionItems: dicData ? dicData : [],
                        }
                    })
                })
                state.itemformlist = newArr;
                state.xgfs = 'add'
                state.itemformshow = true;
            }

            const subonCancel = () => {
                state.itemformshow = false;
            }

            const subonConfirm = () => {
                state.itemformdata.xgfs = state.xgfs;
                if(state.xgfs == 'add') {
                    state.addformlist.push(state.itemformdata)
                }
                if(!state.dtxxlist[state.changegid]){
                    state.dtxxlist[state.changegid] = []
                }
                state.dtxxlist[state.changegid].push(state.itemformdata);
                let newdata = {
                    tmform: state.changeformdata,
                    dtxxlist: state.dtxxlist
                }
                state.submitformdata = newdata;
                state.itemformshow = false;
                console.log(`onConfirm`,state.itemformdata)
            }

            return {
                ...toRefs(state),
                changeTab,
                handleRemind,
                handleRemindCancel,
                handleRemindDelete,
                onConfirmReason,
                handleClick,
                onCheckChange,
                selectedUser,
                onConfirm,
                handleBack,
                handleAudit,
                getsptzdata,
                tobanli,
                formdsp,
                toformsubmit,
                formdelete,
                formdedit,
                formdadd,
                subonCancel,
                subonConfirm,
            }
        },
        computed: {
            ...mapState(["userInfo"]),
        },
        methods: {}
    };
</script>

<style lang="less" scoped>

    .boxSty{
        border: 1px solid rgba(220, 220, 220, 0.52);
        padding: 10px;
        border-radius: 10px;
        position: relative;
        margin-bottom: 10px;
    }

    .product-list-content {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #fff;
    }

    .content {
        height: calc(~ "(88vh - 78px)");
        //height: 88vh;
        overflow: hidden;
        overflow-y: scroll;
        margin: 100px 0;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .div-tips {
        margin: 10px;
        line-height: 20px;
        border: 1px solid #ebeef3;
        padding: 10px;
    }

    ::v-deep .van-tabs .van-tabs__wrap {
        height: 50px;
    }

    ::v-deep.van-tabs .van-tab {
        line-height: 50px;
        font-size: 16px;
    }

    ::v-deep .van-tabs .van-tabs__content {
        background: #fff;
        border-top: 5px solid #f5f5f5;
    }

    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }


    .van-submit-bar .van-button-diy {
        margin: 5px;
        width: 88%;
    }

    .van-toast {
        width: auto !important;
    }

    .van-button--default {
        border: var(--van-button-border-width) solid var(--van-button-default-border-color) !important;
    }

</style>

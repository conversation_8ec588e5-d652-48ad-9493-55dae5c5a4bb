<template>
    <div>
        <div class="product-list-content">
            <s-header :name="'应用'"></s-header>
        </div>
        <van-tabs v-model="active" @click-tab="changeTabs">
            <van-tab :title="item" v-for="item in tabs" :key="item.id"  />
        </van-tabs>
        <div style="margin-top: 15px;">
            <component :isnew="true" :is="istype" :notit="true" :item="{data:{limit: 99}}"></component>
        </div>

        <!--        <div v-if="active == 0 && serviceData && serviceData.length > 0" class="category-list">-->
<!--            <div v-for="(item, index) in serviceData" :key="index"-->
<!--                 @click="showDetail(item)">-->
<!--                <img :src="item.imgUrl.url?(baseUrl+item.imgUrl.url):defaultIcon" :onerror="defaultIcon">-->
<!--                <span>{{item.name.length >5?item.name.substring(0,5)+"..":item.name}}</span>-->
<!--            </div>-->
<!--        </div>-->
    </div>
</template>

<script>
import {mapState} from "vuex";
import { onMounted, reactive, toRefs} from "vue";
import {GetAppletList} from "@/service/applet";
// import {useRouter} from "vue-router";
import yingyongPT from "@/views/DesktopModel/yingyongPT";
import jichengxitong from "@/views/DesktopModel/jichengxitong";
import wodefuwu from "@/views/DesktopModel/wodefuwu";
import wodeliebiao from "@/views/DesktopModel/wodeliebiao";
import sHeader from '@/components/SimpleHeader'


export default {
    name: "yingyong",
    props: [
    ],
    components: {
        yingyongPT,jichengxitong,wodefuwu,wodeliebiao,sHeader
    },
    setup() {
        // const router = useRouter()
        const state = reactive({
            active: null,
            istype: 'yingyongPT',
            tabs: [
                '平台应用',
                '集成系统',
                '我的服务',
                '友情链接'

            ],
            serviceData: null,
        })

        onMounted(async () => {
            getData()
        })

        const changeTabs = (tab) => {
            console.log(`tab`,tab)
            if(tab.title == '集成系统'){
                state.istype = 'jichengxitong'
            }else if (tab.title == '平台应用'){
                state.istype = 'yingyongPT'
            }else if (tab.title == '我的服务'){
                state.istype = 'wodefuwu'
            }else if (tab.title == '友情链接'){
                state.istype = 'wodeliebiao'
            }
        }
        const getData = () => {
            GetAppletList({
                queryParam: {type: "应用平台"},
                page: 1,
                pageSize: 99,
                showType: "mobile"
            }).then(res => {
                if (res.data.code === "00000") {
                    state.serviceData = res.data.info.records;
                }
            });
        }

        return {
            ...toRefs(state),
            changeTabs
        }
    },
    computed: {
        ...mapState(["baseUrl"])
    }
};
</script>

<style lang="less" scoped>
@import '../common/style/mixin';
@import '../common/style/home';
.thebox{
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding-bottom: 10px;
}
</style>

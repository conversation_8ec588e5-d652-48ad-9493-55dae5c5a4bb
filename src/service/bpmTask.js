import axios from '../utils/axios'

// import {baseUrl} from '@/config/env';

// 得到待办任务页
export function getTodoTaskPage(params) {
    return axios.post('/bpmTask/todo-page', params);
}


// 已办
export function getDoneTaskPage(params) {
    return axios.post('/bpmTask/done-page', params);
}

// 完成
export function completeTask(params) {
    return axios.post('/bpmTask/complete', params);
}

// 审批任务
export function approveTask(params) {
    return axios.post('/bpmTask/approve', params);
}

// 拒绝任务
export function rejectTask(params) {
    return axios.post('/bpmTask/reject', params);
}

// 更新任务负责人 用于【流程详情】的【转派】按钮
export function updateTaskAssignee(params) {
    return axios.post('/bpmTask/update-assignee', params);
}

// 获得指定流程实例的任务列表
export function getTaskListByProcessInstanceId(params) {
    return axios.post('/bpmTask/list-by-process-instance-id', params);
}

// 更新任务负责人 用于【流程详情】的【转派】按钮
export function delegateTask(params) {
    return axios.post('/bpmTask/delegateTask', params);
}

export function returnList(params) {
    return axios.post('/bpmTask/getBackNodesByTaskId', params);
}

// 驳回任务
export function returnTask(params) {
    return axios.post('/bpmTask/returnTask', params);
}

// 获取可驳回节点列表
export function getBackNodesByTaskId(params) {
    return axios.post('/bpmTask/getBackNodesByTaskId', params);
}

// 获取下个节点列表
export function getNextNodesByTaskId(params) {
    return axios.post('/bpmTask/getNextNodesByTaskId', params);
}

// 获取第一个用户任务节点
export function getFirstUserTask(params) {
    return axios.post('/bpmTask/getFirstUserTask', params);
}

// 判断当前任务是否在发起人节点，传参任务id：taskId
export function checkStartUserTask(params) {
    return axios.post('/bpmTask/checkStartUserTask', params);
}

// 获取第一个用户任务节点

export function getFirstNextNodesByTaskId(params) {
    return axios.post('/bpmTask/getNextNodesByProcessDefinitionIdAndTaskDefinitionKey', params);
}

// 查询我转办、委派的任务
export function getOwerTaskPage(params) {
    return axios.post('/bpmTask/ower-page', params);
}

export function setNextApprover(params) {
    return axios.post('/bpmTask/setNextApprover', params);
}

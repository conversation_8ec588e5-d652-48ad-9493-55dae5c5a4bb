import axios from '../utils/axios'

export function getUserInfo() {
  return axios.post('/user/info');
}

export function EditUserInfo(params) {
  return axios.put('/user/info', params);
}

export function login(params) {
  return axios.post('/user/login', params);
}

export function logout() {
  return axios.post('/user/logout')
}

export function register(params) {
  return axios.post('/user/register', params);
}


// oauth
export function oauthClient(params) {
  return axios.post('/oauthClient', params);
}

// token
export function GetToken(params){
  return axios.post('/oauthClient/userinfo', params);
}

export function switchRole(params){
  return axios.post('/user/switchRole', params);
}


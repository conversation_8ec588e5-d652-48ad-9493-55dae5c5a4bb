import axios from "../utils/axios";

// // 获得我的实例分页列表
// export const getMyProcessInstancePage = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/myPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// // 获得所有实例分页列表
// export const getProcessInstancePage = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/queryPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// // 获得指定流程实例
// export const getProcessInstance = (id) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/get',
//     method: 'post',
//     data: {
//         id: id
//     }
// });
//
// // 新建流程实例
// export const createProcessInstance = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/create',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
export function getActivityList(params) {
    return axios.get('/bpmActivity/list'+'?processInstanceId='+params);
}
// 取消流程实例， 撤回发起的流程
export function cancelProcessInstance(params) {
    return axios.post('/bpmProcessInstanceExt/cancel', params);
}
// // 删除流程
export function deleteProcessInstance(params) {
    return axios.post('/bpmProcessInstanceExt/delete', params);
}
//催办
export function remindRuntimeTask(params) {
    return axios.post('/bpmProcessInstanceExt/remind', params);
}
//
// //强制撤回
// export const recallRuntimeTask = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/recall',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// // 更新流程实例
// export const updateProcessInstance = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/update',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
// // 判断流程是否已审批
// export const checkApproved = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/checkApproved',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
// // 统计
// export const statistics = (data) => request({
//     url: baseUrl + '/bpmProcessInstanceExt/statistics',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//

import axios from "../utils/axios";

// import request from '@/router/axios';
// import {baseUrl} from '@/config/env';
//
// // 获得流程定义分页
// export const getProcessDefinitionPage = (data) => request({
//     url: baseUrl + '/bpmProcessDefinition/queryPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// // 获得流程定义列表、
export function getProcessDefinitionList(params) {
    return axios.post('/bpmProcessDefinition/list', params);
}

// export const getProcessDefinitionList = (data) => request({
//     url: baseUrl + '/bpmProcessDefinition/list',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// 获得流程定义
export function getProcessDefinition(params) {
    return axios.post('/bpmProcessDefinition/getDefinitionRespVO', params);
}

export function GetBpmVariabledata(params) {
    return axios.post('/bpmFormVariable/format', params);
}
// // 获得流程定义的 BPMN XML
// export const getProcessDefinitionBpmnXML = (id) => request({
//     url: baseUrl + '/bpmProcessDefinition/getBpmnXml',
//     method: 'post',
//     data: {
//         id: id
//     }
// });
//
// // 获得流程定义列表，分页
// export const getProcessDefinitionListPage = (data) => request({
//     url: baseUrl + '/bpmProcessDefinition/getListPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// // 激活或挂起流程定义
// export const updateState = (data) => request({
//     url: baseUrl + '/bpmProcessDefinition/updateState',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// // 删除流程
// export const delDeployment = (data) => request({
//     url: baseUrl + '/bpmProcessDefinition/delete',
//     method: 'post',
//     data: {
//         ...data
//     }
// });

export function queryOne(params, type) {
    return axios.post((type ? type : '/sytTaskPerson') + '/queryOne', params);
}


// 根据模型id查询出激活的流程定义id
export function getActiveDefinitionId(params) {
    return axios.post('/bpmProcessDefinition/getActiveDefinitionId', params);
}

//
// // 获得流程定义列表，下拉选使用
// export const getProcessDefinitionListData = (data) => request({
//     url: baseUrl + '/bpmProcessDefinition/queryListData',
//     method: 'post',
//     data: {
//         ...data
//     }
// });


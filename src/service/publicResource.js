export default {
    // 取消预约
    cancelApply: '/sytResourApply/cancelApply',
    // 获得预约项目列表，下拉选使用
    getResourProjectData: '/sytResourProject/listData',
    // 获得预约项目列表，下拉选使用
    getResourProjectTreeData: '/sytResourProject/listTreeData',
    // 查询枚举状态列表
    queryEnumArray: '/sytResourApply/enumArray',
    // 分页查询指定人预约资源的时间段
    getApplyResour: '/sytResourProject/getApplyResour',
    // 根据项目ID查询数据
    getDataById: '/sytResourProject/getDataById',
    // 根据项目ID查询项目和对应的流程定义ID
    getProjectAndDefinById: '/sytResourProject/getProjectAndDefinById',
    // 查看预约状态
    queryState: '/sytResourApplyTime/queryState',
    Edit: '/sytResourApply/edit',
    /**
     * 预约记录
     * page: 1
     * pageSize: 10
     * queryParam: {applyUserCode: "10001", categoryId: "e2e45457134714696011468db1fac066"}
     */
    yyjlQueryPage: '/sytResourApply/queryPage',


    /**
     * 填报任务列表
     * page: 1
     * pageSize: 10
     * queryParam: {categoryId: "080f18022c55f20aad47214d3ce2860c", isStyle: "0,2"}
     */
    sytTaskProjectQueryPage: '/sytTaskProject/portal/queryPage',
    /**
     * 数据上报分类
     */
    sytCodeTbrwQueryList: '/sytCodeTbrw/queryList',




};

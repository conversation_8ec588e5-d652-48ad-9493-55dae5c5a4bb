import axios from '../utils/axios'

const config = {
    headers: {
        "Content-Type": "application/x-www-form-urlencoded",
    }
};
export function diyGet2(url) {
    return axios.get(url);
}

export function diyGet(url,params) {
    return axios.get(url, params);
}
export function diyPost(url,params) {
    return axios.post(url, params);
}
export function user(params) {
    return axios.post('/user', params, config);
}
export function login1(params) {
    return axios.post('/user/login1', params, config);
}

export function getAllDesktop(params) {
    return axios.post('/sytDesktop/listByUser', params);
}

export function getDesktopById(params) {
    return axios.post('/sytDesktop/data', params);
}
export function GetNews(params) {
    return axios.post('/sytNews/list', params);
}

export function GetappletList(params) {
    return axios.post('/sytApplet/list', params);
}


// 提交 服务反馈
export function Tofeedback(params) {
    return axios.post('/sytServiceCenter/feedback', params);
}

/**
 * 轮播图
 * @param params
 * @returns {Promise<AxiosResponse<any>>}
 */
export function bannerList(params) {
    return axios.post('/sytRollBanner/list', params);
}

/**
 * 服务列表
 * {
 * linkType : ""—类别
 * orgid  : ""—部门
 * name: ""—名称
 * }
 * tab: "recommend"-推荐服务
 * tab: "collect"-收藏服务
 */
export function GetService(params) {
    return axios.post('/sytServiceCenter/list', params);
}

// 访问次数
export function hitCount(params) {
    return axios.post('/sytServiceCenter/hitCount', params);
}

import axios from '../utils/axios'

// 轮播图
export function GetCarousel(params) {
    return axios.post('/sytRollBanner/list', params);
}

// 新闻
export function GetNews(params) {
    return axios.post('/sytNews/list', params);
}

export function GetNewsList(params) {
    return axios.post('/sytNews/getList', params);
}
export function GetDataCategory(params) {
    return axios.post('/sytDataCategory/list', params);
}
// 消息
export function GetMsg(params) {
    return axios.post('/sytSysMsg/list', params);
}

// 集成系统
export function GetSystem(params) {
    return axios.post('/oauthClientDetails/list', params);
}

// 推荐服务

export function GetRecommend(params) {
    return axios.post('/sytServiceCenter/recommendList', params);
}

// 常用服务
export function GetCommon(params) {
    return axios.post('/sytServiceCenter/commonList', params);
}


// 收藏服务
export function GetCollect(params) {
    return axios.post('/sytServiceCenterCollect/list', params);
}

// 进入服务
export function ToService(params) {
    return axios.post('/oauthClient/service', params);
}

//保存桌面设置
export function saveBlocks(params) {
    return axios.post('/sytDesktopBlock/saveBatchByUser', params);
}

//动态接口请求
export function getDataByUrl(url, params) {
    return axios.post(url, params);
}

export function SetReadList(params) {
    return axios.post('/sytReadList/edit', params);
}

export function GetReadList(params) {
    return axios.post('/sytReadList/queryPage', params);
}
//
// export const getDataByUrl = (url, data) => axios.post({
//     url: url.indexOf('http') != -1 ? url :url,
//     // url: url,
//     method: 'post',
//     data: data,
//     params: data,
// });
// export const getDataByUrl = (url, data) => request({
//     // url: url.indexOf('http') != -1 ? url : baseUrl + url,
//     url: url,
//     method: 'post',
//     data: data,
//     params: data,
// });

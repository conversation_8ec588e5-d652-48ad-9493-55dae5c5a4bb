import axios from '../utils/axios'

export function GetApplet(params) {
    return axios.post('/sytApplet/queryPage', params);
}

export function EditApplet(params) {
    return axios.post('/sytApplet/edit', params);
}


export function deleteApplet(params) {
    return axios.post('/sytApplet/delete', params);
}

// 小应用、我的列表、超链接,应用平台
export function GetAppletList(params) {
    return axios.post('/sytApplet/list', params);
}

/**
 * 首页校历显示数据
 * @param params
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getCurrentSchoolCalendar(params) {
    return axios.post('/eduSchoolCalendar/getCurrentSchoolCalendar', params);
}
export function getHolidays(params) {
    return axios.post('/eduSchoolCalendarHolidays/getHolidays', params);
}

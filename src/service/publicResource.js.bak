import axios from '../utils/axios'

// 取消预约
export function cancelApply(params) {
    return axios.post('/sytResourApply/cancelApply', params);
}
// 获得预约项目列表，下拉选使用
export function getResourProjectData(params) {
    return axios.post('/sytResourProject/listData', params);
}
// 获得预约项目列表，下拉选使用
export function getResourProjectTreeData(params) {
    return axios.post('/sytResourProject/listTreeData', params);
}

// 查询枚举状态列表
export function queryEnumArray(params) {
    return axios.post('/sytResourApply/enumArray', params);
}

// 分页查询指定人预约资源的时间段
export function getApplyResour(params) {
    return axios.post('/sytResourProject/getApplyResour', params);
}

// 根据项目ID查询数据
export function getDataById(params) {
    return axios.post('/sytResourProject/getDataById', params);
}

// 根据项目ID查询项目和对应的流程定义ID
export function getProjectAndDefinById(params) {
    return axios.post('/sytResourProject/getProjectAndDefinById', params);
}

// 查看预约状态
export function queryState(params) {
    return axios.post('/sytResourApplyTime/queryState', params);
}

export function Edit(params) {
    return axios.post('/sytResourApply/edit', params);
}

import axios from '../utils/axios'


// // 菜单管理
// export const editMenu = (data) => request({
//     // url: baseUrl + '/user/getMenu',
//     url: baseUrl + '/sytPermissionResources/edit',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const removeMenu = (data) => request({
//     // url: baseUrl + '/user/getMenu',
//     url: baseUrl + '/sytPermissionResources/delete',
//     method: 'post',
//     data: {
//         id: data
//     }
// });
//

// 角色管理
// left
export function GetRoleInfo() {
    return axios.post('/sytPermissionRole/list',);
}

// export const GetEmployeeType = () => request({
//     url: baseUrl + '/sytCodeJslxb/list',
//     method: 'post',
// });
//
// export const GetERoleInfo = () => request({
//     url: baseUrl + '/sytPermissionRole/listManage',
//     method: 'post'
// });
//
// export const EditRole = (data) => request({
//     url: baseUrl + '/sytPermissionRole/edit',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const RemoveRole = (data) => request({
//     url: baseUrl + '/sytPermissionRole/delete',
//     method: 'post',
//     data: {
//         id: data
//     }
// });
//
// // right
// export const GetRoleItemInfo = (data) => request({
//     url: baseUrl + '/sytPermissionAccount/queryPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const RemoveRoleItemInfo = (data) => request({
//     url: baseUrl + '/sytPermissionAccount/delete',
//     method: 'post',
//     data: {
//         id: data
//     }
// });
//
// // 人员管理
// export const GetUserList = (data) => request({
//     url: baseUrl + '/sytPermissionAccount/queryPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const EditUserList = (data) => request({
//     url: baseUrl + '/sytPermissionAccount/edit',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const DeleteUserList = (data) => request({
//     url: baseUrl + '/sytPermissionAccount/delete',
//     method: 'post',
//     data: {
//         id: data
//     }
// });
//
// // 轮播图设置
// export const GetCarousel = (data) => request({
//     url: baseUrl + '/sytRollBanner/queryPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const EditCarousel = (data) => request({
//     url: baseUrl + '/sytRollBanner/edit',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const RemoveCarousel = (data) => request({
//     url: baseUrl + '/sytRollBanner/delete',
//     method: 'post',
//     data: {
//         id: data
//     }
// });
//
// // 服务中心设置
// export const GetSCList = () => request({
//     url: baseUrl + '/sytServiceCenterCategory/list',
//     method: 'post'
// });
//
// export const GetSC = (data) => request({
//     url: baseUrl + '/sytServiceCenter/queryPage',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const EditSC = (data) => request({
//     url: baseUrl + '/sytServiceCenter/edit',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//
// export const RemoveSC = (data) => request({
//     url: baseUrl + '/sytServiceCenter/delete',
//     method: 'post',
//     data: {
//         id: data
//     }
// });
//
// // 服务中心 appid
// export const GetAppId = (data) => request({
//     url: baseUrl + '/oauthClientDetails/list',
//     method: 'post',
//     data
// });
//
// // 授权
// export const ToSQ = (data) => request({
//     url: baseUrl + '/sytPermissionRole/authorize',
//     method: 'post',
//     data: {
//         ...data
//     }
// });
//






<template>
    <div class="nav-bar van-hairline--top">
        <ul class="nav-list">
            <router-link v-for="(item,index) in navbar" :key="index" class="nav-list-item active" :to="{path: item.url,query: { isNavBar: true }}">
                <van-icon :name="item.iconClass" :badge="!item.count ? '' : item.count"/>
                <span>{{item.name}}</span>
            </router-link>
        </ul>
    </div>
</template>

<script>
import {computed, onMounted, reactive, toRefs} from 'vue'
import {mapMutations, mapState, useStore} from 'vuex'
import {diyPost} from "@/service/home";
import AjaxApi from "@/utils/api";
import {getLocal} from '@/common/js/utils'

export default {

        setup() {
            const store = useStore()
            const state = reactive({
                navbar: [],
                userInfo: null,
            })
            let storeStateFns = mapState(["navNum"])
            const storeState = {}
            Object.keys(storeStateFns).forEach(fnKey => {
                const fn = storeStateFns[fnKey].bind({$store: store})
                storeState[fnKey] = computed(fn)
            })
            // 1.手动的映射和绑定
            const mutations = mapMutations(["setNavBar","setNavNum"])
            const newMutations = {}
            Object.keys(mutations).forEach(key => {
                newMutations[key] = mutations[key].bind({$store: store})
            })
            const {setNavBar} = newMutations
            onMounted(() => {
                const userInfo = JSON.parse(getLocal('userInfo'))
                state.userInfo = userInfo
                if(store.state.navBarList.length>0){
                    state.navbar = store.state.navBarList
                }else {
                    getNavBar(storeState['navNum'].value ? storeState['navNum'].value : 3)
                }

            })

            const getNavBar = async (num) => {
                // alert(num)
                setTimeout( async ()=>{
                    await diyPost(AjaxApi.navBar, {showLocale: num}).then(response => {
                            state.navbar = response.data.info;
                            let navBarUrls = []
                            state.navbar.forEach(item => {
                                if (state.userInfo.humanCode !== "syt_visitor") {
                                    if (item.dataSources && item.dataSources.indexOf('/') == 0) {
                                        diyPost(item.dataSources, {}).then(res => {
                                            if (res.data.code === '00000') {
                                                item.count = res.data.info;
                                            }
                                        })
                                    }
                                }
                                navBarUrls.push(item.url)
                            })
                            setNavBar(navBarUrls)
                        }
                    )
                },1000)

            }

            return {
                ...toRefs(state),
            }
        }
    }
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';
    @import '../common/style/home';

    .nav-bar {
        height: 50px;
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        padding: 5px 0;
        z-index: 1000;
        background: #fff;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        padding-bottom: env(safe-area-inset-bottom);

        .nav-list {
            width: 100%;
            .fj();
            flex-direction: row;
            padding: 0;
            margin-top: 5px;

            .nav-list-item {
                display: flex;
                flex: 1;
                flex-direction: column;
                text-align: center;
                color: #666;

                &.router-link-active {
                    color: @primary;
                }
                van-icon{
                    margin: 0 auto;
                    margin-bottom: 2px;
                }
                i {
                    text-align: center;
                    font-size: 22px;
                }

                span {
                    font-size: 14px;
                }
                .van-icon{
                    margin: 0 auto;
                    margin-bottom: 2px;
                }
                .van-icon-user-o{
                    margin: 0 auto;
                    margin-bottom: 2px;
                }

                .van-icon-records {
                    margin: 0 auto;
                    margin-bottom: 2px;
                }

                .van-icon-chat-o {
                    margin: 0 auto;
                    margin-bottom: 2px;
                }

                .van-icon-star-o {
                    margin: 0 auto;
                    margin-bottom: 2px;
                }
            }
        }
    }
</style>

<template>
    <section>
        <SetRangList :mark="setMark"
                     :rang="rang"
                     :result="code"
                     :nextNodesList="nextNodesList"
                     @update:handleRangCancle="handleRangCancle"
                     @update:checkRangBox="chkRbox"></SetRangList>
    </section>
</template>
<script>
    import SetRangList from '@/components/RangList'
    import {onMounted, reactive, toRefs, watch} from "vue";

    export default {
        components: {SetRangList},
        props: {
            setMark: String,
            rang: Array,
            nextNodesList: Array,
            showRang: String,
            code: Array
        },
        setup(props, ctx) {
            const state = reactive({
                active: null,
                mark: null,

                currentMark: props.setMark
            })
            onMounted(async () => {
            })

            watch(() => state.currentMark, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) console.log("watch====", newVal)
            })

            const chkRbox = (e) => {
                ctx.emit("update:checkChange", e);
            };
            const handleRangCancle = () => {
                ctx.emit("update:handleRangCancle");
            };
            return {
                ...toRefs(state),
                chkRbox,
                handleRangCancle,
            }
        },
    }
</script>

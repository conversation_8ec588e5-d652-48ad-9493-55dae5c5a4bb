<template>
    <header class="simple-header van-hairline--bottom">
        <i v-if="!isback" class="nbicon nbfanhui" @click="goBack"></i>
        <i v-else>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</i>
        <div class="simple-header-name">{{ name }}</div>
        <i class="nbicon "></i>
<!--        nbmore-->
    </header>
    <div class="block"/>
</template>

<script>
    import {ref} from 'vue'
    import {useRouter} from 'vue-router'

    export default {
        props: {
            name: {
                type: String,
                default: ''
            },
            back: {
                type: String,
                default: ''
            },
            newParams: {
                type: String,
                default: ''
            },
            noback: {
                type: Boolean,
                default: false
            }
        },
        emits: ['callback'],
        setup(props, ctx) {
            const isback = ref(props.noback)
            const router = useRouter()
            let json = {}
            setTimeout(() => {
                json = props.newParams ? JSON.parse(props.newParams) : {}

            }, 300)

            const goBack = () => {
                json.isNavBar = true;
                if (!props.back) {
                    router.go(-1)
                } else {
                    router.push({path: props.back, query: json})
                }
                ctx.emit('callback')
            }
            return {
                goBack,
                isback
            }

        }
    }
</script>

<style lang="less" scoped>
    @import '../common/style/mixin';

    .simple-header {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 10000;
        .fj();
        .wh(100%, 44px);
        line-height: 44px;
        padding: 0 10px;
        .boxSizing();
        color: #252525;
        background: #fff;

        .simple-header-name {
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;        /* 隐藏超出部分 */
            text-overflow: ellipsis;
        }
    }

    .block {
        height: 44px;
    }
</style>

<template>
    <div>
        <div :style="{ height: '100%' }"
                   v-if="isShowDigitaSign"
                   position="bottom">
            <ZZ_Form ref="formaa" :fields="fields" :setformObj="fieldsVal" :issubform="true" :subname="subname" @subform="subform"></ZZ_Form>
        </div>
    </div>
</template>

<script>
    import {onMounted, reactive, ref, toRefs, watch,} from "vue";
    import ZZ_Form from "@/components/form/ZZ_Form";
    import {forEachFormJsonValues} from "@/utils/forEachFormJson";

    export default {
        name: "ZZ_FieldSubForm",
        props: {
            fields: Array,
            show: Boolean,
            subname: String
        },
        components: {
            ZZ_Form,
        },
        setup(props, ctx) {
            const myCanvas = ref(null);
            const state = reactive({
                isShowDigitaSign: props.show,
                value: null, //base64
                fields: [],
                fieldsVal: {},
                issubform: true,
                subname: ''
            })
            onMounted(() => {
                // console.log(`state.fields11111111111111`,state.fields)
                // console.log(`state.fieldsVal11111111111111`,state.fieldsVal)
                state.fields = props.fields
                state.subname = props.subname
                forEachFormJsonValues(state.fields,state.fieldsVal)
            });

            watch(() => props.show, (newVal) => {
                state.isShowDigitaSign = newVal
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) console.log(`newVal1`,newVal)
                }
            )
            // watch(() => state.fieldsVal, (newVal) => {
            //         // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
            //         if (newVal) console.log(`newVal1`,newVal)
            //     }
            // )
            const onConfirm = () => {
                ctx.emit('update:model-value', {});
                onCancel();
            }
            const onCancel = () => {
                onChangeShow(false);
            };
            const onChangeShow = (value) => {
                ctx.emit('update:show', value);
            };
            const subform = (name) => {
                ctx.emit('subform2',name)
            }

            return {
                ...toRefs(state),
                props, myCanvas,
                onChangeShow,
                onCancel,
                onConfirm,
                subform
            }
        },
        watch: {},
        computed: {},
        methods: {}
    }
</script>

<style lang="less" scoped>
    ::v-deep .van-cell__title, .van-cell__value {
        flex: none !important;
        text-align: left !important;
    }
    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }

</style>

<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :model-value="valueShow"
                   :required="required"
                   @click="selectorShow = true && !disabled"
                   :disabled="disabled"
                   readonly
                   :placeholder="placeholder"/>
        <van-popup v-model:show="selectorShow"
                   position="bottom" teleport="body">
            <van-picker-group :title="label"
                              :tabs="['开始日期', '结束日期']"
                              @confirm="onConfirm"
                              @cancel="selectorShow=false">
                <van-date-picker v-model="startEnd"/>
                <van-date-picker v-model="endDate"/>
            </van-picker-group>
        </van-popup>
    </div>
</template>

<script>
    import {onMounted, reactive, toRefs} from "vue";
    import Utils from "@/utils/momentWrap";
    import {showFailToast} from 'vant';

    export default {
        components: {},
        name: "ZZ_FieldTimePicker",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: Boolean,
            label: String,
            disabled: Boolean,
        },
        setup(props, ctx) {
            const state = reactive({
                selectorShow: false,
                valueShow: null,
                startEnd: [],
                endDate: [],
                columnsType: ['hour', 'minute', 'second']

            })
            onMounted(async () => {
                state.valueShow = props.value ? props.value[0]+" "+props.value[1] : null
                state.startEnd = dateFormat_YMD(new Date()).split('-')
                state.endDate = dateFormat_YMD(new Date()).split('-')
            })
            const dateFormat_YMD = (o) => {
                return Utils.dateFormat_YMD(o);
            }
            const onConfirm = () => {
                if(new Date(state.startEnd) >= new Date(state.endDate)){
                    showFailToast('结束日期应晚于开始日期')
                }else {
                    state.valueShow = state.startEnd.join('-')+" "+ state.endDate.join('-');
                    // ctx.emit("update:model-value", state.valueShow);
                    console.log([state.startEnd.join('-'),state.endDate.join('-')])
                    ctx.emit("update:model-value", [state.startEnd.join('-'),state.endDate.join('-')]);
                    state.selectorShow = false;
                }


            }
            return {
                ...toRefs(state),
                onConfirm,
            }
        },
    }
</script>

<style scoped>

</style>

<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :model-value="valueShow"
                   :required="required"
                   @click="selectorShow = true"
                   :disabled="disabled"
                   :readonly="readonly"
                   :placeholder="placeholder"/>
        <van-popup v-model:show="selectorShow" position="bottom" teleport="body">
            <van-cascader v-model="valueShow" :title="'请选择'+label"
                          :options="setOptions"
                          :field-names="fieldNames"
                          @close="selectorShow = false"
                          @finish="onFinish"/>
        </van-popup>
    </div>
</template>

<script>
    import {onMounted, reactive, toRefs} from "vue";

    export default {
        components: {},
        name: "ZZ_FieldCascaderSelector",
        props: {
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            options: Array,
            field: Object,
            disabled: Boolean,
            readonly: Boolean
        },
        setup(props, ctx) {
            const fieldNames = {
                text: 'label',
                value: 'value',
                children: 'children',
            };
            const state = reactive({
                selectorShow: false,
                valueShow: null,
                cascaderValue: null,
                setOptions: props.options,
            })
            onMounted(async () => {
                state.valueShow = props.value ? props.value : null
            })
            const onFinish = ({selectedOptions}) => {
                let currentData = selectedOptions.map((option) => option.label).join('/');
                state.valueShow = currentData
                ctx.emit("update:model-value", currentData);
                state.selectorShow = false;
            };
            return {
                ...toRefs(state),
                fieldNames,
                onFinish,
            }
        },
    }
</script>

<style scoped>

</style>

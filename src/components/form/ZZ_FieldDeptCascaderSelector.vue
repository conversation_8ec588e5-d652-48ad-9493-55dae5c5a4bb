<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :model-value="valueShow"
                   :required="required"
                   @click="selectorShow = true && !disabled"
                   :disabled="disabled"
                   readonly
                   :placeholder="placeholder"/>
        <van-popup v-model:show="selectorShow" position="bottom" teleport="body">
            <van-cascader v-model="valueShow" :title="'请选择'+label"
                          :options="setOptions"
                          :field-names="fieldNames"
                          @close="selectorShow = false"
                          @finish="onFinish"/>
        </van-popup>
    </div>
</template>

<script>
    import {onMounted, reactive, toRefs} from "vue";
    import AjaxApi from "@/utils/api";
    import {diyPost} from '@/service/home'

    export default {
        components: {},
        name: "ZZ_FieldCascaderSelector",
        props: {
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            options: Array,
            field: Object,
            disabled: Boolean,
        },
        setup(props, ctx) {
            const fieldNames = {
                text: 'label',
                value: 'id',
                children: 'children',
            };
            const state = reactive({
                selectorShow: false,
                valueShow: props.value,
                cascaderValue: null,
                setOptions: [],

            })
            onMounted(async () => {
                getSytSysOrganization()
            })
            const getSytSysOrganization = () => {
                diyPost(AjaxApi.sytSysOrganization, {}).then(res => {
                    if (res.status === 200 && res.data.code === '00000') {
                        state.setOptions = formatData(res.data.info)

                    }
                })
            }
            /**
             * 过滤掉级联选择器空的children
             */
            const formatData = (data) => {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].children.length < 1) {
                        // children若为空数组，则将children设为undefined
                        data[i].children = undefined;
                    } else {
                        // children若不为空数组，则继续 递归调用 本方法
                        formatData(data[i].children);
                    }
                }
                return data;
            }
            // 计算属性(简写
            const onFinish = ({selectedOptions}) => {
                let currentData = selectedOptions.map((option) => option.label).join(',');
                state.valueShow = currentData
                ctx.emit("update:model-value", currentData);
                state.selectorShow = false;
            };
            return {
                ...toRefs(state),
                fieldNames,
                onFinish,
            }
        },
    }
</script>

<style scoped>

</style>

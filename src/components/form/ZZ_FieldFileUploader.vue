<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :required="required"
                   :placeholder="placeholder?placeholder:''"
                   readonly>
            <template #button>
                <van-button v-if="field.type ==='sign'" :disabled="disabled" size="small" type="primary"
                            @click="onClickSignature">点我签字
                </van-button>
                <van-button v-else size="small" :disabled="disabled" type="primary" @click="isShow = true">
                    上传{{field.type == 'picture-upload'?'图片':'文件'}}
                </van-button>
            </template>
        </van-field>
        <div v-if="oldFiles">
            <div class="file-item" :key="fIndex" v-for="(file,fIndex) in oldFiles">
                <van-icon v-if="!readOnlyMark && !disabled" name="delete" size="1.5em" @click="onDeleteFile(fIndex)"/>
                <div v-if="file.type == 'picture-upload' || file.type == 'sign'|| file.type == 'img-upload'" class="file-item-other">
                    <ZZ_Image v-if="file.data" :src="file.data" :type="file.type" width="100%"></ZZ_Image>
                </div>
                <div v-else class="file-item-other">
                    <a> {{file.name}}</a>
<!--                    {{baseUrl+file.data}}-->
                </div>
            </div>
        </div>
        <ZZ_FileUploader @update:model-value="onInput($event)" v-model:show="isShow"
                         :file-type="field.el"></ZZ_FileUploader>

        <ZZ_FileUploaderDigitaSign @update:model-value="onInput($event)" v-model:show="isShowDigitaSign"
                                   :wxts="field.wxts"
                                   :file-type="field.el"></ZZ_FileUploaderDigitaSign>
    </div>
</template>

<script>
    import ZZ_FileUploader from "@/components/form/ZZ_FileUploader";
    import ZZ_FileUploaderDigitaSign from "@/components/form/ZZ_FileUploaderDigitaSign";
    import ZZ_Image from "@/components/ZZ_Image";
    import {isImage} from "@/utils/common_file";
    import {computed, onMounted, reactive, toRefs,} from "vue";
    import {mapState} from "vuex";

    export default {
        components: {
            ZZ_FileUploader, ZZ_Image,
            ZZ_FileUploaderDigitaSign
        },
        name: "ZZ_FieldFileUploader",
        props: {
            field: Object,
            value: String,
            wxts: String,
            errorMessage: String,
            placeholder: String,
            label: String,
            required: Boolean,
            oldFilesObj: Object,
            readOnlyMark: Boolean,
            disabled: Boolean,
        },
        computed: {
            ...mapState(["baseUrl"])
        },
        setup(props, ctx) {
            const state = reactive({
                isShow: false,
                isShowDigitaSign: false,
            })

            onMounted(async () => {
                console.log(props.field)
            })

            const oldFiles = computed(() => {
                console.log(`oldFiles`, props.oldFilesObj.files)
                // 注意必须return(否则newMoney没有值)
                return props.oldFilesObj.files
            })
            const onClickSignature = () => {
                //签名只保留一张
                if (props.field.type === 'sign') {
                    // ctx.emit("update:deletefile", props.field.id, 0);
                    state.isShowDigitaSign = true;
                }
            }
            const getIsImage = (file) => {
                return isImage(file);
            }
            const onDelete = () => {
                ctx.emit("update:file");
            }
            const onDeleteFile = (index) => {
                ctx.emit("update:deletefile", props.field.id, index);
            }
            const onInput = (value) => {
                if (value && value.length > 0) {
                    let arr = [];
                    value.forEach((x) => {
                        arr.push({
                            name: x.file.name,
                            data: x.content,
                            // type: x.file.type
                            type: 'img-upload'
                        })
                    });
                    console.log(`arr`,arr)
                    ctx.emit("update:addfiles", props.field.id, arr);
                }
            }
            return {
                ...toRefs(state),
                oldFiles,
                onClickSignature,
                getIsImage,
                onDeleteFile,
                onInput,
                onDelete,
            }
        },
    }
</script>

<style scoped>
    .file-item {
        margin-top: 3px;
        position: relative;
        padding: 0 5px;
        display: inline-block;
        width: 30%;
        height: 140px;
        overflow: hidden;
    }

    .file-item-other {
        display: block;
    }

    .file-item .van-icon {
        vertical-align: sub;
        position: absolute;
        right: 5px;
        top: 0;
    }

    .van-doc-demo-block[data-v-9986b786] .file-item .van-icon-delete {
        position: absolute;
        left: 105px;
        bottom: 5px;
        top: 0;
    }

    .van-hairline--top-bottom {
        position: static;
    }

</style>

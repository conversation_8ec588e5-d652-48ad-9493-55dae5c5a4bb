<template>
    <div>
        <van-field v-if="!isShowSelect" :error-message="errorMessage"
                   :label="label"
                   :model-value="value"
                   :required="required"
                   readonly
                   :placeholder="placeholder">
            <template #input>
                <!-- direction="horizontal" 一行排列  单选 -->
                <van-radio-group v-model="nowVal" :disabled="disabled" @update:model-value="onInput" direction="horizontal">
                    <van-radio @click="handle(index)"
                               :key="index"
                               v-for="(item, index) in setOptions"
                               :name="String(item.value)">{{item.label}}
                    </van-radio>
                </van-radio-group>
            </template>
        </van-field>

        <van-field v-else :error-message="errorMessage"
                   :label="label"
                   :model-value="nowVal"
                   :required="required"
                   @click="selectorShow = true && !disabled"
                   readonly
                   :disabled="disabled"
                   :placeholder="placeholder">
        </van-field>

        <van-popup v-model:show="selectorShow"
                   position="bottom" teleport="body">
            <van-picker :columns="setColumns" :loading="!setColumns"
                        @cancel="onCancel()" @confirm="onConfirm"/>
        </van-popup>
    </div>
</template>

<script>
    import {computed, onMounted, reactive, toRefs} from "vue";

    export default {
        name: "ZZ_FieldSelector",
        props: {
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            options: Array,
            field: Object,
            disabled: Boolean,
        },
        setup(props, ctx) {
            const state = reactive({
                selectorShow: false,
                radioShow: false,
                nowVal: null,
                oldVal: null,
                mark: 1,
                same: false,
                setOptions: props.options,

            })
            onMounted(async () => {
                if (props.field.type === 'select' && props.value) {
                    let newLabel = state.setOptions ? state.setOptions.find(e => {
                        return e.value === props.value;
                    }) : ''

                    state.nowVal = newLabel ? newLabel['label'] : ''
                } else {
                    state.nowVal = props.value ? String(props.value) : null
                }
            })
            const isShowSelect = computed(() => {
                if (props.field.type === 'select') {
                    return true;
                } else if (state.setOptions && state.setOptions.length > 5) {
                    return true;
                } else {
                    return false;
                }
            })
            const setColumns = computed(() => {
                let newCol = []
                if (props.options) {
                    props.options.forEach(e => {
                        newCol.push({text: e.label, value: e.value})
                    })
                }
                return newCol;
            })

            const onInput = (value) => {
                if (!props.field.readonly) {
                    state.nowVal = value;
                    if (state.same) {
                        state.nowVal = "";
                    }
                    ctx.emit("update:model-value", state.nowVal);
                }
            }

            const handle = (index) => {
                if (!props.field.readonly) {
                    if (state.oldVal == state.nowVal) {
                        state.nowVal = "";
                        state.oldVal = "";
                        state.same = true;
                        onInput(state.nowVal);
                        state.mark = 1;
                    } else {
                        state.same = false;
                        state.nowVal = String(state.setOptions[index].value);
                        onInput(state.nowVal);
                        state.oldVal = state.nowVal;
                    }
                    if (state.mark) {
                        state.mark = 0;
                        if (state.oldVal == "") {
                            let setVal = String(state.setOptions[index].value);
                            state.oldVal = setVal
                            state.nowVal = setVal
                            onInput(state.nowVal);
                        }
                    }
                }
            }
            const onCancel = () => {
                state.selectorShow = false;
            }
            const onConfirm = (value) => {
                console.log(value.selectedOptions[0].value)
                state.nowVal = value.selectedOptions[0].text;
                state.selectorShow = false;
                ctx.emit("update:model-value", value.selectedOptions[0].value);
            }

            return {
                ...toRefs(state),
                isShowSelect,
                setColumns,
                onInput,
                handle,
                onCancel,
                onConfirm,
            }
        },
    };
</script>

<style scoped>
</style>

<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :model-value="valueShow"
                   :required="required"
                   @click="tochange"
                   :disabled="disabled"
                   readonly
                   :placeholder="placeholder"/>
        <van-popup v-model:show="selectorShow"
                   position="bottom" teleport="body">
            <van-date-picker v-if="dateshow && field.options.type == 'datetime'" v-model="currentDate"
                             :title="label+'(年月日)'"
                             @confirm="onConfirmtime"
                             @cancel="dateshow=false"/>
            <van-time-picker v-if="!dateshow && field.options.type == 'datetime'" v-model="currentTime"
                             :title="label+'(时间)'"
                             :columns-type="columnsType"
                             @confirm="onConfirmtime2"
                             @cancel="selectorShow=false"/>


            <van-date-picker v-if="field.options.type !== 'datetime'" v-model="currentDate"
                             :title="label"
                             @confirm="onConfirm"
                             @cancel="selectorShow=false"/>
        </van-popup>
<!--        <van-popup v-model:show="selectorShow"-->
<!--                   position="bottom" teleport="body">-->
<!--            <van-date-picker v-model="currentDate"-->
<!--                             :title="label"-->
<!--                             @confirm="onConfirm"-->
<!--                             @cancel="selectorShow=false"/>-->
<!--        </van-popup>-->
    </div>
</template>

<script>
    import {onMounted, reactive, toRefs} from "vue";
    import Utils from "@/utils/momentWrap";


    export default {
        components: {},
        name: "ZZ_FieldDatePicker",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: Boolean,
            label: String,
            disabled: Boolean,
            readonly: Boolean,
        },
        setup(props, ctx) {
            const state = reactive({
                selectorShow: false,
                dateshow: false,
                valueShow: null,
                currentDate: [],
                currentTime: [],
                columnsType: ['hour', 'minute', 'second']

            })
            onMounted(async () => {
                if(props.field.options.type == 'datetime'){
                    state.valueShow = props.value ? dateFormat_date(props.value) : null;
                    state.currentDate = dateFormat_date(new Date()).split('-')
                }else {
                    state.valueShow = props.value ? dateFormat_YMD(props.value) : null;
                    state.currentDate = dateFormat_YMD(new Date()).split('-')
                }
            })
            const dateFormat_YMD = (o) => {
                return Utils.dateFormat_YMD(o);
            }
            const dateFormat_date = (o) => {
                return Utils.dateFormat_date(o);
            }
            const onConfirm = () => {
                state.valueShow = state.currentDate.join('-')
                ctx.emit("update:model-value", state.currentDate.join('-'));
                state.selectorShow = false;
            }

            const onConfirmtime = () => {
                // state.valueShow = state.currentDate.join('/')
                // ctx.emit("update:model-value", state.currentDate.join('/'));
                state.dateshow = false;
            }

            const onConfirmtime2 = () => {
                state.valueShow = state.currentDate.join('-') + " " + state.currentTime.join(':')
                ctx.emit("update:model-value", state.currentDate.join('-') + " " + state.currentTime.join(':'));
                state.selectorShow = false;
            }

            const tochange = () => {
                if( !props.disabled && !props.readonly ){
                    state.selectorShow = true;
                    state.dateshow = true;
                }

            }

            return {
                ...toRefs(state),
                onConfirm,
                onConfirmtime,
                onConfirmtime2,
                tochange,
                dateFormat_YMD,
                dateFormat_date
            }
        },
    }
</script>

<style scoped>

</style>

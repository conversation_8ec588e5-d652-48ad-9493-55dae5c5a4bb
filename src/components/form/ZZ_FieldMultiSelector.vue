<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :value="nowVal"
                   :required="required"
                   @click="selectorShow = true"
                   :type="isShowSelect?'textarea':'text'"
                   :placeholder="placeholder"
                   :is-link="isShowSelect?true:false">
            <template v-if="!isShowSelect" #input>
                <van-checkbox-group v-model="nowVal" :disabled="disabled" direction="horizontal">
                    <van-checkbox :key="option + index" v-for="(option,index) in options" :name="String(option.value)"
                                  shape="square">{{option.label}}
                    </van-checkbox>
                </van-checkbox-group>
            </template>
        </van-field>
        <ZZ_MultiSelector
                v-if="isShowSelect"
                :model-value="valueShow"
                @update:model-value="onInput($event)"
                :columns="options" v-model:show="selectorShow"></ZZ_MultiSelector>
    </div>
</template>

<script>
    import ZZ_MultiSelector from "@/components/form/ZZ_MultiSelector";
    import {reactive, computed, toRefs, watch, onMounted} from "vue";

    export default {
        components: {
            ZZ_MultiSelector
        },
        name: "ZZ_FieldMultiSelector",
        props: {
            directionMark: String,
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            options: {
                type: Array,
                default() {
                    return []
                }
            },
            disabled: Boolean,
            readonly: Boolean
        },
        setup(props, ctx) {
            const state = reactive({
                selectorShow: false,
                nowVal: [],
            })

            onMounted(async () => {
                state.nowVal = props.value && props.value.length > 0 ? props.value : []
            })
            const isShowSelect = computed(() => {
                // 注意必须return(否则newMoney没有值)
                console.log(`props.options=====`,props.options)
                return props.options && props.options.length > 10;
            })
            watch(() => state.nowVal, (newVal) => {
                console.log(newVal)
                if (newVal) ctx.emit("update:model-value", state.nowVal);
            })
            return {
                ...toRefs(state),
                isShowSelect,
            }
        },
    }
</script>

<style>
    .van-checkbox__label {
        margin-left: 10px;
        color: #323233;
        line-height: 25px !important;
        width: 100% !important;
    }
</style>

<template>
    <div>
        <van-field :error-message="errorMessage"
                   :label="label"
                   :model-value="valueShow"
                   :required="required"
                   @click="selectorShow = true && !disabled"
                   :disabled="disabled"
                   readonly
                   :placeholder="placeholder"/>
        <van-popup v-model:show="selectorShow"
                   position="bottom" teleport="body">
            <van-time-picker v-model="currentTime"
                             :title="label"
                             :columns-type="columnsType"
                             @confirm="onConfirm"
                             @cancel="selectorShow=false"/>
        </van-popup>
    </div>
</template>

<script>
    import {onMounted, reactive, toRefs} from "vue";

    export default {
        components: {},
        name: "ZZ_FieldTimePickerGroup",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: Boolean,
            label: String,
            disabled: Boolean,
        },
        setup(props, ctx) {
            const state = reactive({
                selectorShow: false,
                valueShow: null,
                currentTime: [],
                columnsType: ['hour', 'minute', 'second']

            })
            onMounted(async () => {
                state.valueShow = props.value ? props.value : null
            })
            const onConfirm = () => {
                state.valueShow = state.currentTime.join(':')
                ctx.emit("update:model-value", state.currentTime.join(':'));
                state.selectorShow = false;
            }
            return {
                ...toRefs(state),
                onConfirm,
            }
        },
    }
</script>

<style scoped>

</style>

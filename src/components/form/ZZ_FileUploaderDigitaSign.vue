<template>
    <div class="wrap">
        <van-popup :style="{ height: '83%' }"
                   v-model:show="isShowDigitaSign"
                   position="bottom">
            <canvas class="app-sign-canvas" ref="myCanvas">
                您的浏览器不支持canvas技术,请升级浏览器!
            </canvas>
            <div style="text-align: center;">请在红框内签名!</div>
            <div class="van-submit-bar">
                <van-button round size="small" type="default" @click="onCancel">取消</van-button>
                <van-button round size="small" type="primary" plain @click="canvasClear">清空
                </van-button>
                <van-button round size="small" type="success" plain @click="saveAsImgOK">确认
                </van-button>
            </div>
        </van-popup>
    </div>
</template>


<script>
    import {onMounted, nextTick, reactive, toRefs, ref, watch} from "vue";

    export default {
        name: 'SignCanvas',
        model: {
            value: 'image',
            event: 'confirm'
        },
        props: {
            wxts: String,
            show: Boolean,
            image: {
                required: false,
                type: [String],
                default: null
            },
            options: {  //配置项
                required: false,
                type: [Object],
                default: () => null
            },
        },
        setup(props, ctx) {
            const myCanvas = ref(null);
            const state = reactive({
                isShowDigitaSign: props.show,
                value: null, //base64
                // domId: `sign-canvas-${Math.random().toString(36).substr(2)}`,  //生成唯一dom标识
                canvas: null,    //canvas dom对象
                context: null,   //canvas 画布对象
                config: {
                    lastWriteSpeed: 1,  //书写速度 [Number] 可选
                    lastWriteWidth: 2,  //下笔的宽度 [Number] 可选
                    lineCap: 'round',   //线条的边缘类型 [butt]平直的边缘 [round]圆形线帽 [square]	正方形线帽
                    lineJoin: 'round',  //线条交汇时边角的类型  [bevel]创建斜角 [round]创建圆角 [miter]创建尖角。
                    canvasWidth: 350, //canvas宽高 [Number] 可选
                    canvasHeight: 550,  //高度  [Number] 可选
                    isShowBorder: false, //是否显示边框 [可选]
                    bgColor: '#060505', //背景色 [String] 可选
                    borderWidth: 1, // 网格线宽度  [Number] 可选
                    borderColor: "#00a5ec", //网格颜色  [String] 可选
                    writeWidth: 5, //基础轨迹宽度  [Number] 可选
                    maxWriteWidth: 30, // 写字模式最大线宽  [Number] 可选
                    minWriteWidth: 5, // 写字模式最小线宽  [Number] 可选
                    writeColor: '#101010', // 轨迹颜色  [String] 可选
                    isSign: true, //签名模式 [Boolean] 默认为非签名模式,有线框, 当设置为true的时候没有任何线框
                    imgType: 'png'   //下载的图片格式  [String] 可选为 jpeg  canvas本是透明背景的
                },
            })
            onMounted(() => {
            });

            watch(() => props.show, (newVal) => {
                state.isShowDigitaSign = newVal
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) init();
            })

            // watch(() => props.context, (newVal) => {
            //     // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
            //     if (newVal) init();
            //     //温馨提示，做图片水印使用 wt20210913
            //     if (state.wxts) paintFixedWaterMark(state.wxts)
            // })
            const paintFixedWaterMark = (workWxts) => {
                //在Vue中可改成ES6写法
                var twLength = state.context.measureText(workWxts).width;
                var wrap = document.createElement("div");//建立一个div
                wrap.className = "fixed-water-mark";//给div添加类名
                var wm = document.createElement("canvas");//单个水印画布
                wm.id = "watermark";//给canvas标签添加id
                wm.width = twLength + 100;//设置canvas宽
                wm.height = 150;//设置canvas高
                wm.style.display = "none";//设置画布隐藏属性
                wrap.appendChild(wm);//在div中添加画布
                var rwm = document.createElement("canvas");//重复绘制水印画布，用于整个页面
                rwm.id = "repeat-watermark";
                wrap.appendChild(rwm);
                document.body.appendChild(wrap);
                //绘制单个水印
                var cw = document.getElementById('watermark');
                var ctx = cw.getContext("2d");
                // var twLength = ctx.measureText(workWxts).width;
                ctx.clearRect(0, 0, wm.width, wm.height);//清空矩形
                ctx.font = "14px 黑体";//设置字体
                ctx.textBaseline = 'middle';//更改字号后，必须重置对齐方式，否则居中麻烦。设置文本的垂直对齐方式
                ctx.textAlign = 'center';
                ctx.rotate(-12 * Math.PI / 180);//逆时针旋转20度
                // ctx.fillStyle = "rgba(100,100,100,0.3)";//填充透明度为0.3的灰色
                ctx.fillStyle = "rgba(156,153,153,0.45)";//填充透明度为0.3的灰色
                // ctx.fillText(workWxts, 80, 85);//填充内容为 温馨提示信息
                let xNum = wm.width / 2 - 20;
                let yNum = wm.height - 5;
                ctx.fillText(workWxts, xNum, yNum);//填充内容为 温馨提示信息
                //在另外一个画布上重复绘制单个水印
                state.context.clearRect(0, 0, state.config.canvasWidth, state.config.canvasHeight);
                state.context.fillStyle = "#fff";//填充透明度为0.3的灰色
                state.context.fillRect(0, 0, state.config.canvasWidth, state.config.canvasHeight);
                var pat = state.context.createPattern(cw, "repeat");//在水平和垂直方向重复绘制单个水印
                state.context.fillStyle = pat;
                state.context.fillRect(0, 0, state.config.canvasWidth, state.config.canvasHeight);
                setTimeout(function () {
                    document.body.removeChild(wrap);
                }, 500);
            };
            const onCancel = () => {
                onChangeShow(false);
            };
            const onChangeShow = (value) => {
                ctx.emit('update:show', value);
            };
            /**
             * 轨迹宽度
             */
            const setLineWidth = () => {
                const nowTime = new Date().getTime();
                const diffTime = nowTime - state.config.lastWriteTime;
                state.config.lastWriteTime = nowTime;
                let returnNum = state.config.minWriteWidth + (state.config.maxWriteWidth - state.config.minWriteWidth) * diffTime / 30;
                if (returnNum < state.config.minWriteWidth) {
                    returnNum = state.config.minWriteWidth;
                } else if (returnNum > state.config.maxWriteWidth) {
                    returnNum = state.config.maxWriteWidth;
                }
                returnNum = returnNum.toFixed(2);
                //写字模式和签名模式
                if (state.config.isSign) {
                    state.context.lineWidth = state.config.writeWidth;
                } else {
                    state.context.lineWidth = state.config.lastWriteWidth = state.config.lastWriteWidth / 4 * 3 + returnNum / 4;
                }
            };
            /**
             * 绘制轨迹
             */
            const writing = (point) => {
                state.context.beginPath();
                state.context.moveTo(state.config.lastPoint.x, state.config.lastPoint.y);
                state.context.lineTo(point.x, point.y);
                setLineWidth();
                state.context.stroke();
                state.config.lastPoint = point;
                state.context.closePath();
            };
            /**
             * 轨迹样式
             */
            const writeContextStyle = () => {
                state.context.beginPath();
                state.context.strokeStyle = state.config.writeColor;
                state.context.lineCap = state.config.lineCap;
                state.context.lineJoin = state.config.lineJoin;
            };
            /**
             * 写开始
             */
            const writeBegin = (point) => {
                state.config.isWrite = true;
                state.config.lastWriteTime = new Date().getTime();
                state.config.lastPoint = point;
                writeContextStyle();
            };
            /**
             * 写结束
             */
            const writeEnd = (point) => {
                state.config.isWrite = false;
                state.config.lastPoint = point;
                saveAsImg();
            };
            /**
             * 清空画板
             */
            const canvasClear = () => {
                state.context.save();
                state.context.strokeStyle = state.config.writeColor;
                state.context.clearRect(0, 0, state.config.canvasWidth, state.config.canvasHeight);
                state.context.beginPath();
                let size = state.config.borderWidth / 2;
                if (state.config.isShowBorder) {
                    //画外面的框
                    state.context.moveTo(size, size);
                    state.context.lineTo(state.config.canvasWidth - size, size);
                    state.context.lineTo(state.config.canvasWidth - size, state.config.canvasHeight - size);
                    state.context.lineTo(size, state.config.canvasHeight - size);
                    state.context.closePath();
                    state.context.lineWidth = state.config.borderWidth;
                    state.context.strokeStyle = state.config.borderColor;
                    state.context.stroke();
                }
                if (state.config.isShowBorder && !state.config.isSign) {
                    //画里面的框
                    state.context.moveTo(0, 0);
                    state.context.lineTo(state.config.canvasWidth, state.config.canvasHeight);
                    state.context.lineTo(state.config.canvasWidth, state.config.canvasHeight / 2);
                    state.context.lineTo(state.config.canvasWidth, state.config.canvasHeight / 2);
                    state.context.lineTo(0, state.config.canvasHeight / 2);
                    state.context.lineTo(0, state.config.canvasHeight);
                    state.context.lineTo(state.config.canvasWidth, 0);
                    state.context.lineTo(state.config.canvasWidth / 2, 0);
                    state.context.lineTo(state.config.canvasWidth / 2, state.config.canvasHeight);
                    state.context.stroke();
                }
                ctx.emit('confirm', null);
                state.context.restore();
                //温馨提示，做图片水印使用 wt20210913
                if (state.wxts) {
                    paintFixedWaterMark(state.wxts)
                } else {
                    state.context.clearRect(0, 0, state.config.canvasWidth, state.config.canvasHeight);
                    state.context.fillStyle = "#fff";//填充透明度为0.3的灰色
                    state.context.fillRect(0, 0, state.config.canvasWidth, state.config.canvasHeight);
                }
            };
            /**
             *  保存图片 格式base64
             */
            const saveAsImg = () => {
                const image = new Image();
                image.src = state.canvas.toDataURL("image/png");
                ctx.emit('confirm', image.src);
                return image.src;
            }
            /**
             * 保存图片
             */
            const saveAsImgOK = () => {
                const img = saveAsImg();
                let file = dataURLtoFile(img, "signature" + Math.random().toString(36).substr(2) + '.png');
                let filesObj = {
                    content: img,
                    file: file,
                    status: "",
                };
                let files = [];
                files.push(filesObj);
                ctx.emit('update:model-value', files);
                onCancel();
            }
            /**
             * 将base64转换为文件对象
             */
            const dataURLtoFile = (dataurl, filename) => {
                var arr = dataurl.split(',');
                var mime = arr[0].match(/:(.*?);/)[1];
                var bstr = atob(arr[1]);
                var n = bstr.length;
                var u8arr = new Uint8Array(n);
                while (n--) {
                    u8arr[n] = bstr.charCodeAt(n);
                }
                //转换成file对象
                return new File([u8arr], filename, {type: mime});
                //转换成成blob对象
                //return new Blob([u8arr],{type:mime});
            }

            /**
             * 初始化画板
             */
            const canvasInit = () => {
                state.canvas.width = state.config.canvasWidth;
                state.canvas.height = state.config.canvasHeight;
                state.config.emptyCanvas = state.canvas.toDataURL("image/png");
                bindEvent();
            }
            /**
             * 绑定事件
             */
            const bindEvent = () => {
                //鼠标按下 => 下笔
                state.canvas.addEventListener('mousedown', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    writeBegin({x: e.offsetX || e.clientX, y: e.offsetY || e.clientY});
                });
                //书写过程 => 下笔书写
                state.canvas.addEventListener('mousemove', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    state.config.isWrite && writing({x: e.offsetX, y: e.offsetY});
                });
                //鼠标松开 => 提笔
                state.canvas.addEventListener('mouseup', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    writeEnd({x: e.offsetX, y: e.offsetY});
                });
                //离开书写区域 => 提笔离开
                state.canvas.addEventListener('mouseleave', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    writeEnd({x: e.offsetX, y: e.offsetY});
                });
                /* ==========================移动端兼容=Start================================ */
                //手指按下 => 下笔
                state.canvas.addEventListener('touchstart', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    const touch = e.targetTouches[0];
                    // const getBCR = touch.target.getBoundingClientRect();
                    const offsetLeft = offset(touch.target, 'left');
                    const offsetTop = offset(touch.target, 'top');
                    let x = touch.clientX ? touch.clientX - offsetLeft : touch.clientX;
                    let y = touch.clientY ? touch.clientY - offsetTop : touch.clientY;
                    writeBegin({x, y});
                });
                //手指移动 => 下笔书写
                state.canvas.addEventListener('touchmove', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    const touch = e.targetTouches[0];
                    const offsetLeft = offset(touch.target, 'left');
                    const offsetTop = offset(touch.target, 'top');
                    let x = touch.clientX ? touch.clientX - offsetLeft : touch.clientX;
                    let y = touch.clientY ? touch.clientY - offsetTop : touch.clientY;
                    // console.log(touch)
                    state.config.isWrite && writing({x, y});
                });
                //手指移动结束 => 提笔离开
                state.canvas.addEventListener('touchend', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    const tcs = e.targetTouches;
                    const ccs = e.changedTouches;
                    const touch = tcs && tcs.length && tcs[0] || ccs && ccs.length && ccs[0];
                    const offsetLeft = offset(touch.target, 'left');
                    const offsetTop = offset(touch.target, 'top');
                    let x = touch.clientX ? touch.clientX - offsetLeft : touch.clientX;
                    let y = touch.clientY ? touch.clientY - offsetTop : touch.clientY;
                    writeEnd({x, y});
                });
                /* ==========================移动端兼容=End================================ */
            }
            // /**
            //  * 下载二维码到本地
            //  */
            // const downloadSignImg = (name) => {
            //     // const c = document.getElementById(this.domId);
            //     const c = state.$refs.myCanvas;
            //     const dataURL = c.toDataURL('image/png');
            //     saveFile(dataURL, name ? `${name}.${state.config.imgType}` : `${Date.parse(new Date())}.${state.config.imgType}`);
            // }
            // /**
            //  * 保存文件
            //  */
            // const saveFile = (data, filename) => {
            //     const saveLink = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
            //     saveLink.href = data;
            //     saveLink.download = filename;
            //     const event = document.createEvent('MouseEvents');
            //     event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            //     saveLink.dispatchEvent(event);
            // }
            /**
             * 获取dom对象的偏移量 可以获取解决position定位的问题
             */
            const offset = (obj, direction) => {
                //将top,left首字母大写,并拼接成offsetTop,offsetLeft
                const offsetDir = 'offset' + direction[0].toUpperCase() + direction.substring(1);
                let realNum = obj[offsetDir];
                let positionParent = obj.offsetParent;  //获取上一级定位元素对象
                while (positionParent != null) {
                    realNum += positionParent[offsetDir];
                    positionParent = positionParent.offsetParent;
                }
                return realNum;
            }

            const init = () => {
                nextTick(() => {
                    state.canvas = myCanvas.value;
                    state.context = state.canvas.getContext("2d");
                    const options = state.options;
                    if (options) {
                        for (const key in options) {
                            state.config[key] = options[key];
                        }
                    }
                    canvasInit();
                    canvasClear();
                })
            };
            return {
                ...toRefs(state),
                props, myCanvas,
                onChangeShow,
                onCancel,
                canvasClear,
                saveAsImgOK,
            }
        },
    }
</script>

<style scoped>

    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }

    .app-sign-canvas {
        margin: 12px;
        border: 1px dashed #f00;
        z-index: -1;
        top: 0;
        background: #fff;

    }
    .fixed-water-mark {
        position: fixed;
        pointer-events: none;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1600;
        background: red;
    }

    .fixed-water-mark #watermark {
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        opacity: 0.4;
        margin: 0 auto;
        background: #ffa200;
    }
</style>

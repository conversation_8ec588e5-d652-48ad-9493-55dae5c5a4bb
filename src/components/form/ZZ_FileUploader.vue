<template>
    <div class="wrap">
        <van-popup :style="{ height: '28%' }" v-model:show="isShowDigitaSign" :close-on-click-overlay="false"
                   position="bottom">
            <van-uploader style="margin: 8px" v-model="fileList" :accept="fileTypeTrans" :multiple="true"
                          :max-size="15000 * 1024" @oversize="onOversize" :max-count="3"/>
            <div v-if="fileType" class="text-type">文件格式限制：{{ fileType }}</div>
            <div class="van-submit-bar">
                <van-button round size="small" type="default" @click="onCancel">取消</van-button>
                <van-button round size="small" type="success" @click="onConfirm">确认</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    import {computed, onMounted, reactive, toRefs, watch} from "vue";

    export default {
        name: "ZZ_FileUploader",
        props: {
            show: Boolean,
            fileType: String
        },
        setup(props, ctx) {
            const state = reactive({
                isShowDigitaSign: props.show,
                fileList: []
            })
            onMounted(() => {
            });

            watch(() => props.show, (newVal) => {
                state.isShowDigitaSign = newVal
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组
                if (newVal) initResults();
            })

            const fileTypeTrans = computed(() => {
                if (props.fileType) {
                    return "*"
                }
                return "image/*"
            })

            const types = computed(() => {
                if (props.fileType) {
                    return props.fileType.split(',');
                }
                return null;
            })
            const initResults = () => {
                state.fileList = [];
            };
            const onOversize = () => {
                this.$dialog.alert({
                    message: '文件大小不能超过 15M'
                })
            };
            const onCancel = () => {
                onChangeShow(false);
            };
            const onChangeShow = (value) => {
                ctx.emit('update:show', value);
            };
            const onConfirm = () => {
                if (state.fileList.length > 0) {
                    let files = [];
                    for (const f of state.fileList) {
                        if (types.value) {
                            let type = f.file.name.split('.')[1];
                            if (types.value.includes(type)) {
                                files.push(f);
                            }
                        } else {
                            files.push(f);
                        }
                    }
                    ctx.emit('update:model-value', files);
                }
                onCancel();
            };

            return {
                ...toRefs(state),
                fileTypeTrans, types,
                initResults,
                onConfirm,
                onCancel,
                onOversize,
            }
        },
    }
</script>

<style scoped>

    .van-submit-bar {
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        display: flex;
        height: 50px;
    }

    .van-submit-bar .van-button {
        margin: 5px;
        width: 35%;
    }

    .text-type {
        margin-bottom: 4px;
        margin-left: 2px;
        font-size: 1em;
        color: darkgrey;
    }
</style>

<template>
    <div>

        <van-field
                   :rows="4"
                   autosize
                   type="textarea"
                   :label="'审批意见'"
                   :model-value="valueFrom ? valueFrom.spyj : ''"
                   @update:model-value="onInput3($event)"
                   :required="field.options.required"
                   :placeholder="'请输入审批意见'"
                   :disabled="field.options.disabled"
                   :readonly="field.options.readonly"/>

        <van-field :error-message="errorMessage"
                   :label="'审批时间'"
                   :model-value="valueFrom ? valueFrom.spsj : ''"
                   :required="required"
                   @click="selectorShow = true && !disabled"
                   :disabled="disabled"
                   readonly
                   :placeholder="placeholder"/>
        <van-popup v-model:show="selectorShow"
                   position="bottom" teleport="body">
            <van-date-picker v-model="currentDate"
                             :title="'审批时间'"
                             @confirm="onConfirm"
                             @cancel="selectorShow=false"/>
        </van-popup>


        <van-field :error-message="errorMessage"
                   v-if="field.options.showInput === false"
                   :label="'审批人'"
                   :required="required"
                   :placeholder="placeholder?placeholder:''"
                   readonly>
            <template #button>
                <van-button  :disabled="disabled" size="small" type="primary"
                            @click="onClickSignature">点我签字
                </van-button>
            </template>
        </van-field>
        <van-field v-if="field.options.showInput === true"
                   :label="'审批人'"
                   :model-value="valueFrom ? valueFrom.spr : ''"
                   @update:model-value="onInput2($event)"
                   :required="field.options.required"
                   :placeholder="'请输入审批人名称'"
                   :disabled="field.options.disabled"
                   :readonly="field.options.readonly"/>

        <div v-if="oldFiles && field.options.showInput === false">
            <div class="file-item" :key="fIndex" v-for="(file,fIndex) in oldFiles">
                <van-icon v-if="!readOnlyMark && !disabled" name="delete" size="1.5em" @click="onDeleteFile(fIndex)"/>
                <div v-if="file.type == 'picture-upload' || file.type == 'sign'|| file.type == 'img-upload'" class="file-item-other">
                    <ZZ_Image v-if="file.data" :src="file.data" :type="file.type" width="100%"></ZZ_Image>
                </div>
                <div v-else class="file-item-other">
                    <a> {{file.name}}</a>
<!--                    {{baseUrl+file.data}}-->
                </div>
            </div>
        </div>
        <ZZ_FileUploaderDigitaSign @update:model-value="onInput($event)" v-model:show="isShowDigitaSign"
                                   :wxts="field.wxts"
                                   :file-type="field.el"></ZZ_FileUploaderDigitaSign>
    </div>
</template>

<script>
    import ZZ_FileUploaderDigitaSign from "@/components/form/ZZ_FileUploaderDigitaSign";
    import ZZ_Image from "@/components/ZZ_Image";
    import {isImage} from "@/utils/common_file";
    import {computed, onMounted, reactive, toRefs,} from "vue";
    import {mapState} from "vuex";
    import Utils from "@/utils/momentWrap";

    export default {
        components: {
             ZZ_Image,
            ZZ_FileUploaderDigitaSign
        },
        name: "ZZ_FieldFileUploader",
        props: {
            field: Object,
            value: String,
            wxts: String,
            errorMessage: String,
            placeholder: String,
            label: String,
            required: Boolean,
            oldFilesObj: Object,
            readOnlyMark: Boolean,
            disabled: Boolean,
        },
        computed: {
            ...mapState(["baseUrl"])
        },
        setup(props, ctx) {
            const state = reactive({
                isShow: false,
                isShowDigitaSign: false,
                selectorShow: false,
                valueShow: null,
                valueFrom: null,
                currentDate: [],
                columnsType: ['hour', 'minute', 'second']
            })

            onMounted(async () => {
                // state.valueShow = props.value ? dateFormat_YMD(props.value) : null;
                state.valueFrom = JSON.parse(props.value)
                console.log(`state.valueFrom`,state.valueFrom)
                console.log(`props.field`,props.field)

                state.currentDate = dateFormat_YMD(new Date()).split('-')
            })

            const oldFiles = computed(() => {
                // console.log(`oldFiles`, props.oldFilesObj.files)
                // 注意必须return(否则newMoney没有值)
                return state.valueFrom ? [{data:state.valueFrom.qm,type: 'sign'}] : []
            })
            const onClickSignature = () => {
                //签名只保留一张
              state.isShowDigitaSign = true;
            }
            const getIsImage = (file) => {
                return isImage(file);
            }
            const onDelete = () => {
                ctx.emit("update:file");
            }
            const onDeleteFile = (index) => {
                ctx.emit("update:deletefile", props.field.id, index);
            }
            const dateFormat_YMD = (o) => {
                return Utils.dateFormat_YMD(o);
            }
            const onInput = (value) => {
                console.log(value)
                state.valueFrom.qm = value[0].content
                ctx.emit("update:model-value", state.valueFrom);

            }
            const onInput2 = (value) => {
                console.log(value)
                state.valueFrom.spr = value
                ctx.emit("update:model-value", state.valueFrom);

            }
            const onInput3 = (value) => {
                console.log(value)
                state.valueFrom.spyj = value
                ctx.emit("update:model-value", state.valueFrom);

            }
            const onConfirm = () => {
                state.valueFrom.spsj = state.currentDate.join('-')
                ctx.emit("update:model-value", state.valueFrom);
                state.selectorShow = false;
            }
            return {
                ...toRefs(state),
                oldFiles,
                onClickSignature,
                getIsImage,
                onDeleteFile,
                onInput,
                onInput2,
                onInput3,
                onDelete,
                onConfirm,
                dateFormat_YMD
            }
        },
    }
</script>

<style scoped>
    .file-item {
        margin-top: 3px;
        position: relative;
        padding: 0 5px;
        display: inline-block;
        width: 30%;
        height: 140px;
        overflow: hidden;
    }

    .file-item-other {
        display: block;
    }

    .file-item .van-icon {
        vertical-align: sub;
        position: absolute;
        right: 5px;
        top: 0;
    }

    .van-doc-demo-block[data-v-9986b786] .file-item .van-icon-delete {
        position: absolute;
        left: 105px;
        bottom: 5px;
        top: 0;
    }

    .van-hairline--top-bottom {
        position: static;
    }

</style>

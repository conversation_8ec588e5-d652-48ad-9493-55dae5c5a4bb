<template>
    <div>
        <!--  class="vertical-apply" 表单上下两行排列   -->
        <div>
            <div v-for="(field,index) in formFields" :key="index" :class="setClassMark">
                <ZZ_FieldCascaderSelector v-if="field.type === 'cascader'"
                                          :style="{display:field.options.hidden ? 'none' : 'block'}"
                                          :errorMessage="getErrorMessage(field.options.name)"
                                          :key="field.options.name+'cascader'"
                                          :disabled="field.options.disabled"
                                          :readonly="field.options.readonly"
                                          :value="setformObj[field.options.name]"
                                          :label="field.options.label"
                                          @update:model-value="onInput(field, $event)"
                                          :required="field.options.required"
                                          :placeholder="returnPlaceholder(field)"
                                          :options="field.options.optionItems"></ZZ_FieldCascaderSelector>
                <ZZ_FieldMultiSelector v-else-if="field.type === 'checkbox'"
                                       :style="{display:field.options.hidden ? 'none' : 'block'}"
                                       :errorMessage="getErrorMessage(field.options.name)"
                                       :key="field.options.name+'multiSelector'"
                                       :value="setformObj[field.options.name]"
                                       :label="field.options.label"
                                       @update:model-value="onInput(field, $event)"
                                       :required="field.options.required"
                                       :placeholder="returnPlaceholder(field)"
                                       :options="field.options.optionItems"
                                       :disabled="field.options.disabled"></ZZ_FieldMultiSelector>
                <ZZ_FieldSelector v-else-if="field.type === 'radio' || field.type === 'select'"
                                  :style="{display:field.options.hidden ? 'none' : 'block'}"
                                  :field="field"
                                  :errorMessage="getErrorMessage(field.options.name)"
                                  :key="field.options.name+'selector'"
                                  :value="setformObj[field.options.name]"
                                  :label="field.options.label"
                                  @update:model-value="onInput(field, $event)"
                                  :required="field.options.required"
                                  :placeholder="returnPlaceholder(field)"
                                  :options="field.options.optionItems"
                                  :disabled="field.options.disabled"></ZZ_FieldSelector>
                <ZZ_FieldTimePicker v-else-if="field.type === 'time'"
                                    :style="{display:field.options.hidden ? 'none' : 'block'}"
                                    :errorMessage="getErrorMessage(field.options.name)"
                                    :field="field"
                                    :key="field.options.name+'time'"
                                    :value="setformObj[field.options.name]"
                                    :label="field.options.label"
                                    @update:model-value="onInput(field, $event)"
                                    :required="field.options.required"
                                    :disabled="field.options.disabled"
                                    :placeholder="returnPlaceholder(field)"></ZZ_FieldTimePicker>
                <ZZ_FieldTimePickerGroup v-else-if="field.type === 'time-range'"
                                         :style="{display:field.options.hidden ? 'none' : 'block'}"
                                         :errorMessage="getErrorMessage(field.options.name)"
                                         :field="field"
                                         :key="field.options.name+'time-range'"
                                         :value="setformObj[field.options.name]"
                                         :label="field.options.label"
                                         @update:model-value="onInput(field, $event)"
                                         :required="field.options.required"
                                         :disabled="field.options.disabled"
                                         :placeholder="returnPlaceholder(field)"></ZZ_FieldTimePickerGroup>
                <ZZ_FieldDatePicker v-else-if="field.type === 'date'"
                                    :style="{display:field.options.hidden ? 'none' : 'block'}"
                                    :errorMessage="getErrorMessage(field.options.name)"
                                    :field="field"
                                    :key="field.options.name+'date'"
                                    :value="setformObj[field.options.name]"
                                    :label="field.options.label"
                                    @update:model-value="onInput(field, $event)"
                                    :required="field.options.required"
                                    :disabled="field.options.disabled"
                                    :readonly="field.options.readonly"
                                    :placeholder="returnPlaceholder(field)"></ZZ_FieldDatePicker>
                <ZZ_FieldDatePickerGroup v-else-if="field.type === 'date-range'"
                                         :style="{display:field.options.hidden ? 'none' : 'block'}"
                                         :errorMessage="getErrorMessage(field.options.name)"
                                         :field="field"
                                         :key="field.options.name+'date-range'"
                                         :value="setformObj[field.options.name]"
                                         :label="field.options.label"
                                         @update:model-value="onInput(field, $event)"
                                         :required="field.options.required"
                                         :disabled="field.options.disabled"
                                         :placeholder="returnPlaceholder(field)"></ZZ_FieldDatePickerGroup>
                <ZZ_FieldFileUploader v-else-if="field.type === 'file-upload'
                                        || field.type === 'picture-upload'
                                        || (field.type === 'sign' && field.options.showInput === false) "
                                      :disabled="field.options.disabled"
                                      :style="{display:field.options.hidden ? 'none' : 'block'}"
                                      :errorMessage="getErrorMessage(field.options.name)"
                                      :field="field"
                                      :key="field.options.name+field.type"
                                      :value="setformObj[field.options.name]"
                                      @update:addfiles="onAddFiles"
                                      :oldFilesObj="onGetOldFiles(field.options.name)"
                                      @update:deletefile="onDeleteFile"
                                      :label="field.options.label"
                                      :required="field.options.required"
                                      :placeholder="returnPlaceholder(field)"></ZZ_FieldFileUploader>
              <ZZ_FieldSpzj v-else-if="field.type === 'spzj'"
                                    :disabled="field.options.disabled"
                                    :style="{display:field.options.hidden ? 'none' : 'block'}"
                                    :errorMessage="getErrorMessage(field.options.name)"
                                    :field="field"
                                    :key="field.options.name+field.type"
                                    :value="setformObj[field.options.name]"
                                    @update:model-value="onAddFilesspzj(field, $event)"
                                    :label="field.options.label"
                                    :required="field.options.required"
                                    :placeholder="returnPlaceholder(field)"></ZZ_FieldSpzj>
                <van-cell v-else-if="field.type === 'number'"
                          :style="{display:field.options.hidden ? 'none' : 'flex'}"
                          :error-message="getErrorMessage(field.options.name)"
                          :key="field.options.name"
                          :title="field.options.label"
                          :placeholder="returnPlaceholder(field)"
                          :required="field.options.required">
                    <van-stepper :model-value="setformObj[field.options.name]" input-width="80px"
                                 button-size="32px"
                                 :min="field.options.min"
                                 :max="field.options.max"
                                 :disabled="field.options.disabled"
                                 @update:model-value="onInput(field, $event)"
                                 :step="field.options.step"/>

                    <!--                   -->
                </van-cell>
                <van-cell v-else-if="field.type === 'sub-form'"
                          :style="{display:field.options.hidden ? 'none' : 'block'}"
                          :error-message="getErrorMessage(field.options.name)"
                          :key="field.options.name"
                          :title="field.options.label"
                          :placeholder="returnPlaceholder(field)"
                          :disabled="field.options.disabled"
                          :required="field.options.required">
                    <template v-if="field.type === 'sub-form' && subformlist">
                        <div v-for="(schema,index) in subformlist[field.options.name]" :key="index" class="subformbox">
                            <div style="display: flex;justify-content: space-between;padding: 0 10px;line-height: 25px;font-size: 15px;background-color: #F2F2F2;">
                            <span>
                                #{{index+1}}
                            </span>
                                <van-icon v-if="!field.options.disabled" style="color: red;line-height: 25px;" name="clear" @click="deleteSubFormPopup(field,subformlist[field.options.name],index)" />
                            </div>
                            <ZZ_FieldSubForm :key="subs" @update:model-value="onInput($event)"
                                             v-model:show="showSubFormPopup" :fields="schema" :subname="field.options.name" @subform2="subform2"></ZZ_FieldSubForm>
                        </div>
                    </template>
                    <div v-if="!field.readonly" style="text-align: center">
                        <van-button :disabled="field.options.disabled" plain type="info"
                                    style="border: 0px;"
                                    @click="onSubFormPopup(field,setformObj[field.options.name])"><van-icon name="plus" />添加
                        </van-button>
                    </div>
                </van-cell>
                <van-cell v-else-if="field.type === 'switch'"
                          :style="{display:field.options.hidden ? 'none' : 'block'}"
                          :error-message="getErrorMessage(field.options.name)"
                          :key="field.options.name"
                          :title="field.options.label"
                          :placeholder="returnPlaceholder(field)"
                          :disabled="field.options.disabled"
                          :required="field.options.required">
                    <van-switch :model-value="setformObj[field.options.name]" :disabled="field.options.disabled"
                                @update:model-value="onInput(field, $event)"/>
                </van-cell>
                <van-cell v-else-if="field.type === 'rate'"
                          :style="{display:field.options.hidden ? 'none' : 'block'}"
                          :error-message="getErrorMessage(field.options.name)"
                          :key="field.options.name"
                          :title="field.options.label"
                          :placeholder="returnPlaceholder(field)"
                          :disabled="field.options.disabled"
                          :required="field.options.required">
                    <van-rate :model-value="setformObj[field.options.name]" :disabled="field.options.disabled"
                              @update:model-value="onInput(field, $event)"/>
                </van-cell>
                <van-cell v-else-if="field.type === 'color'"
                          :style="{display:field.options.hidden ? 'none' : 'block'}"
                          :error-message="getErrorMessage(field.options.name)"
                          :key="field.options.name"
                          :title="field.options.label"
                          :placeholder="returnPlaceholder(field)"
                          :disabled="field.options.disabled"
                          :required="field.options.required">
                    <color-picker :model-value="setformObj[field.options.name]" :disabled="field.options.disabled"
                                  format="hex6" shape="circle" useType="both"
                                  @update:pureColor="onInput(field, $event)"></color-picker>
                </van-cell>
                <van-field v-else-if="field.type === 'slider'"
                           :style="{display:field.options.hidden ? 'none' : 'block'}"
                           :error-message="getErrorMessage(field.options.name)"
                           :key="field.options.name"
                           :label="field.options.label"
                           :placeholder="returnPlaceholder(field)"
                           :disabled="field.options.disabled"
                           :required="field.options.required"
                           label-align="top">
                    <template #input>
                        <van-slider :model-value="setformObj[field.options.name]" :disabled="field.options.disabled"
                                    @change="onInput(field, $event)"/>
                    </template>
                </van-field>
                <van-field v-else-if="field.type === 'textarea' || field.type === 'rich-editor'"
                           :style="{display:field.options.hidden ? 'none' : 'block'}"
                           :rows="field.options.rows"
                           autosize
                           type="textarea"
                           :error-message="getErrorMessage(field.options.name)"
                           :key="field.options.name"
                           :maxlength="field.maxLength ? field.maxLength : null"
                           :label="field.options.label"
                           :model-value="setformObj[field.options.name]"
                           @update:model-value="onInput(field, $event)"
                           :required="field.options.required"
                           :placeholder="returnPlaceholder(field)"
                           :disabled="field.options.disabled"
                           :readonly="field.options.readonly"/>
                <ZZ_FieldDeptCascaderSelector v-else-if="field.type === 'deptcheck'"
                                              :style="{display:field.options.hidden ? 'none' : 'block'}"
                                              :errorMessage="getErrorMessage(field.options.name)"
                                              :key="field.options.name+'deptcheck'"
                                              :value="setformObj[field.options.name]"
                                              :label="field.options.label"
                                              @update:model-value="onInput(field, $event)"
                                              :required="field.options.required"
                                              :placeholder="returnPlaceholder(field)"
                                              :disabled="field.options.disabled"></ZZ_FieldDeptCascaderSelector>
                <van-field v-else-if="field.type === 'person'"
                           :style="{display:field.options.hidden ? 'none' : 'block'}"
                           :rows="'3'"
                           autosize
                           type="textarea"
                           :error-message="getErrorMessage(field.options.name)"
                           :key="field.options.name"
                           :maxlength="field.maxLength ? field.maxLength : null"
                           :label="field.options.label"
                           :model-value="setformObj[field.options.name]"
                           :required="field.options.required"
                           :placeholder="returnPlaceholder(field)"
                           readonly>
                    <!--                    :disabled="field.options.disabled"-->
                    <template #button v-if="!field.readonly">
                        <van-button :disabled="field.options.disabled" size="small" type="primary"
                                    @click="onSelectPopup(field)">选择
                        </van-button>
                    </template>
                </van-field>
                <van-field v-else
                           :error-message="getErrorMessage(field.options.name)"
                           :key="field.options.name"
                           :style="{display:(field.options.hidden || field.type === 'sub-form') ? 'none !important' : 'block'}"
                           :maxlength="field.maxLength ? field.maxLength : null"
                           :label="field.options.label"
                           :model-value="setformObj[field.options.name]"
                           @update:model-value="onInput(field, $event)"
                           :required="field.options.required"
                           :placeholder="returnPlaceholder(field)"
                           :disabled="field.options.disabled"
                           :readonly="field.options.readonly"/>
            </div>
        </div>
        <van-popup v-model:show="showSelectPopup" position="bottom"
                   :style="{ height: '80%',width:'101%' }">
            <SetRang :code="code"
                     :rang="rang"
                     :setMark="currentSelectType"
                     @update:checkChange="onCheckChange"
                     @update:handleRangCancle="showSelectPopup=false"></SetRang>
        </van-popup>
    </div>
</template>

<script>
    import SetRang from '@/components/Rang'
    import ZZ_FieldSubForm from "@/components/form/ZZ_FieldSubForm";
    import ZZ_FieldSelector from "@/components/form/ZZ_FieldSelector";
    import ZZ_FieldMultiSelector from "@/components/form/ZZ_FieldMultiSelector";
    import ZZ_FieldDatePicker from "@/components/form/ZZ_FieldDatePicker";
    import ZZ_FieldTimePicker from "@/components/form/ZZ_FieldTimePicker";
    import ZZ_FieldTimePickerGroup from "@/components/form/ZZ_FieldTimePickerGroup";
    import ZZ_FieldDatePickerGroup from "@/components/form/ZZ_FieldDatePickerGroup";
    import ZZ_FieldCascaderSelector from "@/components/form/ZZ_FieldCascaderSelector";
    import ZZ_FieldDeptCascaderSelector from "@/components/form/ZZ_FieldDeptCascaderSelector";
    import ZZ_FieldFileUploader from "@/components/form/ZZ_FieldFileUploader";
    import ZZ_FieldSpzj from "@/components/form/ZZ_FieldSpzj";
    import {formCheck} from "@/utils/zizhu_form";
    import AreaList from "@/service/area";
    import {computed, onMounted, reactive, toRefs,} from "vue";
    import {showNotify} from "vant";
    // import {validatenull} from '@/util/validate';

    export default {
        components: {
            SetRang,
            ZZ_FieldSubForm,
            ZZ_FieldCascaderSelector,
            ZZ_FieldDeptCascaderSelector,
            ZZ_FieldSelector,
            ZZ_FieldTimePicker,
            ZZ_FieldTimePickerGroup,
            ZZ_FieldDatePickerGroup,
            ZZ_FieldMultiSelector,
            ZZ_FieldDatePicker,
            ZZ_FieldSpzj,
            ZZ_FieldFileUploader
        },
        name: "ZZ_Form",
        props: {
            fields: Array,
            setformObj: Object,
            issubform: Boolean,
            subname: String,
            gid: String,
        },
        setup(props, ctx) {
            const state = reactive({
                showSubFormPopup: false,
                currentFormObj: {},
                currentFieldArr: null,
                showSelectPopup: false,
                currentField: null,
                currentSelectType: null,
                code: [],
                rang: [],

                changes: [],
                isChanging: false,
                requiredMap: new Map(),
                optionsMap: new Map(),
                attachMathMap: new Map(),
                errorMap: new Map(),
                formFields: [],
                setformObj: {},

                fieldGroupFields: [],
                fileMap: new Map(),
                fileMapChangeTracker: 0,

                areaList: AreaList,
                showAreaPopup: false,
                setDefaultCode: null,
                setDefaultAddress: null,
                setDefaultAddressDetail: null,
                showDetailAreaPopup: false,
                setAreaRequest: false,
                carmodel: null,
                setDefaultAddressStr: null,//用于编辑地址，没有改变原本值，预存入的值

                longitude: null,
                latitude: null,
                address: null,
                canLLA: true,

                subformlist: [],
                subs: 1,
            })
            onMounted(async () => {
                setTimeout(() => {
                    state.formFields = props.fields
                    state.setformObj = props.setformObj
                    console.log(`state.formFields`,state.formFields)
                    console.log(`state.setformObj`,state.setformObj)
                    onInit()
                }, 100)

            })

            const setClassMark = computed(() => {
                if (props.setClass === 'info') {
                    return ''
                } else {
                    return 'vertical-apply'
                }
            })
            const codes = computed(() => {
                let arr = [];
                if (state.rang) {
                    state.rang.forEach(item => {
                        arr.push(item.id)
                    })
                }
                return arr
            })
            const names = computed(() => {
                let arr = [];
                if (state.rang) {
                    state.rang.forEach(item => {
                        arr.push(item.humanname)
                    })
                }
                return arr.join()
            })

            const onInit = () => {
                if (state.formFields) {
                    state.formFields.forEach(field => {
                        const en = field.options.name;

                        if(field.options.expression && field.options.expression !== "" && state.setformObj[en] == ''){
                            // console.log(`field888888888888888888888888`,field,state.setformObj[en])
                            state.setformObj[en] = field.options.expression
                        }
                        if ("file-upload" === field.type || "picture-upload" === field.type || "sign" === field.type) {
                            let value = state.setformObj[en];
                            let arr = [];
                            if ("sign" === field.type) {
                                arr.push({
                                    id: '',
                                    name: '',
                                    data: value,
                                    type: field.type
                                })
                            } else {
                                console.log(`value=====`,value)
                                if (value && value !== undefined) {
                                    value.forEach(item => {
                                        if (item.response && item.response.info && item.response.code == "00000") {
                                            let res = item.response.info
                                            arr.push({
                                                id: res.id,
                                                name: res.name,
                                                data: res.url,
                                                type: field.type
                                            })
                                        } else {
                                            console.log(Array.isArray(item))
                                            if(Array.isArray(item)){
                                                item.forEach(i=>{
                                                    if(i.data){
                                                        arr.push({
                                                            id: '',
                                                            name: i.name,
                                                            data: i.data,
                                                            type: i.type
                                                        })
                                                    }
                                                })
                                            }else{
                                                if(item.data){
                                                    arr.push({
                                                        id: '',
                                                        name: item.name,
                                                        data: item.data,
                                                        type: item.type
                                                    })
                                                }
                                            }

                                            console.log(`item=====`,item)
                                            showNotify({type: 'warning', message: item.response ? item.response.info : ''});
                                        }
                                    })
                                }

                            }
                            state.fileMap.set(en, arr);
                            console.log(`state.fileMap`,state.fileMap)
                            state.fileMapChangeTracker++;
                        }
                        if ("sub-form" === field.type) {
                            state.subformlist[field.options.name] = state.setformObj[field.options.name]
                            state.showSubFormPopup = true
                        }

                        //组件联动
                        if (field.options && field.options.linkbuttons && field.options.linkbuttons.dynamic && field.options.linkbuttons.dynamic.length > 0) {
                            // console.log(`field.options.linkbuttons`,field.options.linkbuttons)
                            field.options.linkbuttons.dynamic.forEach(item => {
                                if (item.widgetVal == state.setformObj[en]) {
                                    // console.log(`state.formFields`,state.formFields)
                                    state.formFields.forEach(e => {
                                        if (e.options.name == item.widgetName) {
                                            e.options.hidden = item.isHidden
                                            e.options.isHidden = item.isHidden
                                            e.options.required = item.isRequired
                                            e.options.isRequired = item.isRequired
                                        }
                                    })
                                } else {
                                    // state.formFields.forEach(e => {
                                    //     if (e.options.name == item.widgetName) {
                                    //         e.options.hidden = !item.isHidden
                                    //         e.options.isHidden = !item.isHidden
                                    //     }
                                    // })
                                }
                            })
                        }
                    })
                }
            }
            const returnPlaceholder = (field) => {
                let fieldType = field.type;
                let wxts = field.wxts;
                if (fieldType === "readonly" || fieldType === "signature") return "";
                if (["single", "select", "more", "init", "date"].includes(fieldType)) return wxts ? wxts : "请选择";
                else if (["file"].includes(fieldType)) return wxts ? wxts : "请上传";
                else if (["float"].includes(fieldType)) return wxts ? wxts : "请输入数字(支持小数)";
                else if (["integer"].includes(fieldType)) return wxts ? wxts : "请输入数字";
                else if (["sub-form"].includes(fieldType)) return wxts ? wxts : "向左滑动删除";
                else return wxts ? wxts : "请输入";
            }
            const subform2 = (name) => {
                let num = 0
                let listname = null;
                setTimeout(() => {
                    // console.log(`state.formFields`,state.formFields)
                    // console.log(`state.setformObj`,state.setformObj)
                    state.setformObj[name].forEach(item=>{
                        item.forEach(wid=>{
                            if(wid.options.subformmount === true){
                                num = num + wid.values
                                listname = wid.id
                            }
                        })
                    })
                    // console.log(`state.setformObj[name]2`,num)
                    state.formFields.forEach(field => {
                        if (field.options.subformall == listname) {
                            // console.log(field.options.name)
                            field.values = num
                            state.setformObj[field.options.name] = num
                        }
                    })
                }, 100)
            }

            const getRegExp = function (validatorName) {
                const commonRegExp = {
                    number: /^\\d+(\\.\\d+)?$/,
                    letter: /^[A-Za-z]+$/,
                    letterAndNumber: /^[A-Za-z0-9]+$/,
                    mobilePhone: /^[1][3-9][0-9]{9}$/,
                    letterStartNumberIncluded: /^[A-Za-z]+[A-Za-z\\d]*$/,
                    noChinese: /^[^\u4e00-\u9fa5]+$/,
                    chinese: /^[\u4e00-\u9fa5]+$/,
                    email: /^([-_A-Za-z0-9.]+)@([_A-Za-z0-9]+\\.)+[A-Za-z0-9]{2,3}$/,
                    url: '/^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$/',
                }
                return commonRegExp[validatorName]
            }

            const FormValidators = (type) => {
                if(type == 'number'){
                    return '包含非数字字符'
                }else if(type == 'letter'){
                    return '包含非字母字符'
                }else if(type == 'letterAndNumber'){
                    return '只能输入字母或数字'
                }else if(type == 'mobilePhone'){
                    return '手机号码格式有误'
                }else if(type == 'letterStartNumberIncluded'){
                    return '必须以字母开头'
                }else if(type == 'noChinese'){
                    return '不可输入中文字符'
                }else if(type == 'chinese'){
                    return '只能输入中文字符'
                }else if(type == 'email'){
                    return '邮箱格式有误'
                }else if(type == 'url'){
                    return 'URL格式有误'
                }
            }

            const onInput = (field, value) => {
                // console.log(`field`,field)
                let key = field.options.name;
                // if (props.issubform) {
                //     key = field.id
                // }
                if(field.options.validation  && field.options.validation!==''){
                    if(!getRegExp(field.options.validation).test(value)){
                        state.errorMap.set(
                            key,
                            FormValidators(field.options.validation)
                        );
                    }else {
                        state.errorMap.set(
                            key,
                            ''
                        );
                    }
                }
                state.setformObj[key] = value;
                ctx.emit('toformsubmit',{key:field.options.name,value: value,gid: props.gid})

                //子表单列计算
                if(field.options.subformmount == true){
                    console.log(`计算`)
                    ctx.emit('subform',props.subname)
                }
                //子表单行内计算
                if (props.issubform) {
                    state.formFields.forEach(field => {
                        if (field.options.subcalculation && field.options.subcalculation !== "") {
                            let zujianlist = []
                            let zujianlistdata = []
                            let mathlist = []
                            zujianlist = getStrinsBetweenTwoStrings(field.options.subcalculation, "{", "}")
                            mathlist = getStrinsBetweenTwoStrings(field.options.subcalculation, "<", ">")
                            zujianlistdata = []
                            zujianlist.forEach((item) => {
                                zujianlistdata.push(state.setformObj[item])
                            })
                            let data = null
                            zujianlistdata.forEach((item, index) => {
                                if (index == zujianlistdata.length - 1) {
                                    data = data + item
                                } else {
                                    data = data + item + mathlist[index]
                                }
                            })
                            state.setformObj[field.options.name] = eval(data)
                        }
                    })
                }
                //组件联动
                if (field.options && field.options.linkbuttons && field.options.linkbuttons.dynamic && field.options.linkbuttons.dynamic.length > 0) {
                    field.options.linkbuttons.dynamic.forEach(item => {
                        if (item.widgetVal == value) {
                            state.formFields.forEach(e => {
                                if (e.options.name == item.widgetName) {
                                    e.options.hidden = item.isHidden
                                    e.options.isHidden = item.isHidden
                                    e.options.required = item.isRequired
                                    e.options.isRequired = item.isRequired
                                }
                            })
                        } else {
                            // state.formFields.forEach(e => {
                            //     if (e.options.name == item.widgetName) {
                            //         e.options.hidden = !item.isHidden
                            //         e.options.isHidden = !item.isHidden
                            //     }
                            // })
                        }
                    })
                }
                //组件计算
                if (field.options.calculation || field.options.calculation == "") {
                    state.formFields.forEach(field => {
                        if (field.options.calculation && field.options.calculation !== "") {
                            let zujianlist = []
                            let zujianlistdata = []
                            let mathlist = []
                            zujianlist = getStrinsBetweenTwoStrings(field.options.calculation, "{", "}")
                            mathlist = getStrinsBetweenTwoStrings(field.options.calculation, "<", ">")
                            zujianlistdata = []
                            zujianlist.forEach((item) => {
                                zujianlistdata.push(state.setformObj[item])
                            })
                            let data = null
                            zujianlistdata.forEach((item, index) => {
                                if (index == zujianlistdata.length - 1) {
                                    data = data + item
                                } else {
                                    data = data + item + mathlist[index]
                                }
                            })
                            state.setformObj[field.options.name] = eval(data)
                        }
                    })
                }
                //获取日期天数
                if (field.type == "date-range") {
                    // let val = value.split(" ")
                    let val = value
                    let data1 = val[0];
                    let data2 = val[1];
                    let datas = {
                        containday: getWeekday(data1, data2),
                        nocontainday: dataDiff(data2, data1) + 1
                    }
                    let name = field.options.name
                    state.formFields.forEach(field => {
                        if (field.formItemFlag == true && field.options.getdays) {
                            if (field.options.getdays.widget == name) {
                                if (field.options.getdays.datatype == '0') {
                                    state.setformObj[field.options.name] = datas.nocontainday
                                } else {
                                    state.setformObj[field.options.name] = datas.containday
                                }
                            }
                        }
                    })
                }
                if (props.issubform) {
                    // console.log(`state.formFields`,state.formFields)
                    // console.log(`field`,field)
                    // console.log(`state.setformObj`,state.setformObj)

                    state.formFields.forEach(item => {
                        if (item.options.name == field.options.name) {
                            item.values = value
                        }
                    })
                }




            }

            const dataDiff = (sDate1, sDate2) => {
                var aDate, oDate1, oDate2, iDays
                if (sDate1.indexOf("-") !== -1) {
                    aDate = sDate1.split("-")
                    oDate1 = new Date(aDate[0], aDate[1], aDate[2])
                    aDate = sDate2.split("-")
                    oDate2 = new Date(aDate[0], aDate[1], aDate[2])
                } else {
                    aDate = sDate1.split(":")
                    oDate1 = new Date(aDate[0], aDate[1], aDate[2])
                    aDate = sDate2.split(":")
                    oDate2 = new Date(aDate[0], aDate[1], aDate[2])
                }
                iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24)
                return iDays
            }

            const getWeekday = (first, last) => {
                if (first.indexOf("-") !== -1) {
                    first = first.split("-")
                    first = new Date(first[0], first[1] - 1, first[2])
                    last = last.split("-")
                    last = new Date(last[0], last[1] - 1, last[2])
                } else {
                    first = first.split(":")
                    first = new Date(first[0], first[1] - 1, first[2])
                    last = last.split(":")
                    last = new Date(last[0], last[1] - 1, last[2])

                }
                first = first.getTime();
                last = last.getTime();
                var count = 0;
                for (var i = first; i <= last; i += 24 * 3600 * 1000) {
                    var d = new Date(i);
                    if (d.getDay() >= 1 && d.getDay() <= 5) {
                        count++;
                    }
                }
                return count;
            }

            const getStrinsBetweenTwoStrings = (targetString, beginString, endString) => {
                if (targetString !== '' && targetString !== undefined) {
                    let aa = targetString.split(beginString), re = [];
                    for (let i = 1, len = aa.length; i < len; i++) {
                        let a = aa[i], s = a.split(endString)[0];
                        s && re.push(s);
                    }
                    return re;
                }
            }

            const getErrorMessage = (key) => {
                return state.errorMap.get(key);
            }
            const onGetOldFiles = (en) => {
                console.log(`onGetOldFiles`,state.fileMap.get(en))
                return {
                    tracker: state.fileMapChangeTracker,
                    files: state.fileMap.get(en)
                };
            }
            const getFileStr = () => {
                return JSON.stringify(state.mapToObj(state.fileMap));
            }
            const mapToObj = (strMap) => {
                let obj = {};
                for (let [k, v] of strMap) {
                    obj[k] = v;
                }
                return obj;
            }
            const onDeleteFile = (en, index) => {
                let arr1 = state.fileMap.get(en);
                if (arr1 && arr1.length > index) {
                    arr1.splice(index, 1);
                    state.fileMap.set(en, arr1);
                    state.fileMapChangeTracker--;
                    state.setformObj[en].splice(index, 1);
                }
            }
            const onAddFiles = (en, arr) => {
                console.log(`onAddFiles`,en,arr)
                let arr1 = state.fileMap.get(en);
                if (!arr1) {
                    arr1 = [];
                }
                for (const arrE of arr) {
                    arr1.push(arrE);
                }
                state.fileMap.set(en, arr1);
                state.fileMapChangeTracker++;
                state.setformObj[en] = (arr1);
                console.log(`state.setformObj`,state.setformObj)

            }
            const onAddFilesspzj = (en,val) => {
                // console.log(val)
                state.setformObj[en.options.name] = JSON.stringify(val)
            }
            // const fieldCheck = (field, value) => {
            //     state.errorMap.set(
            //         field.en,
            //         onFieldCheck3(field, value, onGetOldFiles(field.en).files)
            //     );
            // }
            const checkForm = () => {
                if (formCheck(state.formFields, state.value, state.fileMap)) {
                    ctx.emit("formsubmit", state.formFields, state.getFileStr());
                }
            }
            const checkFormZC = () => {
                ctx.emit("formsubmit", state.formFields, JSON.stringify(mapToObj(state.fileMap)))
            }
            const onCheckChange = (e) => {
                if(e){
                    let userCode = e.id;
                    if (codes.value.includes(userCode)) {
                        state.rang.splice(codes.value.indexOf(userCode), 1)
                    } else {
                        state.rang.push(e)
                    }
                }
                let arr = [];
                state.rang.forEach(item => {
                    arr.push(item.humanname)
                })
                onInput(state.currentField, arr.join());
            }
            const onSelectPopup = (field) => {
                // if(val == ''){
                //     state.rang = []
                // }else {
                //     state.rang = val.split(',')
                // }

                state.currentField = field
                state.currentSelectType = field.type;
                state.showSelectPopup = true
            }
            const onSubFormPopup = (field, setformObj) => {
                field.rowIdData.push('id' + Math.floor(Math.random() * 100000 + Math.random() * 20000 + Math.random() * 5000))
                let arr = JSON.parse(JSON.stringify(field.widgetList));
                // let newfield = []
                arr.forEach(e => {
                    e.options.name = e.type + Math.floor(Math.random() * 100000 + Math.random() * 20000 + Math.random() * 5000)
                    // e.values = null
                    // newfield.push(e)
                })
                setformObj.push(arr)
                // console.log(`state.formFields`,state.formFields)
                // console.log(`state.setformObj`,state.setformObj)
                state.subs++
            }
            const deleteSubFormPopup = (field, setformObj, index) => {
                field.rowIdData.splice(index, 1)
                setformObj.splice(index, 1)
                state.subs++
            }
            return {
                ...toRefs(state),
                setClassMark, codes, names,
                returnPlaceholder,
                getErrorMessage,
                onGetOldFiles,
                getFileStr,
                onDeleteFile,
                onAddFiles,
                onAddFilesspzj,
                checkFormZC,
                checkForm,
                onInput,
                onSelectPopup,
                onSubFormPopup,
                deleteSubFormPopup,
                onCheckChange,
                subform2
            }
        },
        methods: {},
        created() {
        }
    };
</script>

<style scoped>
    .vertical-apply .van-cell__right-icon {
        margin-top: -90px;
        float: right;
    }

    .divDisplayBlack {
        display: block;
    }

    .divDisplayNone {
        display: none;
    }

    .van-submit-bar {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
    }

    .van-submit-bar-button {
        height: 35px;
        width: 45.5% !important;
        line-height: 35px;
        margin-left: 3% !important;
        background: #00a5ec;
        font-size: 16px;
        border-color: #00a5ec;
    }

    .vanButton {
        background: #fff;
        color: #00a5ec;
    }

    .vertical-apply >>> .van-cell {
        font-size: 15px;
    }

    .vertical-apply >>> .van-field {
        /*display: block;*/
        font-size: 15px;
    }

    .vertical-apply >>> .van-field .van-field__label {
        width: 100%;
        margin-bottom: 5px;
        color: #646566 !important;
    }

    .vertical-apply >>> .van-radio {
        margin-bottom: 5px;
    }


    .goods-card {
        margin: 3px !important;
        /*background-color: @white;*/
    }

    .delete-button {
        height: 100%;
    }

    ::v-deep .van-card {
        padding: 0 !important;
    }
    .subformbox{
        margin: 10px 0px;
        border: 1px solid rgba(138, 144, 156, 0.3);
        border-radius: 5px;
    }
</style>

<template>
    <van-list v-model:loading="isLoading"
              :finished="finished"
              @load="loadMore"
              v-model:error="error"
              error-text="请求失败，点击重新加载"
              finished-text="没有更多了"
              :immediate-check="firstLoad">
        <slot v-bind:list="list2"></slot>
    </van-list>
</template>
<script>
    import {diyPost} from '@/service/home'

    export default {
        name: "Base_List",
        props: {
            url: String,
            queryObj: {
                type: Object,
                default() {
                    return {};
                },
            },
            queryJson: String,
            list: {
                type: Array,
                default() {
                    return [];
                },
            },
            newPageSize: String,
        },
        data() {
            return {
                pageIndex: 0,
                pageSize: this.newPageSize ? this.newPageSize : 10,
                list2: [],
                isLoading: false,
                finished: false,
                error: null,
            };
        },
        watch: {
            list(newV) {
                if (newV !== this.list2) {
                    this.list2 = newV;
                    this.count = null;
                }
            },
            queryObj: {
                handler() {
                    this.onReload();
                },
                deep: true,
            },
            queryJson() {
                this.onReload();
            },
        },
        computed: {
            firstLoad() {
                return !!this.url;
            },
        },
        methods: {
            onReload() {
                this.finished = true;
                this.pageIndex = 0;
                this.list2 = [];
                this.$emit("update:list", this.list2);
                this.finished = false;
                this.isLoading = true;
                this.loadMore();
            },
            loadMore() {
                if (this.isLoading) {
                    this.pageIndex++;
                    let obj = this.queryObj;
                    if (this.queryJson) {
                        obj = JSON.parse(this.queryJson);
                    }
                    let newObj = Object.assign(
                        {
                            page: this.pageIndex,
                            pageSize: this.pageSize,
                        },
                        obj
                    );
                    diyPost(this.url, newObj).then((response) => {
                        if (response.status === 200 && response.data.code === "00000") {
                            let pageData = response.data.info;
                            console.log(`pageData`,pageData)
                            // if (pageData.total && pageData.current) {
                            if (pageData.records) {
                                console.log(`pageData.records`,pageData.records)
                                this.list2 = this.list2.concat(pageData.records);
                                this.$emit("update:list", this.list2);
                                this.$emit("update:pageData", pageData);
                                this.$emit("allData", this.list2);
                                this.finished = pageData.pages <= pageData.current;
                            } else {
                                this.error = true;
                                this.finished = true
                            }
                        }
                    }).catch((error) => {
                        console.error(error);
                        this.error = true;
                        this.finished = true
                    }).finally(() => (this.isLoading = false));
                }
            },
        },
    };
</script>
<style scoped>
</style>

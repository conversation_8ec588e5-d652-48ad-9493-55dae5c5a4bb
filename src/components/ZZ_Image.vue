<template>
    <van-image @click="onImageView"
               :src="fileData" :width="width" :height="height"/>
</template>

<script>
    import {showImagePreview} from 'vant';
    import 'vant/es/image-preview/style';
    import {mapState} from "vuex";
    import {computed, onMounted, reactive, toRefs,} from "vue";

    export default {
        name: "ZZ_Image",
        props: {
            type: String,
            src: String,
            width: String,
            height: String,
            id: String
        },
        setup(props,) {
            const state = reactive({})
            onMounted(() => {
            });
            let storeStateFns = mapState(["baseUrl", "defaultIcon"])

            const fileData = computed(() => {
                if (props.type === 'sign' || props.type === 'img-upload' || props.type === 'picture-upload') {
                    console.log(`fileData`,props.src)
                    return props.src;
                } else {
                    console.log(`fileData`,storeStateFns.baseUrl + props.src)
                    return storeStateFns.baseUrl + props.src
                }
            })
            const onImageView = () => {
                showImagePreview([[fileData.value]]);
            };

            return {
                ...toRefs(state),
                fileData,
                onImageView
            }
        },
    }
</script>

<style scoped>

</style>

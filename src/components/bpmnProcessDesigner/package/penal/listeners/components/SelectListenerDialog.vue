<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogOpen" @close="cancel" width="800px" append-to-body>
      <el-form :model="queryParam" ref="queryForm" :inline="true" label-width="98px">
        <el-form-item label="监听器名称" prop="listenerName">
          <el-input
            v-model="queryParam.name"
            placeholder="请输入监听器名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="监听器类型" prop="listenertype">
          <el-select v-model="queryParam.listenertype" disabled placeholder="请选择监听器类型" clearable size="small">
            <el-option v-for="item in dict.listenerType" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="事件类型" prop="eventType">
          <el-select v-model="queryParam.eventType" placeholder="请选择事件类型" clearable size="small">
            <el-option v-for="item in dict.eventType" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="值类型" prop="type">
          <el-select v-model="queryParam.type" placeholder="请选择值类型" clearable size="small">
            <el-option v-for="item in dict.valueType" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" ref="table" :data="tableList" @select="handleSelection" @select-all="handleSelectionAll">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="监听器名称" align="center" prop="name" />
        <el-table-column label="监听器类型" align="center" prop="listenertype" :formatter="formatterListenerType" />
        <el-table-column label="事件类型" align="center" prop="eventType" :formatter="formatterEventType" />
        <el-table-column label="值类型" align="center" prop="type" :formatter="formatterValueType" />
        <el-table-column label="监听器值" align="center" prop="value" />
        <el-table-column label="备注-描述" align="center" prop="remark" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getFlowListener} from '@/api/bpmListener'
import dict from './dict'

export default {
  name: 'SelectListenerDialog',
  props: {
    open: Boolean,
    listenerType: Number, // 1 执行监听器  2 任务监听器
  },
  mixins: [dict],
  watch: {
    open (val, oldVal) {
      this.dialogOpen = val;
      if (val) {
        this.getList();
      }
    }
  },
  data() {
    return {
      title: this.listenerType === 1 ? '选择执行监听器' : '选择任务监听器',
      dialogOpen: false,
      // 查询参数
      queryParam: {
        page: 1,
        pageSize: 10,
        name: null,
        listenertype: this.listenerType === 1 ? 'executionListener' : 'taskListener',
        eventType: null,
        valueType: null,
        systemListener: null,
      },
      total: 0,
      loading: false,
      tableList: [],
      selectObj: {},
      listProcessExpression: [{
        createTime: "2021-09-02 01:46:30",
        creator: null,
        delFlag: 0,
        desc: "11111",
        eventType: "start",
        id: 10,
        listenerName: "改后",
        listenerType: 1,
        listenerValue: "值111",
        systemListener: 0,
        updateTime: "2021-09-02 01:46:30",
        updator: null,
        valueType: "classListener"
      }]
    }
  },
  methods: {
    async getList () {
      this.loading = true;
      const res = await getFlowListener(this.queryParam);
      console.log(`res`,res)
      this.loading = false;
      this.tableList = res.data.info.records;
      this.total = res.data.info.total;


      // this.loading = false;
      // this.tableList = this.listProcessExpression;
      // this.total = 1;
    },
    handleSelection (select, row) {
      this.$refs.table.clearSelection();
      this.selectObj = {};
      if (row) {
        this.selectObj = row;
        this.$refs.table.toggleRowSelection(row, true);
      }
    },
    handleSelectionAll (selectList) {
      this.selectObj = {};
      this.$refs.table.clearSelection();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    submit () {
      if (this.selectObj) {
        this.$emit('submit', this.selectObj)
      }
      this.cancel()
    },
    cancel () {
      this.$emit('close');
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  }
}
</script>

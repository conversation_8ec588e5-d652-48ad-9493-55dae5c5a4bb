export default {
  computed: {
    dict() {
      return {
        // 监听器类型
        listenerType: [
          {
            label: '执行监听器',
            value: 'executionListener'
          },
          {
            label: '任务监听器',
            value: 'taskListener'
          }
        ],
        // 事件类型
        eventType: [
          {
            label: '创建',
            value: 'create'
          },
          {
            label: '指派',
            value: 'assignment'
          },
          {
            label: '完成',
            value: 'complete'
          },
          {
            label: '删除',
            value: 'delete'
          },
          {
            label: '开始',
            value: 'start'
          },
          {
            label: '结束',
            value: 'end'
          },
          {
            label: '启用',
            value: 'take'
          }
        ],
        // 任务监听器事件类型
        taskEventType: [
          {
            label: '创建',
            value: 'create'
          },
          {
            label: '指派',
            value: 'assignment'
          },
          {
            label: '完成',
            value: 'complete'
          },
          {
            label: '删除',
            value: 'delete'
          }
        ],
        // 执行监听器事件类型
        implementEventType: [
          {
            label: '开始',
            value: 'start'
          },
          {
            label: '结束',
            value: 'end'
          },
          {
            label: '启用',
            value: 'take'
          }
        ],
        // 值类型
        valueType: [
          {
            label: '类',
            value: 'class',
            field: 'class'
          },
          {
            label: '表达式',
            value: 'expression',
            field: 'expression'
          },
          {
            label: '委托表达式',
            value: 'delegateExpression',
            field: 'delegateExpression'
          }
        ],
        // 是否是系统预设监听器
        systemListener: [
          {
            label: '是',
            value: 0
          },
          {
            label: '否',
            value: 1
          }
        ],
        // 参数配置
        // 字段类型
        paramType: [
          {
            label: '字符串',
            value: 'string',
          },
          {
            label: '表达式',
            value: 'expression',
          }
        ],
        required: [
          {
            label: '是',
            value: '是'
          },
          {
            label: '否',
            value: '否'
          }
        ]
      }
    }
  },
  methods: {
    formatterListenerType(row, column, cellValue) {
      return this.dict.listenerType.find(item => item.value === cellValue)?.label ?? ''
    },
    formatterEventType(row, column, cellValue) {
      return this.dict.eventType.find(item => item.value === cellValue)?.label ?? ''
    },
    formatterTaskEventType(row, column, cellValue) {
      return this.dict.taskEventType.find(item => item.value === cellValue)?.label ?? ''
    },
    formatterImplementEventType(row, column, cellValue) {
      return this.dict.implementEventType.find(item => item.value === cellValue)?.label ?? ''
    },
    formatterValueType(row, column, cellValue) {
      return this.dict.valueType.find(item => item.value === cellValue)?.label ?? ''
    },
    formatterSystemListener(row, column, cellValue) {
      return this.dict.systemListener.find(item => item.value === Number(cellValue))?.label ?? ''
    },
    formatterParamType(row, column, cellValue) {
      return this.dict.paramType.find(item => item.value === cellValue)?.label ?? ''
    },
    formatterRequired(row, column, cellValue) {
      return this.dict.required.find(item => item.value === cellValue)?.label ?? ''
    },
  },
}

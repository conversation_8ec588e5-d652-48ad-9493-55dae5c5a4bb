<template>
  <div class="panel-tab__content">
    <el-table
        ref="multipleTable"
        :data="copyfortable"
        tooltip-effect="dark"
        style="width: 100%">
      <el-table-column
          prop="humanname"
          label="表单字段">
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作"
          width="120">
        <template slot-scope="scope">
          <el-button
              @click.native.prevent="deleteRow(scope.$index, copyfortable)"
              type="text"
              size="small">
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="element-drawer__button">
      <el-button size="mini" type="primary" icon="el-icon-plus" @click="openListenerForm()">添加督办人</el-button>
    </div>
    <!-- 监听器 编辑/创建 部分 -->
    <el-drawer :visible.sync="copyforVisible" title="督办人员" :size="`${width}px`" append-to-body destroy-on-close>
      <el-form size="mini" :model="copyforForm" label-width="96px" ref="listenerFormRef" @submit.native.prevent>
        <el-form-item label="督办人员" prop="event" :rules="{ required: true, trigger: 'blur' }">
          <el-select v-model="copyforType" @change="clearcopyfor">
            <el-option
                v-for="item in typelist"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="督办时间" prop="event" :rules="{ required: true, trigger: 'blur' }">
          <el-date-picker
              v-model="supervisetime"
              type="datetime"
              @change="changeEventTime"
              placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
<!--        <el-form-item v-if="copyforType == 'user'" label="督办人" prop="event" :rules="{ required: true, trigger: 'blur' }">-->
<!--          <el-tag v-for="item in copyforidlist" :key="item.id" size="small"-->
<!--                  style="margin-right: 5px;" type="primary"-->
<!--          >-->
<!--            {{item.humanname}}-->
<!--          </el-tag>-->
<!--          <el-button size="mini" type="primary" icon="el-icon-plus" @click="openuser()"></el-button>-->
<!--        </el-form-item>-->

<!--        <el-form-item v-if="copyforType == 'dept'" label="督办部门" prop="event" :rules="{ required: true, trigger: 'blur' }">-->
<!--          <el-select :multiple="true" v-model="deptlabellist" clearable @clear="claerable"  @remove-tag="remove">-->
<!--            <el-option :value="deptlist">-->
<!--              <el-tree-->
<!--                  :data="deptOptions"-->
<!--                  :props="deptProps"-->
<!--                  :expand-on-click-node="false"-->
<!--                  :filter-node-method="filterNode"-->
<!--                  node-key="label"-->
<!--                  ref="tree"-->
<!--                  show-checkbox-->
<!--                  @check-change="handleCheckChange"-->
<!--              />-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->

<!--        <el-form-item v-if="copyforType == 'role'" label="督办角色" prop="event" :rules="{ required: true, trigger: 'blur' }">-->
<!--          <el-select :multiple="true" v-model="copyforlist" @change="rolechange" >-->
<!--            <el-option-->
<!--                v-for="item in rolelist"-->
<!--                :key="item.id"-->
<!--                :label="item.rolename"-->
<!--                :value="item.id" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->

      </el-form>
      <el-dialog title="候选用户" class="selector-dialog" :visible.sync="userOpen" width="60%" append-to-body>
        <UserSelector @selectedUser="selectedUser" :selected-user-param="selectedUserParam"></UserSelector>
      </el-dialog>
      <div class="element-drawer__button">
        <el-button size="mini" @click="copyforVisible = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="savecopyforConfig">保 存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
import UserSelector from "@/components/selector/UserSelector";

<script>
import UserSelector from "@/components/selector/UserSelector";
import {GetOrg} from "@/api/basetable";
import {GetRoleInfo} from "@/api/settings";


export default {
  name: "ElementSupervise",
  props: {
    id: String,
    type: String,
  },
  components:{
    UserSelector
  },
  inject: {
    prefix: "prefix",
    width: "width",

  },
  data() {
    return {
      tableData: [],
      deptOptions: [],
      deptProps: {
        children: "children",
        label: "label"
      },
      selectedUserParam:{
        orgname: '',
      },
      copyforForm: {},
      userOpen: false,
      copyforVisible: false,
      copyforType: '',
      copyforlist: [],
      copyfortable: [],
      deptlist: [],
      deptidlist: [],
      copyforidlist:[],
      deptlabellist:[],
      supervisetime: '',
      rolelist: [],
      typelist:[
        {
          label: '部门领导',
          value: 'deptleader'
        },
        {
          label: '部门上级领导',
          value: 'supdeptleader'
        },

      ]
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        val && val.length && this.$nextTick(() => this.resetFormList());
      }
    }
  },
  methods: {
    resetFormList(){
      this.getDeptOptions()
      this.bpmnELement = window.bpmnInstances.bpmnElement;
      // 获取元素扩展属性 或者 创建扩展属性
      this.elExtensionElements = this.bpmnELement.businessObject.get("extensionElements") || window.bpmnInstances.moddle.create("bpmn:ExtensionElements", { values: [] });
      if(this.elExtensionElements.values){
      }else {
        this.elExtensionElements.values = []
      }
      this.otherExtensions = this.elExtensionElements.values;
      this.Data = this.elExtensionElements.values.filter(ex => ex.$type === `${this.prefix}:SuperviseItemList`)?.[0] ||
          window.bpmnInstances.moddle.create(`${this.prefix}:SuperviseItemList`,{values: []});
      this.otherExtensions.forEach((item,index)=>{
        if(item.$type == 'flowable:SuperviseItemList'){
          this.otherExtensions.splice(index,1)
        }
      })
      if(this.Data.values){
      }else{
        this.Data.values = [];
      }

      if(this.Data.values != undefined){
        this.$nextTick(()=>{
          this.Data.values.forEach(item=>{
            this.copyfortable.push.apply(this.copyfortable,JSON.parse(item.id))
          })
        })
      }


      this.updateElementExtensions()
      // this.saveFormList();
    },
    changeEventTime(e){
      this.supervisetime = e.toISOString();
    },
    saveFormList(){
      let param = window.bpmnInstances.moddle.create(`${this.prefix}:SuperviseItem`, { id: JSON.stringify(this.copyforidlist),type: this.copyforType, time: this.supervisetime });
      if(this.Data.values.length >0){
        let data = this.Data.values.some(item=>item.type==param.type)
        if(data == true){
          this.Data.values.forEach(item=>{
            if(item.type==param.type){
              let itemlist = JSON.parse(item.id);
              itemlist.forEach( itemlist =>{
                this.copyfortable.forEach( (copyfortable,index) =>{
                  if(copyfortable.id == itemlist.id){
                    this.copyfortable.splice(index,1)
                  }
                })
              })
              item.id = param.id;
            }
          })
          let arr = this.copyfortable
          this.copyfortable = [...arr,...this.copyforidlist]
        }else {
          this.Data.values.push(param)
          let arr = this.copyfortable
          this.copyfortable = [...arr,...this.copyforidlist]
        }
      }else{
        this.Data.values.push(param)
        let arr = this.copyfortable
        this.copyfortable = [...arr,...this.copyforidlist]
      }

      this.updateElementExtensions()
    },
    /**
     * 查询部门下拉树结构
     */
    getDeptOptions() {
      return new Promise((resolve, reject) => {
        if (!this.deptOptions || this.deptOptions.length <= 0) {
          GetOrg().then(response => {
            this.deptOptions = response.data.info;
            resolve()
          })
          GetRoleInfo().then(res =>{
            this.rolelist = res.data.info;
          })
        } else {
          // reject()
        }
      });
    },
    clearcopyfor(){
      this.copyforidlist = [];
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleCheckChange(data, checked, indeterminate){
      let res = this.$refs.tree.getCheckedNodes()
      let arrlabel = [];
      let arrid = [];
      let arr = [];
      res.forEach(item =>{
        arrlabel.push(item.label)
        arrid.push({
          id:item.id,
          humanname: item.label
        });
        arr.push(item)
      })
      this.deptlabellist =  arrlabel;
      this.copyforidlist = arrid;
      this.deptlist = arr;
    },
    remove(val){
      this.$refs.tree.setChecked(val, false);
    },
    claerable(){
      this.$refs.tree.setCheckedKeys([])
    },
    deleteRow(index,rows){
      let param = rows[index]
      this.Data.values.forEach(item=>{
        let id = JSON.parse(item.id)
        id.forEach( (ids,index) =>{
          if( ids.id == param.id ){
            id.splice(index,1)
            let newe = JSON.stringify(id)
            item.id = newe;
          }
        })
      })
      this.otherExtensions.forEach((item,index)=>{
        if(item.$type == 'flowable:SuperviseItemList'){
          this.otherExtensions.splice(index,1)
        }
      })
      this.updateElementExtensions()
      this.copyfortable.splice(index,1)

    },
    rolechange(){
      if(this.copyforlist.length>0){
        this.copyforidlist = [];
        this.$nextTick(()=>{
          this.copyforlist.forEach(item=>{
            this.rolelist.forEach(items=>{
              if(item == items.id){
                this.copyforidlist.push({
                  humanname: items.rolename,
                  id: items.id
                })
              }
            })
          })
        })
      }
    },
    openListenerForm(){
      this.copyforVisible = true;
    },
    openuser(){
      this.userOpen = true;
    },
    selectedUser(data){
      this.userOpen = data.userOpen;
      this.$nextTick(()=>{
        data.selectedUserDate.forEach(item=>{
          this.copyforidlist.push({
            id: item.humancode,
            humanname: item.humanname
          })
        })
      })
    },
    savecopyforConfig(){
      this.saveFormList();
      this.copyforVisible = false
    },
    updateElementExtensions() {
      const newElExtensionElements = window.bpmnInstances.moddle.create(`bpmn:ExtensionElements`, {
        values: this.otherExtensions.concat(this.Data)
      });
      // // 更新到元素上
      window.bpmnInstances.modeling.updateProperties(this.bpmnELement, {
        extensionElements: newElExtensionElements
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.selector-dialog ::v-deep {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 80%;
    overflow: hidden;
  }
  .el-card__header {
    padding: 12px 18px;
  }
  .el-dialog__body {
    padding: 10px 15px;
    max-height: calc(100% - 110px) !important;
  }
}
</style>

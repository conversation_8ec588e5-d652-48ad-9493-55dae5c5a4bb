<template>
  <div class="panel-tab__content">
    <div class="element-drawer__button">
      <el-button size="mini" type="primary" icon="el-icon-plus" @click="openFormjson()">表单字段权限</el-button>
    </div>
    <el-dialog
        title="表单字段权限配置"
        :visible.sync="dialogVisible"
        width="60%">
      <el-table
          ref="multipleTable"
          :data="tableData"
          tooltip-effect="dark"
          size="mini"
          border
          style="width: 100%">
        <el-table-column
            prop="label"
            label="表单字段"
            align="center">
        </el-table-column>
        <el-table-column
            align="center"
            label="回显内容">
          <template slot-scope="scope">
            <el-select size="mini" @change="checkBoxChange(scope)" v-model="scope.row.defaultValue" clearable placeholder="请选择">
              <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.name"
                  :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
            align="center"
            label="审批意见">
<!--          <template slot="header" slot-scope="scope">-->
<!--            <el-checkbox @change="allcheckBoxChange(alldisabled)" v-model="alldisabled">是否禁用</el-checkbox>-->
<!--          </template>-->
          <template slot-scope="scope">
            <el-checkbox @change="checkBoxChange(scope)" v-model="scope.row.reason"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
            align="center">
          <template slot="header" slot-scope="scope">
            <el-checkbox @change="allcheckBoxChange(alldisabled)" v-model="alldisabled">是否禁用</el-checkbox>
          </template>
          <template slot-scope="scope">
            <el-checkbox @change="checkBoxChange(scope)" v-model="scope.row.disabled"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
            align="center">
          <template slot="header" slot-scope="scope">
            <el-checkbox @change="allcheckBoxChangeallrequired(allrequired)" v-model="allrequired">是否必填</el-checkbox>
          </template>
          <template slot-scope="scope">
            <el-checkbox @change="checkBoxChange(scope)" v-model="scope.row.required"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
            align="center">
          <template slot="header" slot-scope="scope">
            <el-checkbox @change="allcheckBoxChangeallhidden(allhidden)" v-model="allhidden">是否隐藏</el-checkbox>
          </template>
          <template slot-scope="scope">
            <el-checkbox @change="checkBoxChange(scope)" v-model="scope.row.hidden"></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {GetBpmVariable} from "@/api/bpmVariable"


export default {
  name: "ElementForm",
  props: {
    id: String,
    type: String,
    detailForm: Object
  },
  inject: {
    prefix: "prefix",
    width: "width"
  },
  data() {
    return {
      tableData: [],
      newData: [],
      dialogVisible: false,
      options: [],
      value: '',
      alldisabled: false,
      allrequired: false,
      allhidden: false,
      // prefix: 'bpmn2'
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        val && val.length && this.$nextTick(() => this.resetFormList());
      }
    }
  },
  created() {
    GetBpmVariable().then(res=>{
      this.options = res.data.info
    })
  },
  methods: {
    openFormjson(){
      this.dialogVisible = true
    },
    tablerecursion(data){
      if(data.type == 'table'){
        data.rows.forEach(row =>{
          if( row.cols ){
            row.cols.forEach(col =>{
              if(col.widgetList.length>0){
                col.widgetList.forEach(wid =>{
                  if(wid.formItemFlag == true  && wid.type !== 'static-text' ){
                    let datas = {
                      label: wid.options.label,
                      name: wid.options.name,
                      disabled: wid.options.disabled,
                      reason: wid.options.reason,
                      required: wid.options.required,
                      hidden: wid.options.hidden,
                      defaultValue: wid.options.defaultValue
                    }
                    this.tableData.push(datas)
                  }
                  if(wid.type == 'table'){
                    this.tablerecursion(wid)
                  }
                  if(wid.type == 'grid'){
                    this.gridrecursion(wid)
                  }
                  if(wid.type == 'tab'){
                    this.tabrecursion(wid)
                  }
                  if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'sub-form' ){
                    this.cardrecursion(wid)
                  }
                })
              }
            })
          }
        })
      }
    },
    gridrecursion(data) {
      if(data.type == 'grid'){
        data.cols.forEach(cols =>{
          if(cols.widgetList.length>0){
            cols.widgetList.forEach(wid =>{
              if(wid.formItemFlag == true  && wid.type !== 'static-text' ){
                let datas = {
                  label: wid.options.label,
                  name: wid.options.name,
                  disabled: wid.options.disabled,
                  reason: wid.options.reason,
                  required: wid.options.required,
                  hidden: wid.options.hidden,
                  defaultValue: wid.options.defaultValue
                }
                this.tableData.push(datas)
              }
              if(wid.type == 'table'){
                this.tablerecursion(wid)
              }
              if(wid.type == 'grid'){
                this.gridrecursion(wid)
              }
              if(wid.type == 'tab'){
                this.tabrecursion(wid)
              }
              if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'sub-form' ){
                this.cardrecursion(wid)
              }
            })
          }
        })
      }
    },
    tabrecursion(data){
      if(data.type == 'tab'){
        data.tabs.forEach(tabs =>{
          if(tabs.widgetList.length>0){
            tabs.widgetList.forEach(wid =>{
              if(wid.formItemFlag == true  && wid.type !== 'static-text' ){
                let datas = {
                  label: wid.options.label,
                  name: wid.options.name,
                  disabled: wid.options.disabled,
                  reason: wid.options.reason,

                  required: wid.options.required,
                  hidden: wid.options.hidden,
                  defaultValue: wid.options.defaultValue
                }
                this.tableData.push(datas)
              }
              if(wid.type == 'table'){
                this.tablerecursion(wid)
              }
              if(wid.type == 'grid'){
                this.gridrecursion(wid)
              }
              if(wid.type == 'tab'){
                this.tabrecursion(wid)
              }
              if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'sub-form' ){
                this.cardrecursion(wid)
              }
            })
          }
        })
      }
    },
    cardrecursion(data){
      if(data.type == 'card' || data.type == 'inline' || data.type == 'sub-form' ){
        if(data.type == 'sub-form'){
          console.log(`data`,data)
          let datas = {
            label: data.options.label,
            name: data.options.name,
            disabled: data.options.disabled,
            reason: data.options.reason,

            required: data.options.required,
            hidden: data.options.hidden,
            defaultValue: data.options.defaultValue
          }
          this.tableData.push(datas)
        }
        if(data.widgetList.length>0){
          data.widgetList.forEach(wid =>{
            if(wid.formItemFlag == true  && wid.type !== 'static-text' ){
              let datas = {
                label: wid.options.label,
                name: wid.options.name,
                disabled: wid.options.disabled,
                reason: wid.options.reason,

                required: wid.options.required,
                hidden: wid.options.hidden,
                defaultValue: wid.options.defaultValue
              }
              this.tableData.push(datas)
            }
            if(wid.type == 'table'){
              this.tablerecursion(wid)
            }
            if(wid.type == 'grid'){
              this.gridrecursion(wid)
            }
            if(wid.type == 'tab'){
              this.tabrecursion(wid)
            }
            if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'sub-form' ){
              this.cardrecursion(wid)
            }
          })
        }

      }
    },
    resetFormList() {
      this.bpmnELement = window.bpmnInstances.bpmnElement;

      // 获取元素扩展属性 或者 创建扩展属性
      this.elExtensionElements =
          this.bpmnELement.businessObject.get("extensionElements") || window.bpmnInstances.moddle.create("bpmn:ExtensionElements", { values: [] });
      // 获取元素表单配置 或者 创建新的表单配置
      if(this.elExtensionElements.values){
      }else {
        this.elExtensionElements.values = []
      }
      this.Data =
          this.elExtensionElements.values.filter(ex => ex.$type === `${this.prefix}:FieldsPermission`)?.[0] ||
          window.bpmnInstances.moddle.create(`${this.prefix}:FieldsPermission`, { fields: [] });
      this.otherExtensions = this.elExtensionElements.values.filter(ex => ex.$type !== `${this.prefix}:FieldsPermission`);
      this.tableData = []
      this.detailForm.fields.forEach( (item,index) =>{
        if( item.formItemFlag  == true && item.type !== 'static-text' ){
          let data = {
            label: item.options.label,
            name: item.options.name,
            disabled: item.options.disabled,
            reason: item.options.reason,
            required: item.options.required,
            hidden: item.options.hidden,
            defaultValue: item.options.defaultValue
          }
          this.tableData.push(data)
        }
        if( item.type == 'table' ){
            this.tablerecursion(item)
        }
        if( item.type == 'grid' ){
            this.gridrecursion(item)
        }
        if( item.type == 'tab' ){
            this.tabrecursion(item)
        }
        if( item.type == 'card' || item.type == 'sub-form' || item.type == 'inline' ){
            this.cardrecursion(item)
        }
      })
      if(this.Data.value && this.Data.value !== []){
        this.newData = JSON.parse(this.Data.value);
        this.newData.forEach(item=>{
          this.tableData.forEach(items=>{
            if(item.name == items.name){
              items.disabled = item.disabled
              items.required = item.required
              items.hidden = item.hidden
              items.reason = item.reason
              items.defaultValue = item.defaultValue
            }
          })
        })
      }
      this.Data.value = JSON.stringify(this.tableData)
      // 更新元素扩展属性，避免后续报错
      this.updateElementExtensions();
    },
    checkBoxChange(scope){
      this.Data.value = JSON.stringify(this.tableData)
      this.updateElementExtensions();
    },
    allcheckBoxChange(alldisabled){
      if(alldisabled == true){
        this.tableData.forEach(item=>{
          item.disabled = true
        })
      }else {
        this.tableData.forEach(item=>{
          item.disabled = false
        })
      }
      this.Data.value = JSON.stringify(this.tableData)
      this.updateElementExtensions();
    },
    allcheckBoxChangeallrequired(allrequired){
      if(allrequired == true){
        this.tableData.forEach(item=>{
          item.required = true
        })
      }else {
        this.tableData.forEach(item=>{
          item.required = false
        })
      }
      this.Data.value = JSON.stringify(this.tableData)
      this.updateElementExtensions();
    },
    allcheckBoxChangeallhidden(allhidden){
      if(allhidden == true){
        this.tableData.forEach(item=>{
          item.hidden = true
        })
      }else {
        this.tableData.forEach(item=>{
          item.hidden = false
        })
      }
      this.Data.value = JSON.stringify(this.tableData)
      this.updateElementExtensions();
    },
    updateElementExtensions() {
      const newElExtensionElements = window.bpmnInstances.moddle.create(`bpmn:ExtensionElements`, {
        values: this.otherExtensions.concat(this.Data)
      });
      // // 更新到元素上
      window.bpmnInstances.modeling.updateProperties(this.bpmnELement, {
        extensionElements: newElExtensionElements
      });
    }
  }
};
</script>

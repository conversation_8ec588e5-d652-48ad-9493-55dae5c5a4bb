<template>
  <div>
    <el-row>
      <h4><b>审批人设置</b></h4>
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio label="USERS">指定用户</el-radio>
        <el-radio label="ROLES">角色</el-radio>
        <el-radio label="DEPTS">部门</el-radio>
        <el-radio label="DEPTHEAD">部门领导</el-radio>
        <el-radio label="INITIATOR">发起人</el-radio>
        <el-radio label="HEAD">发起人部门领导</el-radio>
        <el-radio label="ATTRS">发起人组织关系</el-radio>
        <el-radio label="INITATTR">组织关系</el-radio>
      </el-radio-group>
    </el-row>
    <el-row>
      <div v-if="dataType === 'USERS'">
        <el-tag v-for="(userText,index) in selectedUser.text" size="mini" closable @close="removeUser(userText,index)" :key="userText">
          {{userText}}
        </el-tag>
        <div class="element-drawer__button">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="onSelectUsers()">添加用户</el-button>
        </div>
      </div>
      <div v-if="dataType === 'ROLES'">
        <el-select v-model="roleIds" multiple size="mini" placeholder="请选择 角色" @change="changeSelectRoles">
          <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.rolename"
              :value="`ROLE${item.id}`"
              :disabled="item.status === 1">
          </el-option>
        </el-select>
      </div>
      <div v-if="dataType === 'DEPTS'">
        <tree-select
            :width="320"
            :height="400"
            size="mini"
            :data="deptTreeData"
            :defaultProps="deptProps"
            multiple
            clearable
            checkStrictly
            nodeKey="id"
            :checkedKeys="deptIds"
            @checked-change="checkedDeptChange">
        </tree-select>
      </div>
      <div v-if="dataType === 'DEPTHEAD'">
        <tree-select
            :width="320"
            :height="400"
            size="mini"
            :data="deptTreeData"
            :defaultProps="deptProps"
            multiple
            clearable
            checkStrictly
            nodeKey="id"
            :checkedKeys="deptHeadIds"
            @checked-change="checkedDeptHeadChange">
        </tree-select>
      </div>
      <div v-if="dataType === 'ATTRS'">
        <el-select v-model="attributeIds" multiple size="mini" placeholder="请选择组织关系" @change="changeSelectATTR">
          <el-option
              v-for="item in attributeOptions"
              :key="item.id"
              :label="item.attributeName"
              :value="item.id"
              :disabled="item.status === 1">
          </el-option>
        </el-select>
      </div>
      <div v-if="dataType === 'INITATTR'">
        <el-tag v-for="(userText,index) in selectedAttr.text" size="mini" closable @close="removeATTR(userText,index)" :key="userText">
          {{userText}}
        </el-tag>
        <div class="element-drawer__button">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="onSelectATTR()">添加组织关系</el-button>
        </div>
      </div>
    </el-row>
    <el-row>
      <div v-show="showMultiFlog">
        <el-divider />
        <h4><b>多实例审批方式</b></h4>
        <el-radio-group v-model="multiLoopType" @change="changeMultiLoopType">
          <el-row><el-radio label="Null">无</el-radio></el-row>
          <el-row><el-radio label="SequentialMultiInstance">会签（需所有审批人同意）</el-radio></el-row>
          <el-row><el-radio label="ParallelMultiInstance">或签（一名审批人同意即可）</el-radio></el-row>
        </el-radio-group>
      </div>
    </el-row>
    <!-- 候选组织关系弹窗 -->
    <el-dialog title="组织关系" :visible.sync="attrOpen" width="60%" append-to-body>
      <AttrSelector @selectedUser="handleselectedAttr" :selected-user-param="selectedUserParam"  @close="closeAttrSelector"></AttrSelector>
    </el-dialog>
    <!-- 候选用户弹窗 -->
    <el-dialog title="候选用户" :visible.sync="userOpen" width="60%" append-to-body destroy-on-close>
      <UserSelector @selectedUser="handleselectedUser" :selected-user-param="selectedUserParam"  @close="closeUserSelector"></UserSelector>
    </el-dialog>
  </div>

</template>

<script>
// import { listUser } from "@/api/system/user";
// import { listRole } from "@/api/system/role";
// import { treeselect } from '@/api/system/dept'
import {GetOrg} from "@/api/basetable";
import {GetERoleInfo, GetRoleItemInfo} from '@/api/settings'
import TreeSelect from "@/components/TreeSelect";
import UserSelector from "@/components/selector/UserSelector";
import AttrSelector from "@/components/selector/AttributeSelector";

import {GetBpmOrgAttribute} from "@/api/bpmOrgAttribute";


const userTaskForm = {
  dataType: '',
  assignee: '',
  candidateUsers: '',
  candidateGroups: '',
  text: '',
  AttributeHandler: '',
  // dueDate: '',
  // followUpDate: '',
  // priority: ''
}

const multiInstanceForm = {
  completionCondition: "",
  loopCardinality: "",
  extensionElements: [],
  asyncAfter: false,
  asyncBefore: false,
  exclusive: false
}

export default {
  name: "UserTask",
  props: {
    id: String,
    type: String
  },
  components: { TreeSelect,UserSelector,AttrSelector },
  data() {
    return {
      loading: false,
      dataType: 'USERS',
      selectedUser: {
        ids: [],
        text: []
      },
      selectedAttr: {
        ids: [],
        text: []
      },
      selectedUserParam:{
        orgname: '',
        attributeName: ''
      },
      userOpen: false,
      attrOpen: false,
      deptName: undefined,
      deptOptions: [],
      deptProps: {
        children: "children",
        label: "label"
      },
      deptTempOptions: [],
      userTableList: [],
      userTotal: 0,
      selectedUserDate: [],
      roleOptions: [],
      roleIds: [],
      configIds: [],
      deptTreeData: [],
      deptIds: [],
      deptHeadIds: [],
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        queryParam: {}
      },
      showMultiFlog: false,
      multiLoopType: 'Null',
      attributeIds: [],
      attributeOptions: [],
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.$nextTick(() => this.resetTaskForm());
      }
    }
  },
  beforeDestroy() {
    this.bpmnElement = null;
  },
  methods: {
    closeAttrSelector(data) {
      this.attrOpen = data.userOpen;
    },
    closeUserSelector(data) {
      this.userOpen = data.userOpen;
    },
    GetOrgAttribute(){
      let param ={
        page: 1,
        pageSize: 10,
        queryParam: {}
      }
      GetBpmOrgAttribute(param).then(res=>{
        this.attributeOptions = res.data.info.records
      })
    },
    resetTaskForm() {
      const bpmnElementObj = this.bpmnElement?.businessObject;
      if (!bpmnElementObj) {
        return;
      }
      this.clearOptionsData()
      this.dataType = bpmnElementObj['dataType'];
      if (this.dataType === 'USERS') {
        let userIdData = bpmnElementObj['assignee'] || bpmnElementObj['candidateUsers'];
        let userText = bpmnElementObj['text'] || [];
        if (userIdData && userIdData.length > 0 && userText && userText.length > 0) {
          this.selectedUser.ids = userIdData?.toString().split(',');
          this.selectedUser.text = userText?.split(',');
        }
        if (this.selectedUser.ids.length > 1) {
          this.showMultiFlog = true;
        }
      } else if (this.dataType === 'ROLES') {
        this.getRoleOptions();
        let roleIdData = bpmnElementObj['candidateGroups'] || [];
        if (roleIdData && roleIdData.length > 0) {
          this.roleIds = roleIdData.split(',')
        }
        this.showMultiFlog = true;
      } else if (this.dataType === 'DEPTS') {
        this.getDeptTreeData().then(() => {
          let deptIdData = bpmnElementObj['candidateGroups'] || [];
          if (deptIdData && deptIdData.length > 0) {
            this.deptIds = deptIdData.split(',');
          }
        });
        this.showMultiFlog = true;
      } else if (this.dataType === 'DEPTHEAD') {
        this.getDeptHeadTreeData().then((res) => {
          // let deptIdData = bpmnElementObj['candidateGroups'] || [];
          let deptHead = bpmnElementObj['deptHead'] || [];
          if (deptHead && deptHead.length > 0) {
            this.deptHeadIds = deptHead.split(',');
            // this.deptHeadIds = deptIdData.split(',');
          }
        });
        this.showMultiFlog = false;
      } else if (this.dataType === 'ATTRS') {
        this.GetOrgAttribute();
        let attributeIdData = bpmnElementObj['AttributeHandler'] || [];
        if (attributeIdData && attributeIdData.length > 0) {
          this.attributeIds = attributeIdData.split(',')
        }
        this.showMultiFlog = false;
      } else if (this.dataType === 'INITATTR') {
        let configIdData = bpmnElementObj['candidateGroups'] || [];
        // if (configIdData && configIdData.length > 0) {
        //   this.configIds = configIdData.split(',')
        // }
        let userText = bpmnElementObj['text'] || [];
        if (configIdData && configIdData.length > 0 && userText && userText.length > 0) {
          this.selectedAttr.ids = configIdData?.toString().split(',');
          this.selectedAttr.text = userText?.split('/');
        }
        this.showMultiFlog = true;
        /*let userIdData = bpmnElementObj['assignee'] || bpmnElementObj['candidateUsers'];
        let userText = bpmnElementObj['text'] || [];
        if (userIdData && userIdData.length > 0 && userText && userText.length > 0) {
          this.selectedAttr.ids = userIdData?.toString().split(',');
          this.selectedAttr.text = userText?.split(',');
        }
        if (this.selectedAttr.ids.length > 1) {
          this.showMultiFlog = true;
        }*/
      }
      this.getElementLoop(bpmnElementObj);
    },
    /**
     * 清空选项数据
     */
    clearOptionsData() {
      this.selectedUser.ids = [];
      this.selectedUser.text = [];
      this.roleIds = [];
      this.deptIds = [];
    },
    /**
     * 跟新节点数据
     */
    updateElementTask() {
      userTaskForm.dataType = this.dataType
      const taskAttr = Object.create(null);
      for (let key in userTaskForm) {
        taskAttr[key] = userTaskForm[key];
      }

      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, taskAttr);
    },
    /**
     * 查询部门下拉树结构
     */
    getDeptOptions() {
      return new Promise((resolve, reject) => {
        if (!this.deptOptions || this.deptOptions.length <= 0) {
          GetOrg().then(response => {
            this.deptTempOptions = response.data.info;
            this.deptOptions = response.data.info;
            resolve()
          })
        } else {
          reject()
        }
      });
    },
    /**
     * 查询部门下拉树结构（含部门前缀）
     */
    getDeptTreeData() {
      function refactorTree(data) {
        return data.map(node => {
          let treeData = { id: `DEPT${node.id}`, label: node.label, parentId: node.parentId, weight: node.weight };
          if (node.children && node.children.length > 0) {
            treeData.children = refactorTree(node.children);
          }
          return treeData;
        });
      }
      return new Promise((resolve, reject) => {
        if (!this.deptTreeData || this.deptTreeData.length <= 0) {
          this.getDeptOptions().then(() => {
            this.deptTreeData = refactorTree(this.deptOptions);
            resolve()
          }).catch(() => {
            reject()
          })
        } else {
          resolve()
        }
      })
    },
    getDeptHeadTreeData() {
      function refactorTree(data) {
        return data.map(node => {
          let treeData = { id: `DEPT${node.id}`, label: node.label,
            // head: node.orgHeadHumaCode && node.orgHeadHumaCode !== null && node.orgHeadHumaCode !== ''
            //     ? node.orgHeadHumaCode.replaceAll(",","&") : '',
            head: node.orgHeadHumaCode && node.orgHeadHumaCode !== null && node.orgHeadHumaCode !== ''
                ? node.orgHeadHumaCode : '',
            parentId: node.parentId, weight: node.weight };
          if (node.children && node.children.length > 0) {
            treeData.children = refactorTree(node.children);
          }
          return treeData;
        });
      }
      return new Promise((resolve, reject) => {
        if (!this.deptTreeData || this.deptTreeData.length <= 0) {
          this.getDeptOptions().then(() => {
            this.deptTreeData = refactorTree(this.deptOptions);
            resolve()
          }).catch(() => {
            reject()
          })
        } else {
          resolve()
        }
      })
    },
    /**
     * 查询部门下拉树结构
     */
    getRoleOptions() {
      if (!this.roleOptions || this.roleOptions.length <= 0) {
        GetERoleInfo().then(response => this.roleOptions = response.data.info);
      }
    },
    /** 查询用户列表 */
    getUserList() {
      GetRoleItemInfo(this.queryParams).then(response => {
        this.userTableList = response.data.info.records;
        this.userTotal = response.total;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.queryParam.organizationnames = data.id;
      this.getUserList();
    },
    // 关闭标签
    handleClose(tag) {
      this.selectedUserDate.splice(this.selectedUserDate.indexOf(tag), 1);
      this.$refs.multipleTable.toggleRowSelection(tag);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedUserDate = selection;
    },
    removeUser(item,index){
      if (this.selectedUser.text.length > 1) {
        this.selectedUser.text.splice(index,1);
        userTaskForm.candidateUsers = userTaskForm.candidateUsers.split(',');
        userTaskForm.candidateUsers.splice(index,1);
        userTaskForm.candidateUsers = userTaskForm.candidateUsers.join();
        userTaskForm.text = userTaskForm.text.split(',');
        userTaskForm.text.splice(index,1);
        userTaskForm.text = userTaskForm.text.join();
        userTaskForm.assignee = null;
        if(userTaskForm.candidateUsers.length > 1){
          this.showMultiFlog = true;
          this.updateElementTask()
        }else {
          userTaskForm.candidateUsers = userTaskForm.candidateUsers.split(',');
          userTaskForm.candidateUsers.splice(index,1);
          userTaskForm.candidateUsers = userTaskForm.candidateUsers.join();
          userTaskForm.text = userTaskForm.text.split(',');
          userTaskForm.text.splice(index,1);
          userTaskForm.text = userTaskForm.text.join();
          userTaskForm.assignee = userTaskForm.candidateUsers[0];
          userTaskForm.text = userTaskForm.text[0];
          userTaskForm.candidateUsers = null;
          this.showMultiFlog = false;
          this.multiLoopType = 'Null';
          this.changeMultiLoopType(this.multiLoopType);
          this.updateElementTask()
        }
      } else {
        this.selectedUser.text.splice(index,1);
        userTaskForm.assignee = null
        userTaskForm.candidateUsers = null;
        userTaskForm.text = null;
        this.showMultiFlog = false;
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
        this.updateElementTask()
      }

    },
    removeATTR(item,index){
      if (this.selectedAttr.text.length > 1) {
        this.selectedAttr.ids.splice(index,1);
        this.selectedAttr.text.splice(index,1);
        // userTaskForm.candidateGroups = userTaskForm.candidateGroups.split(',');
        // userTaskForm.candidateGroups.splice(index,1);
        // userTaskForm.candidateGroups = userTaskForm.candidateGroups.join();

        userTaskForm.candidateGroups = this.selectedAttr.ids;
        userTaskForm.text = this.selectedAttr.text;
        // userTaskForm.text = userTaskForm.text.split(',');
        // userTaskForm.text.splice(index,1);
        // userTaskForm.text = userTaskForm.text.join();
        userTaskForm.assignee = null;
        if(userTaskForm.candidateGroups.length > 1){
          this.showMultiFlog = true;
          this.updateElementTask()
        }else {
          // userTaskForm.candidateGroups = userTaskForm.candidateGroups.split(',');
          // userTaskForm.candidateGroups.splice(index,1);
          // userTaskForm.candidateGroups = userTaskForm.candidateGroups.join();
          // userTaskForm.text = userTaskForm.text.split(',');
          // userTaskForm.text.splice(index,1);
          // userTaskForm.text = userTaskForm.text.join();
          userTaskForm.assignee = this.selectedAttr.ids[0];
          userTaskForm.text = this.selectedAttr.text[0];
          userTaskForm.candidateGroups = null;
          this.showMultiFlog = false;
          this.multiLoopType = 'Null';
          this.changeMultiLoopType(this.multiLoopType);
          this.updateElementTask()
        }
      } else {
        this.selectedAttr.ids.splice(index,1);
        this.selectedAttr.text.splice(index,1);
        userTaskForm.assignee = null
        userTaskForm.candidateGroups = null;
        userTaskForm.text = null;
        this.showMultiFlog = false;
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
        this.updateElementTask()
      }
    },
    onSelectUsers() {
      this.selectedUserDate = []
      this.$refs.multipleTable?.clearSelection();
      this.getDeptOptions();
      this.userOpen = true;
    },
    onSelectATTR() {
      this.attrOpen = true;
    },
    handleselectedAttr(datas){
      if (!datas.selectedUserDate || datas.selectedUserDate.length <= 0) {
        this.$modal.msgError('请选择组织关系');
        return;
      }
      this.selectedAttr.text = datas.selectedUserDate.map(k => k.humanname) || [];
      userTaskForm.candidateGroups = datas.selectedUserDate.map(k => `ATTR${k.id}`).join() || null;
      userTaskForm.text = datas.selectedUserDate.map(k => k.humanname).join('/') || null;
      if (datas.selectedUserDate.length === 1) {
        let data = datas.selectedUserDate[0];
        this.showMultiFlog = false;
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
      } else {
        this.showMultiFlog = true;
      }
      this.updateElementTask()
      this.attrOpen = false;
    },
    handleselectedUser(datas){
      if (!datas.selectedUserDate || datas.selectedUserDate.length <= 0) {
        this.$modal.msgError('请选择用户');
        return;
      }
      this.selectedUser.text = datas.selectedUserDate.map(k => k.humanname) || [];
      if (datas.selectedUserDate.length === 1) {
        let data = datas.selectedUserDate[0];
        userTaskForm.assignee = data.humancode;
        userTaskForm.text = data.humanname;
        userTaskForm.candidateUsers = null;
        this.showMultiFlog = false;
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
      } else {
        userTaskForm.candidateUsers = datas.selectedUserDate.map(k => k.humancode).join() || null;
        userTaskForm.text = datas.selectedUserDate.map(k => k.humanname).join() || null;
        userTaskForm.assignee = null;
        this.showMultiFlog = true;
      }
      this.updateElementTask()
      this.userOpen = false;
    },
    // handleTaskUserComplete() {
    //   if (!this.selectedUserDate || this.selectedUserDate.length <= 0) {
    //     this.$modal.msgError('请选择用户');
    //     return;
    //   }
    //   this.selectedUser.text = this.selectedUserDate.map(k => k.humanname) || [];
    //   if (this.selectedUserDate.length === 1) {
    //     let data = this.selectedUserDate[0];
    //     userTaskForm.assignee = data.humancode;
    //     userTaskForm.text = data.humanname;
    //     userTaskForm.candidateUsers = null;
    //     this.showMultiFlog = false;
    //     this.multiLoopType = 'Null';
    //
    //     this.changeMultiLoopType(this.multiLoopType);
    //   } else {
    //     userTaskForm.candidateUsers = this.selectedUserDate.map(k => k.humancode).join() || null;
    //     userTaskForm.text = this.selectedUserDate.map(k => k.humanname).join() || null;
    //     userTaskForm.assignee = null;
    //     this.showMultiFlog = true;
    //   }
    //   this.updateElementTask()
    //   this.userOpen = false;
    // },
    changeSelectRoles(val) {
      userTaskForm.candidateGroups = val.join() || null;
      let textArr = this.roleOptions.filter(k => val.indexOf(`ROLE${k.roleId}`) >= 0);
      userTaskForm.text = textArr?.map(k => k.roleName).join() || null;
      this.updateElementTask();
    },
    changeSelectATTR(val) {
      userTaskForm.AttributeHandler = val.join() || null;
      let textArr = this.attributeOptions.filter(k => val.indexOf(k.id) >= 0);
      userTaskForm.text = textArr?.map(k => k.attributeName).join() || null;
      this.updateElementTask();
    },
    checkedDeptChange(checkedIds, checkedData) {
      if (checkedIds && checkedIds.length > 0) {
        this.deptIds = checkedIds;
      }
      if (checkedData && checkedData.length > 0) {
        userTaskForm.candidateGroups = checkedData.map(k => k.id).join() || null
        userTaskForm.text = checkedData.map(k => k.label).join() || null
        // userTaskForm.dataType = this.dataType
        this.updateElementTask();
      }
    },
    checkedDeptHeadChange(checkedIds, checkedData) {
      // if (checkedIds && checkedIds.length > 0) {
      //   this.deptHeadIds = checkedIds;
      // }
      if (checkedData && checkedData.length > 0) {
       /* let headIds = [];
        checkedData.map(data => {
          if (data.head && data.head !== '') {
            headIds.push(data.head);
          }
        });
        this.deptHeadIds = headIds;*/
        let headGroups = [];
        let deptHead = []
        checkedData.map(k =>{
          if (k.head && k.head !== '') {
            headGroups.push(k.head);
            deptHead.push(k.id);
          }
        });
        userTaskForm.deptHead = deptHead.join() || null;
        userTaskForm.candidateUsers = headGroups.join() || null;
        userTaskForm.text = checkedData.map(k => k.label).join() || null
        this.updateElementTask();
      }
    },
    changeDataType(val) {
      // 清空 userTaskForm 所有属性值
      Object.keys(userTaskForm).forEach(key => userTaskForm[key] = null);
      userTaskForm.dataType = val;
      if (val === 'USERS') {
        if (this.selectedUser && this.selectedUser.ids && this.selectedUser.ids.length > 0) {
          if (this.selectedUser.ids.length === 1) {
            userTaskForm.assignee = this.selectedUser.ids[0];
          } else {
            userTaskForm.candidateUsers = this.selectedUser.ids.join()
          }
          userTaskForm.text = this.selectedUser.text?.join() || null
        }
      } else if (val === 'ROLES') {
        this.getRoleOptions();
        if (this.roleIds && this.roleIds.length > 0) {
          userTaskForm.candidateGroups = this.roleIds.join() || null;
          let textArr = this.roleOptions.filter(k => this.roleIds.indexOf(`ROLE${k.roleId}`) >= 0);
          userTaskForm.text = textArr?.map(k => k.roleName).join() || null;
        }
      } else if (val === 'DEPTS') {
        this.getDeptTreeData();
        if (this.deptIds && this.deptIds.length > 0) {
          userTaskForm.candidateGroups = this.deptIds.join() || null;
          let textArr = []
          let treeStarkData = JSON.parse(JSON.stringify(this.deptTreeData));
          this.deptIds.forEach(id => {
            let stark = []
            stark = stark.concat(treeStarkData);
            while (stark.length) {
              let temp = stark.shift();
              if (temp.children) {
                stark = temp.children.concat(stark);
              }
              if (id === temp.id) {
                textArr.push(temp);
              }
            }
          })
          userTaskForm.text = textArr?.map(k => k.label).join() || null;
        }
      }else if (val === 'DEPTHEAD') {
        this.getDeptHeadTreeData();
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
        if (this.deptHeadIds && this.deptHeadIds.length > 0) {
          userTaskForm.deptHead = this.deptHeadIds.join() || null;
          let textArr = []
          let treeStarkData = JSON.parse(JSON.stringify(this.deptTreeData));
          this.deptHeadIds.forEach(id => {
            let stark = []
            stark = stark.concat(treeStarkData);
            while (stark.length) {
              let temp = stark.shift();
              if (temp.children) {
                stark = temp.children.concat(stark);
              }
              if (id === temp.id) {
                textArr.push(temp);
              }
            }
          })
          userTaskForm.candidateUsers = textArr?.map(k => k.head).join() || null;
          userTaskForm.text = textArr?.map(k => k.label).join() || null;
        }
      } else if (val === 'INITIATOR') {
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
        userTaskForm.assignee = "${initiator}";
        userTaskForm.text = "流程发起人";
      } else if (val === 'HEAD') {
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
        userTaskForm.candidateUsers = "${orgHeadHandler.getUserIds(execution)}";
        userTaskForm.text = "部门领导";
      } else if (this.dataType === 'ATTRS') {
        this.GetOrgAttribute()
        this.multiLoopType = 'Null';
        this.changeMultiLoopType(this.multiLoopType);
        userTaskForm.candidateUsers = "${startUserOrgAttributeHandler.getUserIds(execution)}";
        // userTaskForm.text = "发起人组织关系";
        if (this.attributeIds && this.attributeIds.length > 0) {
          userTaskForm.AttributeHandler = this.attributeIds.join() || null;
          let textArr = this.attributeOptions.filter(k => this.attributeIds.indexOf(k.id) >= 0);
          userTaskForm.text = textArr?.map(k => k.attributeName).join() || null;
        }
      }
      this.updateElementTask();
      if ( val === 'ROLES' || val === 'DEPTS' || (val === 'USERS' && this.selectedUser.ids.length > 1)) {
        this.showMultiFlog = true;
      } else {
        this.showMultiFlog = false;
      }
      if(val == 'INITIATOR'){
        this.multiLoopType = '';
      }
      this.changeMultiLoopType(this.multiLoopType);
    },
    getElementLoop(businessObject) {
      if (!businessObject.loopCharacteristics) {
        this.multiLoopType = "Null";
        return;
      }
      if (businessObject.loopCharacteristics.completionCondition.body === "${nrOfCompletedInstances >= nrOfInstances}") {
        this.multiLoopType = "SequentialMultiInstance";
      } else {
        this.multiLoopType = "ParallelMultiInstance";
      }
    },
    changeMultiLoopType(type) {
      // 取消多实例配置
      if (type === "Null") {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, { loopCharacteristics: null,assignee: null });
        return;
      }
      // 完成条件
      let completionCondition = null;
      // 会签
      if (type === "SequentialMultiInstance") {
        this.multiLoopInstance = window.bpmnInstances.moddle.create("bpmn:MultiInstanceLoopCharacteristics", { isSequential: false });
        completionCondition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body: '${nrOfCompletedInstances >= nrOfInstances}' });
        // 更新多实例配置
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          loopCharacteristics: this.multiLoopInstance,
          assignee: '${assignee}'
        });
        // 更新模块属性信息
        window.bpmnInstances.modeling.updateModdleProperties(this.bpmnElement, this.multiLoopInstance, {
          collection: '${multiInstanceHandler.getUserIds(execution)}',
          elementVariable: 'assignee',
          completionCondition
        });
      }
      // 或签
      if (type === "ParallelMultiInstance") {
        this.multiLoopInstance = window.bpmnInstances.moddle.create("bpmn:MultiInstanceLoopCharacteristics");
        completionCondition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body: '${nrOfCompletedInstances > 0}' });
        // 更新多实例配置
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          loopCharacteristics: this.multiLoopInstance,
          assignee: '${assignee}'
        });
        // 更新模块属性信息
        window.bpmnInstances.modeling.updateModdleProperties(this.bpmnElement, this.multiLoopInstance, {
          collection: '${multiInstanceHandler.getUserIds(execution)}',
          elementVariable: 'assignee',
          completionCondition
        });
      }
      // // 更新多实例配置
      // window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
      //   loopCharacteristics: this.multiLoopInstance,
      //   assignee: '${assignee}'
      // });
      // // 更新模块属性信息
      // window.bpmnInstances.modeling.updateModdleProperties(this.bpmnElement, this.multiLoopInstance, {
      //   collection: '${multiInstanceHandler.getUserIds(execution)}',
      //   elementVariable: 'assignee',
      //   completionCondition
      // });
    },
  }
};
</script>

<style scoped lang="scss">
.el-row .el-radio-group {
  margin-bottom: 15px;
  .el-radio {
    line-height: 28px;
  }
}
.el-tag {
  margin-bottom: 10px;
  + .el-tag {
    margin-left: 10px;
  }
}

</style>

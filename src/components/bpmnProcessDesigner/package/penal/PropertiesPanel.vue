<!--<template>-->
<!--  <div class="process-panel__container" :style="{ width: `${this.width}px` }">-->
<!--    <el-collapse v-model="activeTab">-->
<!--      <el-collapse-item name="base">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-info"></i>常规</div>-->
<!--        <element-base-info :id-edit-disabled="idEditDisabled" :business-object="elementBusinessObject" :type="elementType"-->
<!--                           :model="model" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="condition" v-if="elementType === 'Process'" key="message">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-comment"></i>消息与信号</div>-->
<!--        <signal-and-massage />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="condition" v-if="conditionFormVisible" key="condition">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-promotion"></i>流转条件</div>-->
<!--        <flow-condition :detail-form="detailForm" :business-object="elementBusinessObject" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="condition" v-if="elementType === 'UserTask'" key="form">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-order"></i>表单配置</div>-->
<!--        <element-form :detail-form="detailForm" :id="elementId" :type="elementType" />-->
<!--&lt;!&ndash;        友情提示：使用 <router-link target="_blank" :to="{path:'/bpm/manager/form'}"><el-link type="danger">流程表单</el-link> </router-link>&ndash;&gt;-->
<!--&lt;!&ndash;        替代，提供更好的表单设计功能&ndash;&gt;-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="task" v-if="elementType === 'UserTask'" key="task">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-claim"></i>任务</div>-->
<!--        <element-task :id="elementId" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="button" v-if="elementType === 'UserTask'" key="button">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-order"></i>按钮配置</div>-->
<!--        <element-button :table-data="tableData" :model="model" :id="elementId" :type="elementType"/>-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="copyfor" v-if="elementType === 'UserTask'" key="copyfor">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-order"></i>抄送配置</div>-->
<!--        <element-copyfor  :id="elementId" :type="elementType"></element-copyfor>-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="supervise" v-if="elementType === 'UserTask'" key="supervise">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-order"></i>督办配置</div>-->
<!--        <element-supervise  :id="elementId" :type="elementType"></element-supervise>-->
<!--      </el-collapse-item>-->
<!--&lt;!&ndash;      <el-collapse-item name="reject" v-if="elementType === 'UserTask'" key="reject">&ndash;&gt;-->
<!--&lt;!&ndash;        <div slot="title" class="panel-tab__title"><i class="el-icon-s-order"></i>驳回配置</div>&ndash;&gt;-->
<!--&lt;!&ndash;        <element-reject  :id="elementId" :type="elementType" :bpmnModeler="bpmnModeler"></element-reject>&ndash;&gt;-->
<!--&lt;!&ndash;      </el-collapse-item>&ndash;&gt;-->
<!--      <el-collapse-item name="Intermediateevent" v-if="elementType.indexOf('Event') !== -1" key="Intermediateevent">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-help"></i>{{ elementType == 'BoundaryEvent' ? '边界事件' : '时间设置' }}</div>-->
<!--        <boundary-element :business-object="elementBusinessObject" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="multiInstance" v-if="elementType.indexOf('Task') !== -1" key="multiInstance">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-help"></i>多实例</div>-->
<!--        <element-multi-instance :business-object="elementBusinessObject" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="listeners" key="listeners">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-message-solid"></i>执行监听器</div>-->
<!--        <element-listeners :id="elementId" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="taskListeners" v-if="elementType === 'UserTask'" key="taskListeners">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-message-solid"></i>任务监听器</div>-->
<!--        <user-task-listeners :id="elementId" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="extensions" key="extensions">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-circle-plus"></i>扩展属性</div>-->
<!--        <element-properties :id="elementId" :type="elementType" />-->
<!--      </el-collapse-item>-->
<!--      <el-collapse-item name="other" key="other">-->
<!--        <div slot="title" class="panel-tab__title"><i class="el-icon-s-promotion"></i>其他</div>-->
<!--        <element-other-config :id="elementId" />-->
<!--      </el-collapse-item>-->
<!--    </el-collapse>-->
<!--  </div>-->
<!--</template>-->
<!--<script>-->
<!--import ElementBaseInfo from "./base/ElementBaseInfo";-->
<!--import ElementOtherConfig from "./other/ElementOtherConfig";-->
<!--import ElementTask from "./task/ElementTask";-->
<!--import ElementButton from "./button/ElementButton";-->
<!--import ElementMultiInstance from "./multi-instance/ElementMultiInstance";-->
<!--import BoundaryElement from "./boundary/BoundaryElement";-->
<!--import FlowCondition from "./flow-condition/FlowCondition";-->
<!--import SignalAndMassage from "./signal-message/SignalAndMessage";-->
<!--import ElementListeners from "./listeners/ElementListeners";-->
<!--import ElementProperties from "./properties/ElementProperties";-->
<!--import ElementForm from "./form/ElementForm";-->
<!--import UserTaskListeners from "./listeners/UserTaskListeners";-->
<!--import ElementCopyfor from "./copyfor/element-copyfor";-->
<!--import ElementSupervise from "./supervise/element-supervise";-->
<!--import ElementReject from "./reject/element-reject";-->
<!--import {bpmTaskButton} from "@/api/bpmTaskButton";-->
<!--import {bpmGet} from "@/api/bpm";-->
<!--import {decodeFields} from "@/util/formGenerator";-->

<!--/**-->
<!-- * 侧边栏-->
<!-- * <AUTHOR>
<!-- * @Home https://github.com/miyuesc-->
<!-- * @Date 2021年3月31日18:57:51-->
<!-- */-->
<!--export default {-->
<!--  name: "MyPropertiesPanel",-->
<!--  components: {-->
<!--    UserTaskListeners,-->
<!--    ElementForm,-->
<!--    ElementProperties,-->
<!--    ElementListeners,-->
<!--    SignalAndMassage,-->
<!--    FlowCondition,-->
<!--    ElementMultiInstance,-->
<!--    ElementTask,-->
<!--    ElementButton,-->
<!--    ElementOtherConfig,-->
<!--    ElementBaseInfo,-->
<!--    ElementCopyfor,-->
<!--    ElementSupervise,-->
<!--    ElementReject,-->
<!--    BoundaryElement-->
<!--  },-->
<!--  componentName: "MyPropertiesPanel",-->
<!--  props: {-->
<!--    bpmnModeler: Object,-->
<!--    prefix: {-->
<!--      type: String,-->
<!--      default: "camunda"-->
<!--    },-->
<!--    width: {-->
<!--      type: Number,-->
<!--      default: 480-->
<!--    },-->
<!--    idEditDisabled: {-->
<!--      type: Boolean,-->
<!--      default: false-->
<!--    },-->
<!--    model: Object, // 流程模型的数据-->
<!--  },-->
<!--  provide() {-->
<!--    return {-->
<!--      prefix: this.prefix,-->
<!--      width: this.width-->
<!--    };-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      activeTab: "base",-->
<!--      elementId: "",-->
<!--      elementType: "",-->
<!--      detailForm: {},-->
<!--      tableData: [],-->
<!--      elementBusinessObject: {}, // 元素 businessObject 镜像，提供给需要做判断的组件使用-->
<!--      conditionFormVisible: false, // 流转条件设置-->
<!--      formVisible: false // 表单配置-->
<!--    };-->
<!--  },-->
<!--  watch: {-->
<!--    elementId: {-->
<!--      handler() {-->
<!--        this.activeTab = "base";-->
<!--        console.log(`elementType`,this.elementType)-->
<!--      }-->
<!--    },-->
<!--    model: {-->
<!--      handler() {-->
<!--        bpmTaskButton().then(res =>{-->
<!--          this.tableData = res.data.info;-->
<!--        })-->
<!--        bpmGet({-->
<!--          id: this.model.formId-->
<!--        }).then(response => {-->
<!--          const data = response.data.info-->
<!--          this.detailForm = {-->
<!--            ...JSON.parse(data.conf),-->
<!--            fields: decodeFields(data.fields)-->
<!--          }-->
<!--        })-->
<!--      }-->
<!--    }-->
<!--  },-->
<!--  created() {-->
<!--    this.initModels();-->
<!--  },-->
<!--  methods: {-->
<!--    initModels() {-->
<!--      // 初始化 modeler 以及其他 moddle-->
<!--      if (!this.bpmnModeler) {-->
<!--        // 避免加载时 流程图 并未加载完成-->
<!--        this.timer = setTimeout(() => this.initModels(), 10);-->
<!--        return;-->
<!--      }-->
<!--      if (this.timer) clearTimeout(this.timer);-->
<!--      window.bpmnInstances = {-->
<!--        modeler: this.bpmnModeler,-->
<!--        modeling: this.bpmnModeler.get("modeling"),-->
<!--        moddle: this.bpmnModeler.get("moddle"),-->
<!--        eventBus: this.bpmnModeler.get("eventBus"),-->
<!--        bpmnFactory: this.bpmnModeler.get("bpmnFactory"),-->
<!--        elementFactory: this.bpmnModeler.get("elementFactory"),-->
<!--        elementRegistry: this.bpmnModeler.get("elementRegistry"),-->
<!--        replace: this.bpmnModeler.get("replace"),-->
<!--        selection: this.bpmnModeler.get("selection")-->
<!--      };-->
<!--      this.getActiveElement();-->
<!--    },-->
<!--    getActiveElement() {-->
<!--      // 初始第一个选中元素 bpmn:Process-->
<!--      this.initFormOnChanged(null);-->
<!--      this.bpmnModeler.on("import.done", e => {-->
<!--        this.initFormOnChanged(null);-->
<!--      });-->
<!--      // 监听选择事件，修改当前激活的元素以及表单-->
<!--      this.bpmnModeler.on("selection.changed", ({ newSelection }) => {-->
<!--        this.initFormOnChanged(newSelection[0] || null);-->
<!--      });-->
<!--      this.bpmnModeler.on("element.changed", ({ element }) => {-->
<!--        // 保证 修改 "默认流转路径" 类似需要修改多个元素的事件发生的时候，更新表单的元素与原选中元素不一致。-->
<!--        if (element && element.id === this.elementId) {-->
<!--          this.initFormOnChanged(element);-->
<!--        }-->
<!--      });-->
<!--    },-->
<!--    // 初始化数据-->
<!--    initFormOnChanged(element) {-->
<!--      let activatedElement = element;-->
<!--      if (!activatedElement) {-->
<!--        activatedElement =-->
<!--          window.bpmnInstances.elementRegistry.find(el => el.type === "bpmn:Process") ??-->
<!--          window.bpmnInstances.elementRegistry.find(el => el.type === "bpmn:Collaboration");-->
<!--      }-->
<!--      if (!activatedElement) return;-->
<!--      // console.log(`-->
<!--      //         &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->
<!--      // select element changed:-->
<!--      //           id:  ${activatedElement.id}-->
<!--      //         type:  ${activatedElement.businessObject.$type}-->
<!--      //         &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->
<!--      //         `);-->
<!--      // console.log("businessObject: ", activatedElement.businessObject);-->
<!--      window.bpmnInstances.bpmnElement = activatedElement;-->
<!--      this.bpmnElement = activatedElement;-->
<!--      this.elementId = activatedElement.id;-->
<!--      this.elementType = activatedElement.type.split(":")[1] || "";-->
<!--      this.elementBusinessObject = JSON.parse(JSON.stringify(activatedElement.businessObject));-->
<!--      this.conditionFormVisible = !!(-->
<!--        this.elementType === "SequenceFlow" &&-->
<!--        activatedElement.source &&-->
<!--        activatedElement.source.type.indexOf("StartEvent") === -1-->
<!--      );-->
<!--      this.formVisible = this.elementType === "UserTask" || this.elementType === "StartEvent";-->
<!--    },-->
<!--    beforeDestroy() {-->
<!--      window.bpmnInstances = null;-->
<!--    }-->
<!--  }-->
<!--};-->
<!--</script>-->

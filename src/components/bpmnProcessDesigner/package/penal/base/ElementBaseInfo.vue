<template>
  <div class="panel-tab__content">
    <el-form size="mini" label-width="90px" :model="model" :rules="rules" @submit.native.prevent>
      <div v-if="elementBaseInfo.$type === 'bpmn:Process'"> <!-- 如果是 Process 信息的时候，使用自定义表单 -->
        <el-form-item label="流程标识" prop="key">
          <el-input v-model="model.key" placeholder="请输入流标标识"
                    :disabled="model.id !== undefined && model.id.length > 0" @change="handleKeyUpdate" />
        </el-form-item>
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="model.name" placeholder="请输入流程名称" clearable @change="handleNameUpdate" />
        </el-form-item>
      </div>
      <div v-else>
        <el-form-item label="ID">
          <el-input v-model="elementBaseInfo.id" clearable @change="updateBaseInfo('id')"/>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="elementBaseInfo.name" clearable @change="updateBaseInfo('name')" />
        </el-form-item>
<!--        <el-form-item label="到期时间">-->
<!--          <el-date-picker-->
<!--              v-model="elementBaseInfo.expirationtime"-->
<!--              type="datetime"-->
<!--              @change="changeEventTime"-->
<!--              placeholder="选择日期时间">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
        <el-form-item label="审批意见是否必填" label-width="150px">
          <el-switch
              v-model="isopinion"
              @change="changeopinion"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="允许手动选择下一节点办理人" label-width="150px">
          <el-switch
              v-model="changeuser"
              @change="changeoUser"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="是否允许上节点选择办理人" label-width="150px">
          <el-switch
              v-model="ischangeuser"
              @change="ischangeoUser"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="是否可选更多人员" label-width="150px">
          <el-switch
              v-model="moreuser"
              @change="changeoMore"
          >
          </el-switch>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>
<script>

export default {
  name: "ElementBaseInfo",
  props: {
    businessObject: Object,
    model: Object, // 流程模型的数据
  },
  data() {
    return {
      elementBaseInfo: {
      },
      isopinion: true,
      ischangeuser: true,
      changeuser: false,
      moreuser: false,
      extendlist: '',
      extendData: [
        {}
      ],
      // 流程表单的下拉框的数据
      forms: [],
      // 流程模型的校验
      rules: {
        key: [{ required: true, message: "流程标识不能为空", trigger: "blur" }],
        name: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
      },
    };
  },
  watch: {
    businessObject: {
      immediate: false,
      handler: function(val) {
        if (val) {
          this.$nextTick(() => this.resetBaseInfo());
        }
        setTimeout(() => {
          if(this.model.key !== val.id && val.$type == 'bpmn:Process'){
            window.bpmnInstances.modeling.updateProperties(window?.bpmnInstances?.bpmnElement, {
              id: this.model.key,
              name: this.model.name
            });
          }
        }, 1000)
      }
    },
    'extendDatalist':{
      handler(val){
        if(val < 4){
          console.log(this.extendData)
          window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
            extendData: JSON.stringify(this.extendData)
          });
        }



      }
    }
    // model: {
    //   // immediate: false,
    //   handler: function (val) {
    //
    //     // this.handleKeyUpdate(val)
    //   }
    // }
  },
  computed:{
    extendDatalist() {
      return this.extendlist
    }
  },
  created() {
    // 针对上传的 bpmn 流程图时，需要延迟 1 秒的时间，保证 key 和 name 的更新
    setTimeout(() => {
      this.handleKeyUpdate(this.model.key)
      this.handleNameUpdate(this.model.name)
      this.candidateGroupsUpdate(this.model)
    }, 1000)
  },
  methods: {
    resetBaseInfo() {
      this.bpmnElement = window?.bpmnInstances?.bpmnElement;
      this.elementBaseInfo = JSON.parse(JSON.stringify(this.bpmnElement.businessObject));
      if(this.elementBaseInfo.extendData && this.elementBaseInfo.$type === "bpmn:UserTask"){
        this.extendData = JSON.parse(this.elementBaseInfo.extendData)
        this.extendlist = Object.keys(this.extendData[0]).length
        if(this.extendData.length>0){
          this.isopinion= true
          this.ischangeuser= true
          this.changeuser= false
          this.moreuser= false
          this.isopinion = this.extendData[0].isopinion !== undefined && this.extendData[0].isopinion !== null ? this.extendData[0].isopinion : this.isopinion
          this.ischangeuser = this.extendData[0].ischangeuser !== undefined && this.extendData[0].ischangeuser !== null ? this.extendData[0].ischangeuser : this.ischangeuser
          this.changeuser = this.extendData[0].changeuser !== undefined && this.extendData[0].changeuser !== null ? this.extendData[0].changeuser : this.changeuser
          this.moreuser = this.extendData[0].moreuser !== undefined && this.extendData[0].moreuser !== null ? this.extendData[0].moreuser : this.moreuser
          this.extendData[0].isopinion = this.isopinion;
          this.extendData[0].ischangeuser = this.ischangeuser;
          this.extendData[0].changeuser = this.changeuser;
          this.extendData[0].moreuser = this.moreuser;
        }
      }else {
        if(this.elementBaseInfo.$type === "bpmn:UserTask"){
          console.log(111)
          this.isopinion = true
          this.ischangeuser = true
          this.changeuser = false
          this.moreuser = false
          this.extendData[0].isopinion = this.isopinion;
          this.extendData[0].ischangeuser = this.ischangeuser;
          this.extendData[0].changeuser = this.changeuser;
          this.extendData[0].moreuser = this.moreuser;
          window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
            extendData: JSON.stringify(this.extendData)
          });
        }
      }
    },
    // candidateStarterUsers 和 candidateStarterGroups 的储存
    candidateGroupsUpdate(value) {
      let candidateGroups = value.orgid.concat(value.roleid)
      if(value.orgid.length == 0 && value.roleid.length == 0) {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {candidateStarterUsers: "all"});
      } else {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {candidateStarterUsers: "groups"});
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {candidateStarterGroups: candidateGroups.toString()});
    },
    //到期时间
    changeEventTime(e){
      this.elementBaseInfo.expirationtime = e.toISOString();
      window.bpmnInstances.modeling.updateProperties(window.bpmnInstances.bpmnElement, {
        expirationtime: this.elementBaseInfo.expirationtime
      });
    },
    changeopinion(val){
      this.extendData[0].isopinion = val
      this.extendData[0].changeuser = this.changeuser
      this.extendData[0].ischangeuser = this.ischangeuser
      this.extendData[0].moreuser = this.moreuser
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        extendData: JSON.stringify(this.extendData)
      });
    },
    changeoUser(val){
      this.extendData[0].changeuser = val
      this.extendData[0].isopinion = this.isopinion
      this.extendData[0].ischangeuser = this.ischangeuser
      this.extendData[0].ischangeuser = this.ischangeuser

      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        extendData: JSON.stringify(this.extendData)
      });
    },
    ischangeoUser(val){
      this.extendData[0].ischangeuser = val
      this.extendData[0].changeuser = this.changeuser
      this.extendData[0].isopinion = this.isopinion
      this.extendData[0].moreuser = this.moreuser
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        extendData: JSON.stringify(this.extendData)
      });
    },
    changeoMore(val){
      this.extendData[0].moreuser = val
      this.extendData[0].changeuser = this.changeuser
      this.extendData[0].isopinion = this.isopinion
      this.extendData[0].ischangeuser = this.ischangeuser
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        extendData: JSON.stringify(this.extendData)
      });
    },
    handleKeyUpdate(value) {
      // 校验 value 的值，只有 XML NCName 通过的情况下，才进行赋值。否则，会导致流程图报错，无法绘制的问题
      if (!value) {
        return;
      }
      if (!value.match(/[a-zA-Z_][\-_.0-9_a-zA-Z$]*/)) {
        console.log('key 不满足 XML NCName 规则，所以不进行赋值');
        return;
      }
      console.log('key 满足 XML NCName 规则，所以进行赋值');

      // 在 BPMN 的 XML 中，流程标识 key，其实对应的是 id 节点
      this.elementBaseInfo['id'] = value;
      this.updateBaseInfo('id');
    },
    handleNameUpdate(value) {
      if (!value) {
        return
      }
      this.elementBaseInfo['name'] = value;
      this.updateBaseInfo('name');
    },
    handleDescriptionUpdate(value) {
      // this.elementBaseInfo['documentation'] = value;
      // this.updateBaseInfo('documentation');
    },
    updateBaseInfo(key) {
      // 触发 elementBaseInfo 对应的字段
      const attrObj = Object.create(null);
      attrObj[key] = this.elementBaseInfo[key];
      if (key === "id") {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          id: this.elementBaseInfo[key],
          di: { id: `${this.elementBaseInfo[key]}_di` }
        });
      } else {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, attrObj);
      }
    }
  },
  beforeDestroy() {
    this.bpmnElement = null;
  }
};
</script>

<template>
  <div class="panel-tab__content">
    <el-table
        ref="multipleTable"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange">
      <el-table-column
          type="selection"
          width="55">
      </el-table-column>
      <el-table-column
          prop="name">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "ElementButton",
  props: {
    id: String,
    type: String,
    tableData: Array,
    model: Object,
  },
  inject: {
    prefix: "prefix",
    width: "width"
  },
  data() {
    return {
      multipleSelection: '',
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        val && val.length && this.$nextTick(() => this.resetFormList());
      }
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = JSON.stringify(val);
      this.updateElementExtensions();
    },
    resetFormList() {
      this.bpmnELement = window.bpmnInstances.bpmnElement;
      //获取buttons 及 回显
      const buttonElement = window?.bpmnInstances?.bpmnElement;
      const buttons = JSON.parse(JSON.stringify(buttonElement.businessObject));
      this.$refs.multipleTable.clearSelection();
      if(buttons.buttons !== 'inherit' && buttons.buttons){
        this.multipleSelection = buttons.buttons;
        let Selectionlist = JSON.parse(this.multipleSelection)
        this.tableData.forEach(row => {
          Selectionlist.forEach(item => {
            if(row.id == item.id) {
              this.$refs.multipleTable.toggleRowSelection(row);
            }
          });
        })
      } else {
        let Selectionlist = this.model.functionbtn;
        let functionBtn = [];
        this.tableData.forEach(row => {
          Selectionlist.forEach(item => {
            if(row.id == item) {
              this.$refs.multipleTable.toggleRowSelection(row);
              functionBtn.push(row)
            }
          });
        })
        this.multipleSelection = JSON.stringify(functionBtn);
      }
      // 更新元素扩展属性，避免后续报错
      this.updateElementExtensions();
    },
    updateElementExtensions() {
      window.bpmnInstances.modeling.updateProperties(this.bpmnELement, {
        buttons: this.multipleSelection
      });
    }
  }
};
</script>

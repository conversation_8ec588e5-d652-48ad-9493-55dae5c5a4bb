<template>
  <div class="panel-tab__content">
    <el-form :model="flowConditionForm" label-width="90px" size="mini" @submit.native.prevent>
      <el-form-item label="流转类型">
        <el-select v-model="flowConditionForm.type" @change="updateFlowType">
          <el-option label="普通流转路径" value="normal" />
          <el-option label="默认流转路径" value="default" />
          <el-option label="条件流转路径" value="condition" />
        </el-select>
      </el-form-item>
      <el-form-item label="条件格式" v-if="flowConditionForm.type === 'condition'" key="condition">
        <el-select v-model="flowConditionForm.conditionType">
          <el-option label="表达式" value="expression" />
          <el-option label="脚本" value="script" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择组件" v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'expression'" key="express">
        <el-button type="primary" @click="dialogVisible = true">获取字段</el-button>
        <el-dialog
            title="表单字段"
            :visible.sync="dialogVisible"
            width="30%"
            :before-close="handleClose">
          <el-button v-for="item in formlist" @click="copy(item.name)" >{{item.label}}</el-button>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
          </span>
        </el-dialog>
      </el-form-item>
      <el-form-item label="表达式" v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'expression'" key="express">
        <el-tooltip class="item" placement="top" effect="light">
          <div slot="content">${myVar == "00000001"} ${myVar >=5} ${myVar < 5}<br/>
            myVar对应的是表单唯一标识，比如请假天数对应的唯一标识为days，则可根据请假天数控制流程${days>=2}、${days<2}<br/>
            根据用户的操作（通过、驳回（回退、不通过）【演示环境中写的按钮名称】），使用流转条件控制流程走向时使用的固定变量：failCount(不通过的人数)、passCount(通过的人数)、outcome;<br/>
            ${failCount==0} ${passCount>0} ${outcome=='驳回'}
          </div>
          <el-input type="textarea" v-model="flowConditionForm.body" clearable @change="updateFlowCondition" />
        </el-tooltip>
<!--        <el-input type="textarea" v-model="flowConditionForm.body" clearable @change="updateFlowCondition" />-->
      </el-form-item>
      <template v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'script'">
        <el-form-item label="脚本语言" key="language">
          <el-input v-model="flowConditionForm.language" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item label="脚本类型" key="scriptType">
          <el-select v-model="flowConditionForm.scriptType">
            <el-option label="内联脚本" value="inlineScript" />
            <el-option label="外部脚本" value="externalScript" />
          </el-select>
        </el-form-item>
        <el-form-item label="脚本" v-if="flowConditionForm.scriptType === 'inlineScript'" key="body">
          <el-input v-model="flowConditionForm.body" type="textarea" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item label="资源地址" v-if="flowConditionForm.scriptType === 'externalScript'" key="resource">
          <el-input v-model="flowConditionForm.resource" clearable @change="updateFlowCondition" />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>

export default {
  name: "FlowCondition",
  props: {
    businessObject: Object,
    type: String,
    detailForm: Object
  },
  data() {
    return {
      flowConditionForm: {},
      formlist: [],
      dialogVisible: false,
      copylist: [],
      contenttext: '${input47568 == "00000001"}\n' +
          '${input47568 >=5}\n' +
          '${input47568 < 5}\n' +
          'input47568对应的是表单唯一标识，比如请假天数对应的唯一标识为days35234，则可根据请假天数控制流程${days35234>=2}、${days35234<2}\n' +
          '根据用户的操作（通过、驳回（回退、不通过）【演示环境中写的按钮名称】），使用流转条件控制流程走向时使用的固定变量：failCount(不通过的人数)、passCount(通过的人数)、outcome;\n' +
          '${failCount==0} \n' +
          '${passCount>0}\n' +
          '${outcome==\'驳回\'}'
    };
  },
  watch: {
    businessObject: {
      immediate: true,
      handler() {
        this.$nextTick(() => this.resetFlowCondition());
      }
    }
  },
  methods: {
    copy(val){
      var cInput = document.createElement("input");
      cInput.value = val;
      document.body.appendChild(cInput);
      cInput.select();
      document.execCommand("copy");
      this.$message({type: "success",message: "复制成功"});
      document.body.removeChild(cInput)
      console.log(`conditionIds`,this.bpmnElement.parent.businessObject.conditionIds)
      this.copylist.push(val)
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement.parent, {
        conditionIds: JSON.stringify(this.copylist)
      });
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
          .then(_ => {
            done();
          })
          .catch(_ => {});
    },
    tablerecursion(data){
      if(data.type == 'table'){
        data.rows.forEach(row =>{
          if( row.cols ){
            row.cols.forEach(col =>{
              if(col.widgetList.length>0){
                col.widgetList.forEach(wid =>{
                  if(wid.formItemFlag == true ){
                    let datas = {
                      label: wid.options.label,
                      name: wid.options.name,
                    }
                    this.formlist.push(datas)
                  }
                  if(wid.type == 'table'){
                    this.tablerecursion(wid)
                  }
                  if(wid.type == 'grid'){
                    this.gridrecursion(wid)
                  }
                  if(wid.type == 'tab'){
                    this.tabrecursion(wid)
                  }
                  if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'dynamic' ){
                    this.cardrecursion(wid)
                  }
                })
              }
            })
          }
        })
      }
    },
    gridrecursion(data) {
      if(data.type == 'grid'){
        data.cols.forEach(cols =>{
          if(cols.widgetList.length>0){
            cols.widgetList.forEach(wid =>{
              if(wid.formItemFlag == true ){
                let datas = {
                  label: wid.options.label,
                  name: wid.options.name,
                }
                this.formlist.push(datas)
              }
              if(wid.type == 'table'){
                this.tablerecursion(wid)
              }
              if(wid.type == 'grid'){
                this.gridrecursion(wid)
              }
              if(wid.type == 'tab'){
                this.tabrecursion(wid)
              }
              if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'dynamic' ){
                this.cardrecursion(wid)
              }
            })
          }
        })
      }
    },
    tabrecursion(data){
      if(data.type == 'tab'){
        data.tabs.forEach(tabs =>{
          if(tabs.widgetList.length>0){
            tabs.widgetList.forEach(wid =>{
              if(wid.formItemFlag == true ){
                let datas = {
                  label: wid.options.label,
                  name: wid.options.name,
                }
                this.formlist.push(datas)
              }
              if(wid.type == 'table'){
                this.tablerecursion(wid)
              }
              if(wid.type == 'grid'){
                this.gridrecursion(wid)
              }
              if(wid.type == 'tab'){
                this.tabrecursion(wid)
              }
              if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'dynamic' ){
                this.cardrecursion(wid)
              }
            })
          }
        })
      }
    },
    cardrecursion(data){
      if(data.type == 'card' || data.type == 'inline' || data.type == 'dynamic' ){
        if(data.widgetList.length>0){
          data.widgetList.forEach(wid =>{
            if(wid.formItemFlag == true ){
              let datas = {
                label: wid.options.label,
                name: wid.options.name,
              }
              this.formlist.push(datas)
            }
            if(wid.type == 'table'){
              this.tablerecursion(wid)
            }
            if(wid.type == 'grid'){
              this.gridrecursion(wid)
            }
            if(wid.type == 'tab'){
              this.tabrecursion(wid)
            }
            if(wid.type == 'card' || wid.type == 'inline' || wid.type == 'dynamic' ){
              this.cardrecursion(wid)
            }
          })
        }

      }
    },
    resetFlowCondition() {
      this.bpmnElement = window.bpmnInstances.bpmnElement;
      this.bpmnElementSource = this.bpmnElement.source;
      this.bpmnElementSourceRef = this.bpmnElement.businessObject.sourceRef;
      if(this.bpmnElement.parent.businessObject.conditionIds){
        this.copylist = JSON.parse(this.bpmnElement.parent.businessObject.conditionIds)
      }

      this.detailForm.fields.forEach( (item,index) =>{
        if( item.formItemFlag  == true  ){
          let data = {
            label: item.options.label,
            name: item.options.name,
          }
          this.formlist.push(data)
        }
        if( item.type == 'table' ){
          this.$nextTick(()=>{
            this.tablerecursion(item)
          })
        }
        if( item.type == 'grid' ){
          this.$nextTick(()=>{
            this.gridrecursion(item)
          })
        }
        if( item.type == 'tab' ){
          this.$nextTick(()=>{
            this.tabrecursion(item)
          })
        }
        if( item.type == 'card' || item.type == 'dynamic' || item.type == 'inline' ){
          this.$nextTick(()=>{
            this.cardrecursion(item)
          })
        }
      })
      if (this.bpmnElementSourceRef && this.bpmnElementSourceRef.default && this.bpmnElementSourceRef.default.id === this.bpmnElement.id) {
        // 默认
        this.flowConditionForm = { type: "default" };
      } else if (!this.bpmnElement.businessObject.conditionExpression) {
        // 普通
        this.flowConditionForm = { type: "normal" };
      } else {
        // 带条件
        const conditionExpression = this.bpmnElement.businessObject.conditionExpression;
        this.flowConditionForm = { ...conditionExpression, type: "condition" };
        // resource 可直接标识 是否是外部资源脚本
        if (this.flowConditionForm.resource) {
          this.$set(this.flowConditionForm, "conditionType", "script");
          this.$set(this.flowConditionForm, "scriptType", "externalScript");
          return;
        }
        if (conditionExpression.language) {
          this.$set(this.flowConditionForm, "conditionType", "script");
          this.$set(this.flowConditionForm, "scriptType", "inlineScript");
          return;
        }
        this.$set(this.flowConditionForm, "conditionType", "expression");
      }
    },
    updateFlowType(flowType) {
      // 正常条件类
      if (flowType === "condition") {
        this.flowConditionRef = window.bpmnInstances.moddle.create("bpmn:FormalExpression");
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          conditionExpression: this.flowConditionRef
        });
        return;
      }
      // 默认路径
      if (flowType === "default") {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          conditionExpression: null
        });
        window.bpmnInstances.modeling.updateProperties(this.bpmnElementSource, {
          default: this.bpmnElement
        });
        return;
      }
      // 正常路径，如果来源节点的默认路径是当前连线时，清除父元素的默认路径配置
      if (this.bpmnElementSourceRef.default && this.bpmnElementSourceRef.default.id === this.bpmnElement.id) {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElementSource, {
          default: null
        });
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        conditionExpression: null
      });
    },
    updateFlowCondition() {
      let { conditionType, scriptType, body, resource, language } = this.flowConditionForm;
      let condition;
      if (conditionType === "expression") {
        condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body });
      } else {
        if (scriptType === "inlineScript") {
          condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { body, language });
          this.$set(this.flowConditionForm, "resource", "");
        } else {
          this.$set(this.flowConditionForm, "body", "");
          condition = window.bpmnInstances.moddle.create("bpmn:FormalExpression", { resource, language });
        }
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, { conditionExpression: condition });
    }
  },
  beforeDestroy() {
    this.bpmnElement = null;
    this.bpmnElementSource = null;
    this.bpmnElementSourceRef = null;
  }
};
</script>

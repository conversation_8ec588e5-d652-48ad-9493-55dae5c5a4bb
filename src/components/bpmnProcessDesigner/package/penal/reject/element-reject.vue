<template>
  <div class="panel-tab__content">


  </div>
</template>

<script>


export default {
  name: "ElementSupervise",
  props: {
    id: String,
    type: String,
    bpmnModeler: Object,
  },
  components:{

  },
  inject: {
    prefix: "prefix",
    width: "width",
  },
  data() {
    return {
      datalist: []
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        val && val.length && this.$nextTick(() => this.resetFormList());
      }
    }
  },
  methods: {
    resetFormList(){
      this.bpmnELement = window.bpmnInstances.bpmnElement;
      console.log(`bpmnELement`,this.bpmnELement)
      let usertasklist = []
      let modeler = this.bpmnModeler._definitions.rootElements[0].flowElements;
      modeler.forEach(item => {
        if(item.$type == 'bpmn:UserTask'){
          usertasklist.push(item)
        }
      })
      usertasklist.forEach((item,index) => {
        if(item.id == this.bpmnELement.id){
          if(index>0){
            this.datalist.push(item)
          }
        }
      })
      console.log(`datalist`,this.datalist)

    },
    saveFormList(){
      this.updateElementExtensions()
    },
    updateElementExtensions() {
      const newElExtensionElements = window.bpmnInstances.moddle.create(`bpmn:ExtensionElements`, {
        values: this.otherExtensions.concat(this.Data)
      });
      // // 更新到元素上
      window.bpmnInstances.modeling.updateProperties(this.bpmnELement, {
        extensionElements: newElExtensionElements
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.selector-dialog ::v-deep {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 80%;
    overflow: hidden;
  }
  .el-card__header {
    padding: 12px 18px;
  }
  .el-dialog__body {
    padding: 10px 15px;
    max-height: calc(100% - 110px) !important;
  }
}
</style>

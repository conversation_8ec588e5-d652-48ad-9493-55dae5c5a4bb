<template>
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="#1baeae">
        <van-swipe-item v-for="(item, index) in list" :key="index">
            <img :src="baseUrl+item.url" :onerror="defaultIcon">
        </van-swipe-item>
    </van-swipe>
</template>

<script>
    import {mapState, } from "vuex";
    import {reactive, toRefs} from "vue";

    export default {
        props: {
            list: Array
        },
        computed: {
            ...mapState(["baseUrl"])
        },
        setup() {
            const state = reactive({
                defaultIcon: require("../assets/isnull.png"),
            })

            return {
                ...toRefs(state),
            }
        },
        methods: {}
    }
</script>

<style lang='less' scoped>
    .my-swipe {
        img {
            width: 100%;
            height: 145px;
        }
    }
</style>

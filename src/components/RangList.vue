<template>
    <section>
        <van-search v-model="searchValue" @input="searchFocus" placeholder="请输入搜索关键词"/>
        <list :url="url" :queryObj="queryObj" @allData="allDataHandle"
              v-on:update:pageData="updatePageData($event)">
            <template v-slot:default="slotProps">
                <van-checkbox @click="onCellClick(item)" style="margin-left: 15px;" :key="item.id"
                              v-for="item in slotProps.list"
                              :name="item.humanname"
                              :model-value="codes.includes(item.humanname)">
                    <van-cell :label="item.organizationnames"
                              :title="item.humanname"></van-cell>
                </van-checkbox>
            </template>
        </list>
        <div class="van-submit-bar">
            <van-checkbox class="van-submit-bar-check"
                          @click="onCheckAll"
                          :model-value="isSelectedAll">全选
            </van-checkbox>
            <div class="van-submit-bar__text van-submit-bar__text1">
                <span style="float: left;margin-left: 15px;line-height: 50px;">共{{pageTotal}}记录</span>
            </div>
            <van-button class="van-button--square" type="primary"
                        size="large" @click="handleRangCancle">下一步
            </van-button>
        </div>
    </section>
</template>
<script>
    import AjaxApi from "@/utils/api";
    import List from "@/components/List";
    import {computed, onMounted, reactive, toRefs, watch,} from "vue";
    import {showToast} from "vant";

    export default {
        props: {
            mark: String,
            result: Array,
            rang: Array,
            nextNodesList: Array,

        },
        setup(props, ctx) {
            const state = reactive({
                pageTotal: 0,
                searchValue: null,
                items: [],
                url: AjaxApi.sytPermissionAccount,
                queryObj: {queryParam: {}},
                userParam: {}
            })

            onMounted(async () => {
                getqueryParam()
            })

            watch(() => state.searchValue, (newVal) => {
                // 因为watch被观察的对象只能是getter/effect函数、ref、active对象或者这些类型是数组

                if (newVal) {
                    // state.queryObj.queryParam.humanname = newVal
                } else {
                    // getqueryParam()
                }
            })

            const searchFocus = (val) =>{
                console.log(val.data)
                state.queryObj.queryParam.humanname = val.data
            }

            const getqueryParam = () => {
                if (props.nextNodesList && props.nextNodesList.length == 1) {
                    props.nextNodesList.forEach(form=>{
                        state.userParam = form.userTaskForm;
                    })
                    switch (state.userParam.dataType) {
                        case "USERS":
                        case "DEPTHEAD":
                            if (state.userParam.assignee && state.userParam.assignee !== '' && state.userParam.assignee.indexOf('assignee') === 0) {
                                state.queryObj.queryParam.humancode = state.userParam.assignee;
                            } else if (state.userParam.candidateUsers && state.userParam.candidateUsers !== '') {
                                state.queryObj.queryParam.humancode = state.userParam.candidateUsers;
                            }
                            break;
                        case "ROLES":
                            if (state.userParam.candidateGroups && state.userParam.candidateGroups !== '') {
                                state.queryObj.queryParam.roleId = state.userParam.candidateGroups.replaceAll("ROLE","");
                            }
                            break;
                        case "DEPTS":
                            if (state.userParam.candidateGroups && state.userParam.candidateGroups !== '') {
                                state.queryObj.queryParam.organizationnames = state.userParam.candidateGroups.replaceAll("DEPT","");
                            }
                            break;
                        case "INITATTR":
                            if (state.userParam.candidateGroups && state.userParam.candidateGroups !== '') {
                                state.queryObj.queryParam.configIds = state.userParam.candidateGroups.replaceAll("ATTR","");
                            }
                            break;
                        default:
                            break;
                    }
                }
            }

            const allDataHandle = (data) => {
                state.items = data;
            }
            const updatePageData = (e) => {
                state.pageTotal = e.total;
            }

            const codes = computed(() => {
                let arr = [];
                if (props.rang && props.rang.length > 0) {
                    props.rang.forEach(item => {
                        if(item.humanname){
                            arr.push(item.humanname);
                        }else {
                            arr.push(item);
                        }

                    });
                }
                return arr;
            })

            const isSelectedAll = computed(() => {
                if (state.items && state.items.length > 0) {
                    for (let i = 0; i < state.items.length; i++) {
                        const item = state.items[i];
                        if (!codes.value.includes(item.humanname)) {
                            return false;
                        }
                    }
                }
                return true;
            })
            const onCheckAll = () => {
                if (state.items && state.items.length > 0) {
                    if (isSelectedAll.value) {
                        state.items.forEach(item => {
                            onCellClick(item);
                        })
                    } else {
                        state.items.forEach(item => {
                            if (!codes.value.includes(item.humanname)) {
                                onCellClick(item);
                            }
                        })
                    }
                }
            }
            const onCellClick = (model) => {
                ctx.emit("update:checkRangBox", model);
            }
            const handleRangCancle = () => {
                if (props.rang && props.rang.length <= 0) {
                    showToast({type: 'warning', message: '请选择用户'});
                    return;
                }
                ctx.emit("update:handleRangCancle");
            }
            return {
                ...toRefs(state),
                isSelectedAll,
                codes,
                onCheckAll,
                allDataHandle,
                onCellClick,
                handleRangCancle,
                updatePageData,
                searchFocus
            }
        },
        components: {
            List
        },
        name: "RANGLIST",
    }
</script>

<style lang="less" scoped>
@import '../common/style/mixin';
@import '../common/style/home';
    .van-checkbox__label {
        margin-left: unset;
    }
    ::v-deep .vertical-apply .van-field {
        display: flex !important;
    }
    .van-submit-bar {
        width: 100%;
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        margin-left: unset;
    }

    .van-submit-bar__text1 {
        color: #999 !important;
        font-weight: normal !important;
        font-size: 13px !important;
        line-height: 50px;
        float: left;
        /*margin-left: 15px;*/
    }

    .van-submit-bar__bar {
        width: 100%;
    }

    .van-button--square {
        float: right;
        width: 80px !important;
    }

    .van-submit-bar-check {
        margin-left: 15px;
        height: 50px;
        float: left;
    }
</style>

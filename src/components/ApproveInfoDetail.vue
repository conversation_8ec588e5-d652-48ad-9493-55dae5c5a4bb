<template>
    <section>
        <div class="f-step" v-if="approveInfo">
            <div class="f-step-item" :key="flow.id" v-for="(flow, index) in approveInfo">
                <div class="f-step-item-left">
                    <van-icon v-if="flow.result===2" size="25px" name="checked"
                              color="#1cc397"/>
                    <van-icon v-else size="25px" name="clock" color="#969799"/>
                    <!--                    <van-icon v-else size="25px" name="clear" color="#fb3842"/>-->
                    <div v-if="index < approveInfo.length-1" class="item-left-vertical-line"></div>
                </div>
                <div class="f-step-item-right">
                    <div class="f-step-item_name">任务节点：{{ flow.name}}</div>
                    <div v-if="flow.name==='发起人'">
                        <span>发起人：{{ processInstanceData.startUser.humanName }} {{ processInstanceData.startUser.organizationnames}}&nbsp;</span>
                        <div><span>发起时间： {{dateFormat_date(processInstanceData.createTime)}}</span>
                        </div>
                    </div>
                    <div v-else>
                        <div v-if="flow.assigneeUser">
                            <span>审批人：{{ flow.assigneeUser.humanName }} {{ flow.assigneeUser.organizationnames }}</span>&nbsp;&nbsp;&nbsp;<span></span>
                        </div>
<!--                        <label v-if="!item.assigneeUser && item.assigneeUserList && item.assigneeUserList.length>0" style="font-weight: normal; margin-right: 30px;">-->
<!--                            {{index==tasks.length-1?'发起人':'审批人'}}：{{ item.assigneeUserStr }}-->
<!--                        </label>-->
                        <div v-if="!flow.assigneeUser && flow.assigneeUserList && flow.assigneeUserList.length>0">
                            <span>审批人：{{ flow.assigneeUserStr }}</span>&nbsp;&nbsp;&nbsp;<span></span>
                        </div>
                        <div v-if="flow.createTime"><span>接收时间：{{ dateFormat_date(flow.createTime) }}</span>&nbsp;&nbsp;&nbsp;<span></span>
                        </div>
                        <div v-if="flow.endTime">
                            <span>办结时间：{{dateFormat_date(flow.endTime) }}</span>&nbsp;&nbsp;&nbsp;<span></span>
                        </div>
                        <div v-if="flow.durationInMillis">
                            <span>耗时：{{getDate(flow.durationInMillis)}}</span>&nbsp;&nbsp;&nbsp;<span></span>
                        </div>
                    </div>
                    <!--                    <div v-if="flow.result">-->
                    <!--                        <span>结果：{{ getCommentType(flow.result) }}</span>&nbsp;&nbsp;&nbsp;<span></span>-->
                    <!--                    </div>-->
                    <!--                    <div v-if="flow.reason"><span>-->
                    <!--                                  <van-tag plain type="success">{{ flow.reason }} </van-tag></span>-->
                    <!--                    </div>-->
                    <!--操作记录-->
                    <template v-if="flow.commentList && flow.commentList.length > 0">
                        <div v-for="(comment,cindex) in flow.commentList" :key="cindex">
                            <div>
                                <van-tag :type="getApproveTypeTag(comment.type*1)">{{getCommentType(comment.type*1) }}
                                </van-tag>
                            </div>
                            <div>
                                <span>操作人：{{comment.humanName }} [{{ comment.userId }}]</span>
                            </div>
                            <div>
                                <span>操作时间：{{dateFormat_date(comment.time) }}</span>
                            </div>
                            <div>
                                <van-tag :type="getApproveTypeTag(comment.type*1)">{{ comment.fullMessage }}</van-tag>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div v-else-if="isError"></div>
    </section>
</template>

<script>
    import Utils from "@/utils/momentWrap";
    import {commentType, approveTypeTag} from "@/utils/textUtil";
    import {reactive, toRefs,} from "vue"

    export default {
        components: {},
        props: {
            currentId: String,
            activeTab: String,
            processInstanceData: { // 流程实例的数据。传递时，可展示流程发起人等信息
                type: Object,
            },
            approveInfo: Array,
        },
        name: "ApproveInfoDetail",
        setup() {
            const state = reactive({
                isError: false,
            })
            const getCommentType = (val) => {
                if (val) {
                    return commentType(val);
                }
                return ''
            }
            const getApproveTypeTag = (val) => {
                if (val) {
                    return approveTypeTag(val);
                }
                return ''
            }
            const dateFormat_date = (o) => {
                return Utils.dateFormat_date(o);
            }
            const getDate = (o) => {
                return Utils.getDate(o);
            }
            return {
                ...toRefs(state),
                getCommentType,
                getApproveTypeTag,
                dateFormat_date,
                getDate,
            }
        },
    }
</script>

<style scoped>
    .f-loading {
        padding: 10px;
        display: flex;
        justify-content: center;
    }

    .f-step {
        background-color: #fff;
        /*padding: 2px 10px 10px;*/
        display: flex;
        flex-direction: column;
    }

    .f-step-item {
        display: flex;
        /*height: 80px;*/
        padding-left: 5px;
    }

    .f-step-item-left {
        display: flex;
        flex-direction: column;
        position: relative;
        top: 8px;
    }

    .item-left-vertical-line {
        align-items: center;
        margin-left: 12px;
        flex-grow: 1;
        width: 1px;
        height: 40px;
        background-color: #dfdfdf;
    }

    .f-step-item-right {
        margin-left: 10px;
        width: 100%;
        /* height: 30px; */
        line-height: 20px;
        font-size: 13px;
        border-bottom: 1px solid #eee;
    }

    .f-step-item:nth-last-of-type(1) .f-step-item-right {
        border-bottom: none;
    }

    /*.f-step-item-right p {*/
    /*    margin: 0;*/
    /*}*/

    .f-step-item-right div:nth-last-of-type(1) {
        margin-bottom: 5px;
    }

    .f-step-item_name {
        font-size: 14px;
        font-weight: bold;
        margin-top: 8px;
    }
</style>

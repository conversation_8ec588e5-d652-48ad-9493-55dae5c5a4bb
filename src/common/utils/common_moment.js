import momentWrap from "@/utils/momentWrap";

/**
 * moment
 */
export const moment = momentWrap.moment;

/**
 * 【2018-09-17|不限制  至  2018-09-18|不限制】/【不限制】
 * @param start
 * @param end
 * @returns {string}
 */
export function applicationApprovalDate(start, end) {
    let sFormat = momentWrap.dateFormat_YMD(start);
    let eFormat = momentWrap.dateFormat_YMD(end);
    if (!sFormat && !eFormat)
        return '不限制';
    let arr = [sFormat, '至', eFormat];
    arr.forEach((item, index, theArray) => {
        if (!item)
            theArray[index] = '不限制'
    });
    return arr.join(" ");
}

/**
 * 2018-09-06
 * @param date
 * @returns {*}
 */
export function dateFormat_YMD(date, orGetNow) {
    return momentWrap.dateFormat_YMD(date, orGetNow);
}

export function dateFormat_YMD_now() {
    return momentWrap.dateFormat_YMD(null, true);
}

/**
 * 2019-07-18 11:11:11
 * @param o
 * @returns {*|string}
 */
export function dateFormat_date(o) {
    return momentWrap.dateFormat_date(o);
}

/**
 * 2019-07-18 11:00
 * @param o
 * @returns {*}
 */
export function dateFormat_YHMHM(o) {
    return momentWrap.dateFormat_YHMHM(o);
}

/**
 *获得本月的开始日期和结束日期
 */
export function getMonthStartDateAndDateRange(time) {
    let oneDayLong = 24 * 60 * 60 * 1000;
    let now = time;
    let year = now.getFullYear();
    let monthStartDate = new Date(year, now.getMonth(), 1); //当前月1号
    let nextMonthStartDate = new Date(year, now.getMonth(), 1); //下个月1号
    let days = (nextMonthStartDate.getTime() -
        monthStartDate.getTime()) / oneDayLong; //计算当前月份的天数
    let monthEndDate = new Date(year, now.getMonth() + 1, days);
    let monthRange = [moment(monthStartDate).format("YYYY-MM-DD"), moment(monthEndDate).format("YYYY-MM-DD")];
    return monthRange;
}

//移动端点击高亮
html, body {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

input {
  border: none;
  outline: none;
  -webkit-appearance: none;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

textarea {
  border: none;
  outline: none;
}

button {
  border: none;
  outline: none;
}

a {
  text-decoration: none;
  color: #333;
}

li {
  list-style-type: none;
}

//解决端遮罩层穿透
body.dialog-open {
  position: fixed;
  width: 100%;
}

.page {
  padding: 0 50px;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

@import './base.less';

.home-header {
  position: fixed;
  left: 0;
  top: 0;
  .wh(100%, 50px);
  .fj();
  line-height: 50px;
  padding: 0 15px;
  .boxSizing();
  font-size: 15px;
  color: #fff;
  z-index: 10000;

  .nbmenu2 {
    color: @primary;
  }

  &.active {
    background: @primary;

    .nbmenu2 {
      color: #fff;
    }

    .login {
      color: #fff;
    }

  }

  .header-search {
    display: flex;
    .wh(100%, 20px);
    line-height: 20px;
    margin: 10px 0;
    padding: 5px 0;
    color: #232326;
    background: rgba(255, 255, 255, .7);
    border-radius: 20px;

    .app-name {
      padding: 0 10px;
      color: @primary;
      font-size: 16px;
      font-weight: bold;
      border-right: 1px solid rgba(102, 102, 102, 0.35);
    }

    .icon-search {
      padding: 0 10px;
      font-size: 17px;
    }

    .search-title {
      font-size: 14px;
      color: #666;
      line-height: 21px;
    }

  }

  .icon-iconyonghu {
    color: #fff;
    font-size: 22px;
  }

  .login {
    color: @primary;
    line-height: 52px;

    .van-icon-manager-o {
      font-size: 20px;
      vertical-align: -3px;
    }

  }
}

.category-list {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 13px;
  div {

    display: flex;
    flex-direction: column;
    width: 25%;
    text-align: center;

    img {
      .wh(36px, 36px);
      margin: 13px auto 8px auto;
    }
  }

  .data {
    width: 48%;
    text-align: center;
    float: left;
    background: #f4f9ff;
    border-radius: 3px;
    margin: 1%;
  }
}

.good {
  .good-header {
    background: #f9f9f9;
    height: 5px;
    text-align: center;
    color: @primary;
    font-size: 16px;
    font-weight: 500;
  }

  .good-box {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .good-item {
      box-sizing: border-box;
      width: 50%;
      border-bottom: 1PX solid #e9e9e9;
      padding: 10px 10px;

      img {
        display: block;
        width: 120px;
        margin: 0 auto;
      }

      .good-desc {
        text-align: center;
        font-size: 14px;
        padding: 10px 0;

        .title {
          color: #222333;
        }

        .price {
          color: @primary;
        }
      }

      &:nth-child(2n + 1) {
        border-right: 1PX solid #e9e9e9;
      }
    }
  }
}

.floor-list {
  width: 100%;
  padding-bottom: 50px;

  .floor-head {
    width: 100%;
    height: 40px;
    background: #F6F6F6;
  }

  .floor-content {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    width: 100%;
    .boxSizing();

    .floor-category {
      width: 50%;
      padding: 10px;
      border-right: 1px solid #dcdcdc;
      border-bottom: 1px solid #dcdcdc;
      .boxSizing();

      &:nth-child(2n) {
        border-right: none;
      }

      p {
        font-size: 17px;
        color: #333;

        &:nth-child(2) {
          padding: 5px 0;
          font-size: 13px;
          color: @primary;
        }

      }

      .floor-products {
        .fj();
        width: 100%;

        img {
          .wh(65px, 65px);
        }

      }
    }
  }
}


.good-header {
  height: 1px;
}

.mineData {
  width: 100%;
  height: 140px;
  background: #fff;
  padding-left: 14px;
}

.mineDataTitle {
  height: 45px;
  line-height: 45px;
  color: #333;
  padding-left: 2px;
  font-size: 15px;
  font-weight: bold;
  font-family: "微软雅黑";
  margin-top: 8px;
}

.dataContent {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  width: 100%;
}

.colorRed {
  color: #f94a59;
  font-size: 16px;
}

.colorBlue {
  color: #319cfe;
  font-size: 16px;
}

.newWrap {
  margin-top: 8px;
  background: #fff;
  padding: 5px 15px;
  position: relative;
}

.lookMoreDiy {
  float: right;
  right: 15px;
  top: 12px;
  font-size: 16px;
  color: #999;
}

.lookMore {
  position: absolute;
  right: 15px;
  top: 18px;
  font-size: 16px;
  color: #999;
  z-index: 1000;
}

//
//.new {
//  width: 100%;
//  height: 25px;
//  margin-bottom: 13px;
//}
//
//.newTitle {
//  font-size: 14px;
//  color: #333;
//  line-height: 20px;
//  overflow: hidden;
//  white-space: nowrap;
//  text-overflow: ellipsis;
//}
//
//.newTag {
//  float: left;
//  width: 5px;
//  height: 5px;
//  background: #72bcfb;
//  border-radius: 50%;
//  margin-top: 7.5px;
//  margin-right: 5px;
//}
//
//.date {
//  font-size: 12px;
//  color: #999;
//  margin-top: 5px;
//}
//
//
//.van-row {
//  padding: 0;
//}
//
//.newRow {
//  display: flex;
//  padding-top: 3px;
//  margin-bottom: 10px;
//}
//
//.newRow .titleWrap {
//  flex-grow: 5;
//}
//
//.newRow .newImgWrap {
//  flex-grow: 1;
//  padding: 4px;
//  padding-right: 0;
//}
//
//.newTitle1 {
//  font-size: 14px;
//  line-height: 22px;
//  color: #333;
//  display: block;
//  display: -webkit-box;
//  -webkit-line-clamp: 2;
//  -webkit-box-orient: vertical;
//  overflow: hidden;
//  text-overflow: ellipsis;
//  padding-right: 5px;
//}
//
//.newElse {
//  font-size: 12px;
//  color: #999;
//  margin-top: 5px;
//}
//
//.newBlock {
//  display: inline-block;
//  color: #da3b35;
//  width: 30px;
//  height: 15px;
//  line-height: 14px;
//  border: 1px solid #da3b35;
//  text-align: center;
//  border-radius: 2px;
//  margin-right: 10px;
//}
//
//.newFrom {
//  font-size: 14px;
//  color: #666;
//  margin-right: 3px;
//}
//
//.van-col--8 {
//  padding-left: 10px;
//}
//
//.newImg {
//  width: 100px;
//  height: 100%;
//  border-radius: 5px;
//}


//.applyServiceWrap {
//  width: 100%;
//  height: 210px;
//  margin-top: 6px;
//  background: #fff;
//}


.applyServiceTitle {
  height: 45px;
  line-height: 45px;
  color: #333;
  padding-left: 16px;
  font-size: 15px;
  font-weight: bold;
  font-family: "微软雅黑";
  margin:0 !important;
  background-color: #E6F3FC;
}


.serviceContent {
  width: 100%;
  height: 100px;
}

.service {
  float: left;
  width: 25%;
  text-align: center;
}

.service img {
  width: 30px;
  height: 30px;
}

.service p {
  font-size: 14px;
  color: #666;
}

.my-process-designer {
  height: 200px;
}

!(function (n, e) { typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = e() : typeof define === 'function' && define.amd ? define(e) : (n = n || self).jsApi = e() }(this, function () { 'use strict'; var n = navigator.userAgent; var e = { versions: { trident: n.indexOf('Trident') > -1, presto: n.indexOf('Presto') > -1, webKit: n.indexOf('AppleWebKit') > -1, gecko: n.indexOf('Gecko') > -1 && n.indexOf('KHTML') === -1, mobile: !!n.match(/AppleWebKit.*Mobile.*/), ios: !!n.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), android: n.indexOf('Android') > -1 || n.indexOf('Linux') > -1, iPhone: n.indexOf('iPhone') > -1, iPad: n.indexOf('iPad') > -1, webApp: n.indexOf('Safari') === -1 }, language: (navigator.browserLanguage || navigator.language).toLowerCase() }; function a () { var n = window.sessionStorage.getItem('jsSession'); var r = { msg: '', status: '' }; return new Promise(function (t, i) { void 0 !== n && n !== '' && n !== null || (r.status = -1, r.msg = '未登录', i(r)), window.WebViewJavascriptBridge.callHandler('authCheckApp', { session: n }, function (n) { var e = n; typeof n === 'string' && (e = JSON.parse(n)), e.status === 0 ? (r.status = 0, r.msg = '认证成功', t(r)) : (r.stutas = e.status, r.msg = e.msg, i(r)) }) }) } function t (n, e) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler(n, e, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) } var i = { apiAddSchedule: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiAddSchedule', { scheduleName: n.scheduleName, startDate: n.startDate, endDate: n.endDate, address: n.address, remindDate: n.remindDate, describe: n.describe }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, login: function (n) { return new Promise(function (t, i) { !(function (n) { if (window.WebViewJavascriptBridge) return n(window.WebViewJavascriptBridge); if (window.WVJBCallbacks) return window.WVJBCallbacks.push(n); window.WVJBCallbacks = [n]; var e = document.createElement('iframe'); e.style.display = 'none', e.src = 'wvjbscheme://__BRIDGE_LOADED__', document.documentElement.appendChild(e), setTimeout(function () { document.documentElement.removeChild(e) }, 0) }(function () { window.WebViewJavascriptBridge.callHandler('login', { appKey: n.appKey }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), e.status === 0 ? (sessionStorage.setItem('jsSession', e.result.session), t(e)) : i(e) }) })) }) }, getGPS: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('getGPS', null, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, getUserInfo: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('getUserInfor', null, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, scanQRcode: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('scanQRcode', null, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, scanQRdata: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('scanQRdata', null, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, alert: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('alert', { message: n.message, title: n.title, btnName: n.btnName }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, confirm: function (r) { return new Promise(function (i, e) { a().then(function () { window.WebViewJavascriptBridge.callHandler('confirm', { message: r.message, title: r.title, btnName: r.btnName }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), i(e); var t = e.result.clickIndex; r.btn[t]() }) }).catch(function (n) { e(n) }) }) }, singleShake: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('singleShake', {}, function (n) {}) }) }, showProgress: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('showProgress', {}, function (n) {}) }) }, hiddenProgress: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('hiddenProgress', {}, function (n) {}) }) }, hiddenNavigation: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('hiddenNavigation', {}, function (n) {}) }) }, showNavigation: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('showNavigation', {}, function (n) {}) }) }, closeWebView: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('closeWebView', {}, function (n) {}) }).catch(function (n) { Promise.reject(n) }) }, webViewReset: function () { a().then(function () { window.WebViewJavascriptBridge.callHandler('webViewReset', {}, function (n) {}) }) }, modifyNavigationTitle: function (n) { a().then(function () { window.WebViewJavascriptBridge.callHandler('webViewReset', { title: n.title }, function (n) {}) }) }, createTribe: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('createTribe', { tribeName: n.tribeName, tribeNotice: n.tribeNotice }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, joinToTribe: function (n) { return t('joinToTribe', n) }, openTribe: function (n) { return t('openTribe', n) }, noticeBroad: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('noticeBroad', { noticeName: n.noticeName }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, openPerson: function (n) { return t('openPerson', n) }, openPersonWithKey: function (n) { return t('openPersonWithKey', n) }, shakeOneShake: function () { return new Promise(function (t) { window.WebViewJavascriptBridge.registerHandler('shakeOneShake', function (n, e) { e({ status: 0 }), t(n) }) }) }, saveImage: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('saveImage', { imageUrl: n.imageUrl }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, needBackAction: function () { return new Promise(function (e) { window.WebViewJavascriptBridge.callHandler('needBackAction', {}, function (n) { e(n) }) }) }, giveBackAction: function () { return new Promise(function (t) { window.WebViewJavascriptBridge.registerHandler('giveBackAction', function (n, e) { e({ status: 0 }), t(n) }) }) }, makePhoneCall: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('makePhoneCall', { tel: n.tel }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, requestMethod: function (n) { return new Promise(function (t, e) { a().then(function () { window.WebViewJavascriptBridge.callHandler('requestMethod', { method: n.method, requestData: n.requestData, requestUrl: n.requestUrl }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), t(e) }) }).catch(function (n) { e(n) }) }) }, openNewWebView: function (n) { a().then(function () { window.WebViewJavascriptBridge.callHandler('openNewWebView', { url: n.url, title: n.title, hiddenNavigation: n.hiddenNavigation }, function (n) {}) }) }, cutWebView: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('cutWebView', {}, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, phoneInfo: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('phoneInfo', {}, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, apiDeviceGetAddress: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiDeviceGetAddress', {}, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, perfectPhone: function (n) { return t('perfectPhone', n) }, getSysBrowser: function (n) { return t('getSysBrowser', n) }, getChildrenInfo: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('getChildrenInfo', {}, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, zfbPay: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('zfbPay', { payApply: n.payApply }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, cmxxsfzzdy: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('cmxxsfzzdy', { url: n.url, type: n.type }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, wxPay: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('wxPay', { payApply: n.payApply }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, getEduUserInfo: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiGetEDUUserInfo', null, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, apiGetRecording: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiGetRecording', { duration: n.duration }, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, terminalPrint: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiPrinting', n, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, terminalDeviceInfo: function () { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiGetDeviceInfo', null, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, terminalSaveUserInfo: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiSaveUserInfo', n, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) }, terminalScanQr: function (n) { return new Promise(function (t, i) { a().then(function () { window.WebViewJavascriptBridge.callHandler('apiScanQr', n, function (n) { var e = n; typeof e === 'string' && (e = JSON.parse(e)), (e.status === 0 ? t : i)(e) }) }).catch(function (n) { i(n) }) }) } }; return new function n () { !(function (n, e) { if (!(n instanceof e)) throw new TypeError('Cannot call a class as a function') }(this, n)), this.browser = e, Object.assign(this, i) }() }))

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv=X-UA-Compatible content="IE=edge">
    <meta name=viewport
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name=format-detection content="telephone=no, email=no">

    <title>加载中</title>

    <style>
        .d-flex {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .loader {
            border: 5px solid #f3f3f3;
            border-radius: 50%;
            border-top: 5px solid #3498db;
            width: 30px;
            height: 30px;
            margin-top: 50%;
            -webkit-animation: spin 2s linear infinite; /* Safari */
            animation: spin 2s linear infinite;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .content {
            margin-top: 20px;
            font-size: 14px;
            color: #999;
        }
    </style>


    <script src="/mobile/cdn/js/jquery-3.1.1.min.js"></script>
    <!--    <script src="/style/sytxggl/js/crypto/crypto-js.js"></script>-->
    <script src="/mobile/cdn/js/crypto/crypto-js.min.js"></script>
    <script src="js_api.min.js"></script>
    <!-- 辅助调试工具 -->
<!--    <script src="https://cdn.bootcss.com/eruda/1.4.2/eruda.min.js"></script>
    <script>eruda.init();</script>-->

    <script>
      $(function () {
        window.jsApi.login({appKey: '0000'}).then(function () {
          getUserInfo()
        });
        // getUserInfo()
      });

      function getUserInfo() {
        window.jsApi.getEduUserInfo().then(function (res) {
          console.log(res)
          if (res.status !== 0) {
            console.error(res.msg)
            doError(res.msg)
          }
          //只有res.status = 0 才是调用成功
          // document.getElementById("result").value += JSON.stringify(res)
          var result = res.result;

          var key_id = result['KEY_ID'];
          console.log(key_id)
          auth(key_id)
        }).catch(function (err) {
          console.error(err)
          doError()
        })
      }

      function doError(text) {
        var url = '/mobile/permissionDenied.html';
        if (text) {
          url += "?message=" + text;
        }
        window.location.replace(url)
      }

      function auth(key_id) {
        $.post("/nonlogin/syt/chongQingChengShiZhiYe/auth/getSecurityKey", {}, function (data) {
          if (data && data.code === "00000") {
            var keyAll = data.info;

            var keyStr = keyAll.substring(0, 16);
            var ivStr = keyAll.substring(16, 32);
            var key_bytes = CryptoJS.enc.Utf8.parse(keyStr);
            var iv_bytes = CryptoJS.enc.Utf8.parse(ivStr);

            var ciphertext = CryptoJS.AES.encrypt(key_id, key_bytes, {iv: iv_bytes}).toString();

            $.post("/nonlogin/syt/chongQingChengShiZhiYe/auth/doAuth", {s: ciphertext}, function (data) {
              if (data && data.code === "00000") {
                var info = data.info;
                var redirect_uri = "/mobile/index.html?uid=" + info.uid + "&token=" + info.token;
                window.location.replace(redirect_uri);
              } else {
                doError(data ? data.msg : null)
              }
            })
          } else {
            doError()
          }
        })

      }

      function getQueryVariableObj() {
        var obj = {};
        var query = window.location.search.substring(1);
        var vars = query.split('&');
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split('=');
          obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
        }
        return obj;
      }

      function getCookie(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i].trim();
          if (c.indexOf(name) === 0) return c.substring(name.length, c.length);
        }
        return null;
      }
    </script>
</head>
<body>
<div class="d-flex">
    <div class="loader"></div>
    <div class="content">加载中...</div>
</div>

</body>
</html>

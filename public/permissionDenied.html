<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset=utf-8>
    <meta http-equiv=X-UA-Compatible content="IE=edge">
    <meta name=viewport
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name=format-detection content="telephone=no, email=no">
    <title>错误</title>
    <script src="/mobile/cdn/js/jquery-3.1.1.min.js"></script>

    <style>
        .d-flex {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .content {
            font-size: 1.8rem;
            /*text-align: center;*/
            /*margin: 1rem 0;*/
            color: #00a5ec;
            padding: 0 16px;
        }

        .defaultText {
            text-align: center;
        }

        @media (max-width: 1024px) {
            .banImg {
                width: 80%;
            }

            .content {
                font-size: 18px;
            }
        }
    </style>

    <script>
        function getQueryVariableObj() {
            var obj = {};
            var query = window.location.search.substring(1);
            var vars = query.split('&');
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split('=');
                obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
            }
            return obj;
        }

        $(function () {
            var queryObj = getQueryVariableObj();
            if (queryObj.hasOwnProperty('message') && queryObj.message) {
                $(".content").html(queryObj.message);
            }
        })
    </script>
</head>
<body>
<div class="d-flex">
    <img class="banImg" src="/mobile/cdn/img/authErrorNow.png" alt="">
    <div class="content">
        <div class="defaultText">
            登录失败
        </div>
<!--        <ol>-->
<!--            <li>未从钉钉工作台应用中进入</li>-->
<!--            <li>业务系统中此账号不存在</li>-->
<!--&lt;!&ndash;            <li>未使用绑定的设备</li>&ndash;&gt;-->
<!--        </ol>-->
    </div>
</div>
</body>
</html>

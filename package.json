{"name": "portal-mobile", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build"}, "dependencies": {"@smallwei/avue": "^3.3.4", "@vant/compat": "^1.0.0", "axios": "^0.20.0", "better-scroll": "^2.3.0", "bpmn-js": "^11.1.0", "core-js": "^3.6.5", "echarts": "^5.4.1", "element-plus": "^2.6.2", "highlight.js": "^11.7.0", "js-calendar-converter": "^0.0.5", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "lib-flexible": "^0.3.2", "moment": "^2.27.0", "nprogress": "^0.2.0", "pushstate-server": "^3.1.0", "vant": "^4.0.3", "vkbeautify": "^0.99.3", "vue": "^3.0.0", "vue-router": "^4.0.0-beta.13", "vue2-verify": "^1.1.5", "vue3-colorpicker": "^2.0.11", "vuex": "^4.0.0", "x2js": "^3.4.4"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "css-loader": "^3.6.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0-0", "less": "^3.12.2", "less-loader": "^7.0.2", "postcss-pxtorem": "^5.1.1", "sass": "^1.89.2", "sass-loader": "^10.5.2", "style-loader": "^0.23.1", "unplugin-vue-components": "^0.22.12", "vue-loader-v16": "^16.0.0-beta.5.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"vue/no-v-model-argument": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
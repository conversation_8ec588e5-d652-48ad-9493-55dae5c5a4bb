2025-07-30 08:49:28.287 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 08:49:28.638 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 9829 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 08:49:28.638 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 08:49:30.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:49:30.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 08:49:31.173 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 08:49:31.219 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 293 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 08:49:31.225 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:49:31.226 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 08:49:31.260 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 08:49:31.260 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 08:49:31.270 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:49:31.270 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 08:49:31.302 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 08:49:31.303 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 08:49:31.304 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 08:49:31.304 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 JPA repository interfaces.
2025-07-30 08:49:31.310 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:49:31.311 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 08:49:31.338 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 08:49:31.338 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 08:49:31.359 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 1 LDAP repository interfaces.
2025-07-30 08:49:31.366 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:49:31.367 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 08:49:31.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 08:49:31.399 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 08:49:31.400 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 08:49:31.400 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 MongoDB repository interfaces.
2025-07-30 08:49:31.419 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:49:31.421 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 08:49:31.463 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 08:49:31.463 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 08:49:31.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 08:49:31.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-07-30 08:49:32.184 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 08:49:32.205 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 08:49:32.236 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 08:49:32.843 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 08:49:32.875 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 08:49:32.877 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 08:49:32.881 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 08:49:33.108 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 08:49:33.108 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4386 ms
2025-07-30 08:49:33.529 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 08:49:34.362 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 08:53:28.975 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 08:53:29.315 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 11388 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 08:53:29.317 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 08:53:31.613 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:53:31.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 08:53:31.942 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 08:53:32.049 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 421 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 08:53:32.063 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:53:32.064 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 08:53:32.397 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 08:53:32.398 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 333 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 08:53:32.410 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:53:32.411 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 08:53:32.455 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 08:53:32.456 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 08:53:32.458 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 08:53:32.458 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 JPA repository interfaces.
2025-07-30 08:53:32.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:53:32.465 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 08:53:32.496 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 08:53:32.496 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 08:53:32.519 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 1 LDAP repository interfaces.
2025-07-30 08:53:32.526 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:53:32.526 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 08:53:32.553 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 08:53:32.553 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 08:53:32.554 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 08:53:32.554 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 MongoDB repository interfaces.
2025-07-30 08:53:32.570 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 08:53:32.573 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 08:53:32.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 08:53:32.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 08:53:32.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 08:53:32.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-07-30 08:53:33.522 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 08:53:33.542 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 08:53:33.573 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 08:53:34.211 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 08:53:34.249 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 08:53:34.250 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 08:53:34.250 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 08:53:34.511 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 08:53:34.511 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5107 ms
2025-07-30 08:53:35.004 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 08:53:35.821 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 08:53:37.127 [redisson-netty-2-18] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-07-30 08:53:37.775 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-30 08:53:38.124 [cluster-ClusterId{value='68896d11389c564ac9eca080', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 08:53:38.449 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 08:53:38.449 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 08:53:38.450 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 08:53:39.064 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 08:53:39.328 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-07-30 08:53:39.717 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 08:53:40.010 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 08:53:44.943 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-07-30 08:53:48.343 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 08:53:56.155 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 08:53:56.181 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 09:09:05.497 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 09:09:05.923 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 17089 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 09:09:05.924 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 09:09:08.791 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:08.795 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 09:09:09.068 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 09:09:09.117 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 308 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 09:09:09.125 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:09.126 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 09:09:09.168 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 09:09:09.168 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 09:09:09.179 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:09.180 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 09:09:09.242 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 09:09:09.244 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 09:09:09.246 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 09:09:09.246 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 60 ms. Found 0 JPA repository interfaces.
2025-07-30 09:09:09.255 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:09.256 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 09:09:09.284 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 09:09:09.285 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 09:09:09.311 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 1 LDAP repository interfaces.
2025-07-30 09:09:09.319 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:09.320 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 09:09:09.347 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 09:09:09.348 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 09:09:09.348 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 09:09:09.348 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 MongoDB repository interfaces.
2025-07-30 09:09:09.368 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:09.370 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:09:09.409 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 09:09:09.410 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 09:09:09.410 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 09:09:09.410 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-30 09:09:10.311 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:09:10.338 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:09:10.379 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:09:11.069 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 09:09:11.125 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 09:09:11.126 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:09:11.126 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 09:09:11.359 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:09:11.360 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5327 ms
2025-07-30 09:09:11.871 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 09:09:12.740 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 09:09:14.160 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-07-30 09:09:14.828 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-30 09:09:15.213 [cluster-ClusterId{value='688970ba1ef85b3e2c0ec34f', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 09:09:15.544 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 09:09:15.545 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 09:09:15.545 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 09:09:16.344 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 09:09:16.647 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-07-30 09:09:17.151 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 09:09:17.555 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 09:09:21.138 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-07-30 09:09:21.228 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-07-30 09:09:45.696 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 09:09:46.026 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 17220 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 09:09:46.026 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 09:09:48.466 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:48.471 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 09:09:48.751 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 09:09:48.797 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 313 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 09:09:48.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:48.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 09:09:48.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 09:09:48.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 09:09:48.849 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:48.849 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 09:09:48.887 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 09:09:48.888 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 09:09:48.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 09:09:48.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 JPA repository interfaces.
2025-07-30 09:09:48.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:48.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 09:09:48.932 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 09:09:48.933 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 09:09:48.954 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57 ms. Found 1 LDAP repository interfaces.
2025-07-30 09:09:48.962 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:48.962 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 09:09:48.988 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 09:09:48.989 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 09:09:48.989 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 09:09:48.989 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 MongoDB repository interfaces.
2025-07-30 09:09:49.005 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 09:09:49.007 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 09:09:49.043 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 09:09:49.044 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 09:09:49.044 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 09:09:49.044 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-07-30 09:09:49.760 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:09:49.780 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:09:49.812 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 09:09:50.395 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 09:09:50.426 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 09:09:50.426 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 09:09:50.427 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 09:09:50.651 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 09:09:50.651 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4537 ms
2025-07-30 09:09:51.158 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 09:09:52.039 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 09:09:53.491 [redisson-netty-2-18] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-07-30 09:09:54.154 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-30 09:09:54.563 [cluster-ClusterId{value='688970e2502322229fdd93eb', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 09:09:54.848 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 09:09:54.849 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 09:09:54.849 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 09:09:55.500 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 09:09:55.650 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-07-30 09:09:56.193 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 09:09:56.538 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 09:10:00.067 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-07-30 09:10:00.147 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-07-30 09:10:16.230 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-30 09:10:16.303 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 09:10:25.148 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Spring Data Elasticsearch: 4.2.10
2025-07-30 09:10:25.148 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client in build: 7.12.1
2025-07-30 09:10:25.148 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client used: 7.12.1
2025-07-30 09:10:25.148 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch cluster: 7.12.1
2025-07-30 09:10:26.291 [main] INFO  org.flowable.spring.boot.eventregistry.EventRegistryAutoConfiguration - No deployment resources were found for autodeployment
2025-07-30 09:10:27.506 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Found 2 Engine Configurators in total:
2025-07-30 09:10:27.506 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 09:10:27.506 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 09:10:27.507 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 09:10:27.514 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 09:10:30.722 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 09:10:32.749 [main] INFO  liquibase.database - Could not set remarks reporting on OracleDatabase: com.sun.proxy.$Proxy231.setRemarksReporting(boolean)
2025-07-30 09:10:36.101 [main] INFO  liquibase.changelog - Reading from SYT_PORTAL.FLW_EV_DATABASECHANGELOG
2025-07-30 09:10:37.039 [main] INFO  org.flowable.eventregistry.impl.EventRegistryEngineImpl - EventRegistryEngine default created
2025-07-30 09:10:37.045 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 09:10:38.621 [main] INFO  org.flowable.idm.engine.impl.IdmEngineImpl - IdmEngine default created
2025-07-30 09:10:40.444 [main] INFO  org.flowable.engine.impl.ProcessEngineImpl - ProcessEngine default created
2025-07-30 09:10:40.877 [main] INFO  org.flowable.engine.impl.cmd.ValidateV5EntitiesCmd - Total of v5 deployments found: 0
2025-07-30 09:10:43.768 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-07-30 09:10:46.510 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-30 09:10:46.611 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:10:46.611 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:10:46.627 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-30 09:10:46.634 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 09:10:46.634 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 09:10:46.634 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-30 09:10:46.635 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@243dc5d1
2025-07-30 09:10:47.944 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-30 09:10:47.944 [main] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main
2025-07-30 09:10:47.944 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 09:10:47.945 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-30 09:10:47.945 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-30 09:10:47.945 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 09:10:47.945 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-30 09:10:47.945 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-30 09:10:48.022 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-30 09:10:52.213 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-07-30 09:10:52.270 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-07-30 09:10:52.273 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-07-30 09:10:52.365 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-07-30 09:10:52.394 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-07-30 09:10:52.400 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-07-30 09:10:52.400 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-07-30 09:10:52.400 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-07-30 09:10:52.400 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-07-30 09:10:52.400 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-07-30 09:10:52.400 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-07-30 09:10:52.401 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-07-30 09:10:52.409 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-07-30 09:10:52.410 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-07-30 09:10:52.494 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册请求拦截器：class com.sanyth.portal.web.filter.MagicApiRequestInterceptor
2025-07-30 09:10:52.512 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 09:10:53.397 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-07-30 09:10:53.463 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-07-30 09:10:53.467 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-30 09:10:53.467 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2f42f229]]
2025-07-30 09:10:53.468 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-30 09:10:53.468 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-07-30 09:10:53.468 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 09:10:53.488 [main] INFO  com.sanyth.portal.Main - Started Main in 68.66 seconds (JVM running for 75.903)
2025-07-30 09:10:59.138 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-07-30 09:10:59.229 [main] INFO  com.sanyth.portal.web.runner.InitRunner - 初始化日程提醒....
2025-07-30 09:11:00.033 [main] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 09:11:12.419 [cluster-ClusterId{value='688970e2502322229fdd93eb', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 10 common frames omitted
2025-07-30 09:11:16.177 [cluster-ClusterId{value='688970e2502322229fdd93eb', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 09:11:30.077 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 09:11:30.173 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-30 09:11:30.174 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2f42f229]]
2025-07-30 09:11:30.174 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-30 09:11:30.174 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:11:30.271 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-07-30 09:11:30.271 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 09:11:30.271 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 09:11:30.272 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 09:11:31.241 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 09:11:31.252 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown initiated...
2025-07-30 09:11:33.245 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown completed.
2025-07-30 11:16:42.273 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 11:16:42.618 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 35677 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 11:16:42.619 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 11:16:45.013 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:16:45.017 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:16:45.302 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 11:16:45.359 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 329 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 11:16:45.366 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:16:45.367 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:16:45.415 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 11:16:45.415 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 11:16:45.427 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:16:45.428 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 11:16:45.462 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:16:45.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:16:45.465 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:16:45.465 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 JPA repository interfaces.
2025-07-30 11:16:45.471 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:16:45.472 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 11:16:45.497 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 11:16:45.498 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 11:16:45.522 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 1 LDAP repository interfaces.
2025-07-30 11:16:45.529 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:16:45.530 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 11:16:45.557 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:16:45.558 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:16:45.558 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:16:45.558 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 MongoDB repository interfaces.
2025-07-30 11:16:45.576 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:16:45.578 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:16:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:16:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:16:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:16:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-07-30 11:16:46.447 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:16:46.467 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:16:46.500 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:16:47.242 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 11:16:47.279 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 11:16:47.279 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:16:47.279 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 11:16:47.525 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:16:47.525 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4823 ms
2025-07-30 11:16:47.989 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 11:16:48.850 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 11:16:50.338 [redisson-netty-2-18] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-07-30 11:16:51.064 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-30 11:16:51.446 [cluster-ClusterId{value='68898ea3d5a0f27b11310459', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:16:51.758 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:16:51.759 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:16:51.759 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:16:52.761 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 11:16:53.024 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-07-30 11:16:54.170 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 11:16:54.525 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 11:16:56.133 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-07-30 11:16:59.698 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 11:17:01.040 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 11:17:01.070 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 11:17:17.124 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 11:17:17.495 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 36674 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 11:17:17.495 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 11:17:19.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:17:19.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:17:20.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 11:17:20.164 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 329 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 11:17:20.171 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:17:20.171 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:17:20.226 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 11:17:20.227 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 11:17:20.238 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:17:20.239 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 11:17:20.279 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:17:20.281 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:17:20.282 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:17:20.282 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 JPA repository interfaces.
2025-07-30 11:17:20.288 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:17:20.289 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 11:17:20.317 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 11:17:20.317 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 11:17:20.340 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50 ms. Found 1 LDAP repository interfaces.
2025-07-30 11:17:20.348 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:17:20.349 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 11:17:20.381 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:17:20.381 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:17:20.382 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:17:20.382 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 MongoDB repository interfaces.
2025-07-30 11:17:20.400 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:17:20.403 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:17:20.452 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:17:20.452 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:17:20.453 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:17:20.453 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-07-30 11:17:21.208 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:17:21.244 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:17:21.338 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:17:21.962 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 11:17:21.996 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 11:17:21.996 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:17:21.996 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 11:17:22.206 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:17:22.206 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4624 ms
2025-07-30 11:17:22.635 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 11:17:23.521 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 11:17:24.974 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-07-30 11:17:25.740 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-30 11:17:26.157 [cluster-ClusterId{value='68898ec54e67504af0f09edf', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:17:26.554 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:17:26.555 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:17:26.556 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:17:27.387 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 11:17:27.557 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-07-30 11:17:28.134 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 11:17:28.486 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 11:17:31.639 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-07-30 11:17:31.733 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-07-30 11:17:44.208 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-30 11:17:44.286 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:17:53.336 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Spring Data Elasticsearch: 4.2.10
2025-07-30 11:17:53.336 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client in build: 7.12.1
2025-07-30 11:17:53.336 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client used: 7.12.1
2025-07-30 11:17:53.336 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch cluster: 7.12.1
2025-07-30 11:17:54.435 [main] INFO  org.flowable.spring.boot.eventregistry.EventRegistryAutoConfiguration - No deployment resources were found for autodeployment
2025-07-30 11:17:55.453 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Found 2 Engine Configurators in total:
2025-07-30 11:17:55.453 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 11:17:55.453 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 11:17:55.453 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 11:17:55.459 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 11:17:59.173 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 11:18:01.001 [main] INFO  liquibase.database - Could not set remarks reporting on OracleDatabase: com.sun.proxy.$Proxy231.setRemarksReporting(boolean)
2025-07-30 11:18:05.050 [main] INFO  liquibase.changelog - Reading from SYT_PORTAL.FLW_EV_DATABASECHANGELOG
2025-07-30 11:18:05.900 [main] INFO  org.flowable.eventregistry.impl.EventRegistryEngineImpl - EventRegistryEngine default created
2025-07-30 11:18:05.905 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 11:18:06.997 [main] INFO  org.flowable.idm.engine.impl.IdmEngineImpl - IdmEngine default created
2025-07-30 11:18:08.272 [main] INFO  org.flowable.engine.impl.ProcessEngineImpl - ProcessEngine default created
2025-07-30 11:18:08.622 [main] INFO  org.flowable.engine.impl.cmd.ValidateV5EntitiesCmd - Total of v5 deployments found: 0
2025-07-30 11:18:11.464 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-07-30 11:18:14.209 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-30 11:18:14.308 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 11:18:14.308 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-30 11:18:14.324 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-30 11:18:14.331 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 11:18:14.331 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 11:18:14.331 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-30 11:18:14.331 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@51bb94c3
2025-07-30 11:18:15.569 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-30 11:18:15.569 [main] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main
2025-07-30 11:18:15.570 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 11:18:15.570 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-30 11:18:15.571 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-30 11:18:15.571 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 11:18:15.571 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-30 11:18:15.571 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-30 11:18:15.641 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-30 11:18:19.706 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-07-30 11:18:19.764 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-07-30 11:18:19.767 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-07-30 11:18:19.845 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-07-30 11:18:19.871 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-07-30 11:18:19.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-07-30 11:18:19.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-07-30 11:18:19.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-07-30 11:18:19.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-07-30 11:18:19.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-07-30 11:18:19.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-07-30 11:18:19.878 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-07-30 11:18:19.885 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-07-30 11:18:19.885 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-07-30 11:18:19.958 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册请求拦截器：class com.sanyth.portal.web.filter.MagicApiRequestInterceptor
2025-07-30 11:18:19.977 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 11:18:20.755 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-07-30 11:18:20.827 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-07-30 11:18:20.831 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-30 11:18:20.831 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a0d1d01]]
2025-07-30 11:18:20.831 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-30 11:18:20.831 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-07-30 11:18:20.832 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 11:18:20.849 [main] INFO  com.sanyth.portal.Main - Started Main in 64.553 seconds (JVM running for 71.536)
2025-07-30 11:18:26.044 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-07-30 11:18:26.123 [main] INFO  com.sanyth.portal.web.runner.InitRunner - 初始化日程提醒....
2025-07-30 11:18:26.862 [main] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:18:37.007 [cluster-ClusterId{value='68898ec54e67504af0f09edf', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 10 common frames omitted
2025-07-30 11:18:40.760 [cluster-ClusterId{value='68898ec54e67504af0f09edf', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:18:56.907 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 11:18:57.007 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-30 11:18:57.007 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4a0d1d01]]
2025-07-30 11:18:57.007 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-30 11:18:57.007 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 11:18:57.174 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-07-30 11:18:57.174 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 11:18:57.174 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 11:18:57.175 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 11:18:58.016 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:18:58.026 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown initiated...
2025-07-30 11:18:59.797 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown completed.
2025-07-30 11:23:55.423 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 11:23:55.793 [main] INFO  com.sanyth.portal.Main - Starting Main using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 37923 (/Users/<USER>/WorkSpace/sanyth-portal/git-sanyth-portal/portal_web/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-portal)
2025-07-30 11:23:55.794 [main] INFO  com.sanyth.portal.Main - The following 1 profile is active: "loc"
2025-07-30 11:23:58.247 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:23:58.250 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:23:58.521 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository, org.springframework.data.elasticsearch.repository.ElasticsearchRepository.
2025-07-30 11:23:58.567 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 305 ms. Found 2 Elasticsearch repository interfaces.
2025-07-30 11:23:58.573 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:23:58.574 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:23:58.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Reactive Elasticsearch - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Reactive Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository.
2025-07-30 11:23:58.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 11:23:58.625 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:23:58.626 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 11:23:58.662 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:23:58.664 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:23:58.665 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: javax.persistence.Entity, javax.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository.
2025-07-30 11:23:58.665 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.
2025-07-30 11:23:58.670 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:23:58.671 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-07-30 11:23:58.699 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 11:23:58.700 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data LDAP - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a LDAP repository, consider annotating your entities with one of these annotations: org.springframework.ldap.odm.annotations.Entry (preferred), or consider extending one of the following types with your repository: org.springframework.data.ldap.repository.LdapRepository.
2025-07-30 11:23:58.722 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50 ms. Found 1 LDAP repository interfaces.
2025-07-30 11:23:58.729 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:23:58.730 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-30 11:23:58.756 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:23:58.756 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:23:58.757 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-07-30 11:23:58.757 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 MongoDB repository interfaces.
2025-07-30 11:23:58.773 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 11:23:58.776 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:23:58.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrConsultationRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:23:58.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.SytZnjqrQuestionRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:23:58.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.portal.service.ldap.service.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-30 11:23:58.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-30 11:23:59.540 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:23:59.557 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:23:59.580 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 11:24:00.190 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8181 (http)
2025-07-30 11:24:00.224 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8181"]
2025-07-30 11:24:00.224 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:24:00.225 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 11:24:00.427 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:24:00.428 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4516 ms
2025-07-30 11:24:00.895 [main] INFO  org.redisson.Version - Redisson 3.17.3
2025-07-30 11:24:01.759 [redisson-netty-2-9] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:6380
2025-07-30 11:24:03.452 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for *************/*************:6380
2025-07-30 11:24:04.129 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-07-30 11:24:04.522 [cluster-ClusterId{value='688990545ebc2609db70589d', description='null'}-*************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:24:04.868 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:24:04.869 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:24:04.870 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-07-30 11:24:05.519 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 11:24:05.673 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.33
2025-07-30 11:24:06.227 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 11:24:06.551 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-07-30 11:24:09.592 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-07-30 11:24:09.683 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-07-30 11:24:23.873 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-30 11:24:23.940 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:24:32.410 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Spring Data Elasticsearch: 4.2.10
2025-07-30 11:24:32.410 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client in build: 7.12.1
2025-07-30 11:24:32.410 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch Client used: 7.12.1
2025-07-30 11:24:32.410 [main] INFO  org.springframework.data.elasticsearch.support.VersionInfo - Version Elasticsearch cluster: 7.12.1
2025-07-30 11:24:33.483 [main] INFO  org.flowable.spring.boot.eventregistry.EventRegistryAutoConfiguration - No deployment resources were found for autodeployment
2025-07-30 11:24:34.510 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Found 2 Engine Configurators in total:
2025-07-30 11:24:34.510 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 11:24:34.510 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 11:24:34.510 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 11:24:34.516 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 11:24:37.504 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
2025-07-30 11:24:39.202 [main] INFO  liquibase.database - Could not set remarks reporting on OracleDatabase: com.sun.proxy.$Proxy231.setRemarksReporting(boolean)
2025-07-30 11:24:42.320 [main] INFO  liquibase.changelog - Reading from SYT_PORTAL.FLW_EV_DATABASECHANGELOG
2025-07-30 11:24:43.180 [main] INFO  org.flowable.eventregistry.impl.EventRegistryEngineImpl - EventRegistryEngine default created
2025-07-30 11:24:43.185 [main] INFO  org.flowable.spring.SpringProcessEngineConfiguration - Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
2025-07-30 11:24:44.344 [main] INFO  org.flowable.idm.engine.impl.IdmEngineImpl - IdmEngine default created
2025-07-30 11:24:45.584 [main] INFO  org.flowable.engine.impl.ProcessEngineImpl - ProcessEngine default created
2025-07-30 11:24:45.954 [main] INFO  org.flowable.engine.impl.cmd.ValidateV5EntitiesCmd - Total of v5 deployments found: 0
2025-07-30 11:24:48.610 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-07-30 11:24:51.320 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-30 11:24:51.420 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 11:24:51.420 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-30 11:24:51.436 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-30 11:24:51.443 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 11:24:51.443 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 11:24:51.443 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-30 11:24:51.443 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@b1321b2
2025-07-30 11:24:52.604 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-30 11:24:52.604 [main] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main
2025-07-30 11:24:52.604 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 11:24:52.604 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-30 11:24:52.605 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-30 11:24:52.605 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 11:24:52.605 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-30 11:24:52.605 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-30 11:24:52.676 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-07-30 11:24:56.803 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-07-30 11:24:56.863 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-07-30 11:24:56.867 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-07-30 11:24:56.948 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-07-30 11:24:56.975 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-07-30 11:24:56.980 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-07-30 11:24:56.980 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-07-30 11:24:56.980 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-07-30 11:24:56.980 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-07-30 11:24:56.980 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-07-30 11:24:56.980 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-07-30 11:24:56.981 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-07-30 11:24:56.989 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-07-30 11:24:56.989 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-07-30 11:24:57.068 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册请求拦截器：class com.sanyth.portal.web.filter.MagicApiRequestInterceptor
2025-07-30 11:24:57.087 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 11:24:57.906 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8181"]
2025-07-30 11:24:57.987 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8181 (http) with context path ''
2025-07-30 11:24:57.991 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-30 11:24:57.992 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@32a93b9b]]
2025-07-30 11:24:57.992 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-30 11:24:57.992 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-07-30 11:24:57.992 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 11:24:58.012 [main] INFO  com.sanyth.portal.Main - Started Main in 63.5 seconds (JVM running for 71.524)
2025-07-30 11:25:02.353 [http-nio-8181-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:25:02.354 [http-nio-8181-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 11:25:02.357 [http-nio-8181-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-30 11:25:02.437 [http-nio-8181-exec-1] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/src/styles/assets/iconfont/iconfont.css
2025-07-30 11:25:02.442 [http-nio-8181-exec-1] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:02.628 [http-nio-8181-exec-1] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:03.068 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-07-30 11:25:03.268 [main] INFO  com.sanyth.portal.web.runner.InitRunner - 初始化日程提醒....
2025-07-30 11:25:03.952 [http-nio-8181-exec-2] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - POST >>> http://localhost:8080/sytSysParam/getNoLogin
2025-07-30 11:25:03.952 [http-nio-8181-exec-7] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - POST >>> http://localhost:8080/sytSysParam/getNoLogin
2025-07-30 11:25:03.952 [http-nio-8181-exec-6] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - POST >>> http://localhost:8080/sytSysParam/getNoLogin
2025-07-30 11:25:03.952 [http-nio-8181-exec-2] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:03.952 [http-nio-8181-exec-7] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:03.952 [http-nio-8181-exec-6] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:03.953 [http-nio-8181-exec-6] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:03.953 [http-nio-8181-exec-7] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:03.953 [http-nio-8181-exec-2] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:04.496 [http-nio-8181-exec-7] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:04.496 [main] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:04.496 [http-nio-8181-exec-2] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:04.496 [http-nio-8181-exec-6] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:25.614 [http-nio-8181-exec-4] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/.well-known/appspecific/com.chrome.devtools.json
2025-07-30 11:25:25.615 [http-nio-8181-exec-4] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:25.628 [http-nio-8181-exec-4] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:25.649 [http-nio-8181-exec-3] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/src/styles/assets/iconfont/iconfont.css
2025-07-30 11:25:25.649 [http-nio-8181-exec-3] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:25.652 [http-nio-8181-exec-3] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:26.301 [http-nio-8181-exec-9] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/cdn/axios/1.0.0/axios.min.map
2025-07-30 11:25:26.301 [http-nio-8181-exec-9] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:26.302 [http-nio-8181-exec-5] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/cdn/sockjs/sockjs.min.js.map
2025-07-30 11:25:26.302 [http-nio-8181-exec-5] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:26.303 [http-nio-8181-exec-9] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:26.304 [http-nio-8181-exec-5] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:29.813 [http-nio-8181-exec-10] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/.well-known/appspecific/com.chrome.devtools.json
2025-07-30 11:25:29.813 [http-nio-8181-exec-10] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:29.816 [http-nio-8181-exec-1] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/src/styles/assets/iconfont/iconfont.css
2025-07-30 11:25:29.816 [http-nio-8181-exec-1] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:29.817 [http-nio-8181-exec-10] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:29.819 [http-nio-8181-exec-1] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:30.032 [http-nio-8181-exec-4] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/cdn/axios/1.0.0/axios.min.map
2025-07-30 11:25:30.032 [http-nio-8181-exec-4] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:30.034 [http-nio-8181-exec-4] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:30.058 [http-nio-8181-exec-3] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - GET >>> http://localhost:8080/cdn/sockjs/sockjs.min.js.map
2025-07-30 11:25:30.058 [http-nio-8181-exec-3] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:30.060 [http-nio-8181-exec-3] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:30.638 [http-nio-8181-exec-9] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - POST >>> http://localhost:8080/sytSysParam/getNoLogin
2025-07-30 11:25:30.638 [http-nio-8181-exec-5] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - POST >>> http://localhost:8080/sytSysParam/getNoLogin
2025-07-30 11:25:30.638 [http-nio-8181-exec-10] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - POST >>> http://localhost:8080/sytSysParam/getNoLogin
2025-07-30 11:25:30.638 [http-nio-8181-exec-5] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:30.638 [http-nio-8181-exec-9] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:30.638 [http-nio-8181-exec-10] INFO  com.sanyth.portal.web.filter.AuthenticationFilter - time:0ms
2025-07-30 11:25:30.639 [http-nio-8181-exec-5] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:30.639 [http-nio-8181-exec-9] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:30.639 [http-nio-8181-exec-10] INFO  com.sanyth.portal.web.filter.PermissionInterceptor - token, null
2025-07-30 11:25:30.642 [http-nio-8181-exec-10] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:30.642 [http-nio-8181-exec-9] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:30.642 [http-nio-8181-exec-5] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-07-30 11:25:34.533 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 11:25:34.690 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-30 11:25:34.691 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@32a93b9b]]
2025-07-30 11:25:34.691 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-30 11:25:34.691 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 11:25:37.153 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-07-30 11:25:37.153 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 11:25:37.154 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 11:25:37.154 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 11:25:37.971 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:25:37.982 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown initiated...
2025-07-30 11:25:39.714 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown completed.

2025-07-30 08:49:33.606 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 08:53:35.077 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 08:53:38.018 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 08:53:38.220 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 08:53:40.010 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-30 08:53:40.021 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 08:53:44.877 [main] WARN  org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
java.sql.SQLRecoverableException: IO 错误: Got minus one from a read call, connect lapse 3102 ms., Authentication lapse 0 ms.
	at oracle.jdbc.driver.T4CConnection.handleLogonIOException(T4CConnection.java:913)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:678)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1032)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:90)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:681)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:602)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:180)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214)
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1255)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:583)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:448)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1365)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at com.sanyth.portal.Main.main(Main.java:30)
Caused by: java.io.IOException: Got minus one from a read call, connect lapse 3102 ms., Authentication lapse 0 ms.
	at oracle.jdbc.driver.T4CConnection.handleLogonIOException(T4CConnection.java:908)
	... 56 common frames omitted
Caused by: java.io.IOException: Got minus one from a read call, connect lapse 3102 ms.
	at oracle.net.ns.NSProtocolNIO.negotiateConnection(NSProtocolNIO.java:202)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:350)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:1967)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:640)
	... 55 common frames omitted
Caused by: oracle.net.ns.NetException: Got minus one from a read call
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1121)
	at oracle.net.ns.NIOPacket.readNIOPacket(NIOPacket.java:418)
	at oracle.net.ns.NSProtocolNIO.negotiateConnection(NSProtocolNIO.java:167)
	... 58 common frames omitted
2025-07-30 08:53:48.343 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 08:53:55.885 [main] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 17002, SQLState: 08006
2025-07-30 08:53:55.937 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
2025-07-30 09:09:11.951 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 09:09:15.110 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 09:09:15.309 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 09:09:17.555 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-30 09:09:17.571 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 09:09:51.241 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 09:09:54.375 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 09:09:54.629 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 09:09:56.538 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-30 09:09:56.550 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 09:10:18.335 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionAccount",So @TableField annotation will not work!
2025-07-30 09:10:18.409 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResource",So @TableField annotation will not work!
2025-07-30 09:10:18.434 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResourceRole",So @TableField annotation will not work!
2025-07-30 09:10:18.636 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.portal.model.SytSysOrganizationUser".
2025-07-30 09:10:18.636 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.portal.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 09:10:18.655 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCategory",So @TableField annotation will not work!
2025-07-30 09:10:18.674 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmForm",So @TableField annotation will not work!
2025-07-30 09:10:18.693 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessDefinitionExt",So @TableField annotation will not work!
2025-07-30 09:10:18.718 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessInstanceExt",So @TableField annotation will not work!
2025-07-30 09:10:18.733 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskAssignRule",So @TableField annotation will not work!
2025-07-30 09:10:18.754 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApply",So @TableField annotation will not work!
2025-07-30 09:10:19.629 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytAddressBook",So @TableField annotation will not work!
2025-07-30 09:10:19.729 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytApplet",So @TableField annotation will not work!
2025-07-30 09:10:20.774 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.model.SytElectronicSeal",So @TableField will not work!
2025-07-30 09:10:20.858 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytFileResource",So @TableField annotation will not work!
2025-07-30 09:10:20.945 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytHallStatistics",So @TableField annotation will not work!
2025-07-30 09:10:21.035 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawAwards",So @TableField annotation will not work!
2025-07-30 09:10:21.130 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawParticipator",So @TableField annotation will not work!
2025-07-30 09:10:21.426 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDraw",So @TableField annotation will not work!
2025-07-30 09:10:21.512 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessage",So @TableField annotation will not work!
2025-07-30 09:10:21.718 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessageSend",So @TableField annotation will not work!
2025-07-30 09:10:21.857 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMobileNavigation",So @TableField annotation will not work!
2025-07-30 09:10:22.641 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsBlock",So @TableField annotation will not work!
2025-07-30 09:10:22.752 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsTab",So @TableField annotation will not work!
2025-07-30 09:10:25.592 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxRelpy",So @TableField annotation will not work!
2025-07-30 09:10:25.621 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxQuestion",So @TableField annotation will not work!
2025-07-30 09:10:25.709 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxZxflb",So @TableField annotation will not work!
2025-07-30 09:10:27.191 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskExt",So @TableField annotation will not work!
2025-07-30 09:10:27.229 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCommentTemplate",So @TableField annotation will not work!
2025-07-30 09:10:42.247 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFormField",So @TableField annotation will not work!
2025-07-30 09:10:42.489 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmModelExt",So @TableField annotation will not work!
2025-07-30 09:10:43.371 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.bpm.model.BpmFormVariable",So @TableField will not work!
2025-07-30 09:10:43.501 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCustomColumn",So @TableField annotation will not work!
2025-07-30 09:10:44.051 [main] WARN  org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 09:10:44.990 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFlowListenerParam",So @TableField annotation will not work!
2025-07-30 09:10:45.095 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFlowListener",So @TableField annotation will not work!
2025-07-30 09:10:45.402 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskButton",So @TableField annotation will not work!
2025-07-30 09:10:45.567 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApplyTime",So @TableField annotation will not work!
2025-07-30 09:10:45.698 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourProject",So @TableField annotation will not work!
2025-07-30 09:10:45.842 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourPerson",So @TableField annotation will not work!
2025-07-30 09:10:46.146 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskProject",So @TableField annotation will not work!
2025-07-30 09:10:46.219 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskPerson",So @TableField annotation will not work!
2025-07-30 09:10:46.727 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskFillRecord",So @TableField annotation will not work!
2025-07-30 09:10:46.902 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.dispatchcenter.model.SytSendRecord",So @TableField annotation will not work!
2025-07-30 09:10:49.798 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-07-30 09:10:49.827 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-30 11:16:48.065 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 11:16:51.311 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 11:16:51.518 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 11:16:54.524 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-30 11:16:54.536 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 11:16:56.068 [main] WARN  org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
java.sql.SQLRecoverableException: IO 错误: The Network Adapter could not establish the connection (CONNECTION_ID=cDOaXiv5QkqgpwgxOF2U4w==)
	at oracle.jdbc.driver.T4CConnection.handleLogonNetException(T4CConnection.java:870)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:675)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1032)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:90)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:681)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:602)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:180)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:35)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:101)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:237)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214)
	at org.hibernate.id.factory.internal.DefaultIdentifierGeneratorFactory.injectServices(DefaultIdentifierGeneratorFactory.java:152)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.injectDependencies(AbstractServiceRegistryImpl.java:286)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:243)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:214)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:176)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:127)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1224)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1255)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1154)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:908)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:583)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:448)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1365)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at com.sanyth.portal.Main.main(Main.java:30)
Caused by: oracle.net.ns.NetException: The Network Adapter could not establish the connection (CONNECTION_ID=cDOaXiv5QkqgpwgxOF2U4w==)
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:594)
	at oracle.net.resolver.AddrResolution.resolveAndExecute(AddrResolution.java:565)
	at oracle.net.ns.NSProtocol.establishConnection(NSProtocol.java:937)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:346)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:1967)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:640)
	... 55 common frames omitted
Caused by: java.io.IOException: Malformed reply from SOCKS server, socket connect lapse 7 ms. localhost 1521 Proxy = SOCKS @ /127.0.0.1:6153 60000 (2/2) true
	at oracle.net.nt.TcpNTAdapter.establishSocket(TcpNTAdapter.java:421)
	at oracle.net.nt.TcpNTAdapter.doLocalDNSLookupConnect(TcpNTAdapter.java:303)
	at oracle.net.nt.TcpNTAdapter.connect(TcpNTAdapter.java:265)
	at oracle.net.nt.ConnOption.connect(ConnOption.java:229)
	at oracle.net.nt.ConnStrategy.executeConnOption(ConnStrategy.java:814)
	at oracle.net.nt.ConnStrategy.execute(ConnStrategy.java:555)
	... 60 common frames omitted
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at oracle.net.nt.ProxyHelper.read(ProxyHelper.java:355)
	at oracle.net.nt.ProxyHelper.doSOCKSConnect(ProxyHelper.java:203)
	at oracle.net.nt.ProxyHelper.doSOCKSTunneling(ProxyHelper.java:148)
	at oracle.net.nt.ProxyHelper.connectViaProxy(ProxyHelper.java:83)
	at oracle.net.nt.TimeoutSocketChannel.connect(TimeoutSocketChannel.java:188)
	at oracle.net.nt.TimeoutSocketChannel.<init>(TimeoutSocketChannel.java:158)
	at oracle.net.nt.TcpNTAdapter.establishSocket(TcpNTAdapter.java:380)
	... 65 common frames omitted
2025-07-30 11:16:59.699 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 11:17:00.743 [main] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 17002, SQLState: 08006
2025-07-30 11:17:00.824 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
2025-07-30 11:17:22.705 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 11:17:26.033 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 11:17:26.304 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 11:17:28.486 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-30 11:17:28.498 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 11:17:46.883 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionAccount",So @TableField annotation will not work!
2025-07-30 11:17:46.937 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResource",So @TableField annotation will not work!
2025-07-30 11:17:46.956 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResourceRole",So @TableField annotation will not work!
2025-07-30 11:17:47.171 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.portal.model.SytSysOrganizationUser".
2025-07-30 11:17:47.171 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.portal.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 11:17:47.190 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCategory",So @TableField annotation will not work!
2025-07-30 11:17:47.210 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmForm",So @TableField annotation will not work!
2025-07-30 11:17:47.229 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessDefinitionExt",So @TableField annotation will not work!
2025-07-30 11:17:47.253 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessInstanceExt",So @TableField annotation will not work!
2025-07-30 11:17:47.268 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskAssignRule",So @TableField annotation will not work!
2025-07-30 11:17:47.287 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApply",So @TableField annotation will not work!
2025-07-30 11:17:48.108 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytAddressBook",So @TableField annotation will not work!
2025-07-30 11:17:48.229 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytApplet",So @TableField annotation will not work!
2025-07-30 11:17:49.311 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.model.SytElectronicSeal",So @TableField will not work!
2025-07-30 11:17:49.395 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytFileResource",So @TableField annotation will not work!
2025-07-30 11:17:49.479 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytHallStatistics",So @TableField annotation will not work!
2025-07-30 11:17:49.566 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawAwards",So @TableField annotation will not work!
2025-07-30 11:17:49.644 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawParticipator",So @TableField annotation will not work!
2025-07-30 11:17:49.930 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDraw",So @TableField annotation will not work!
2025-07-30 11:17:50.017 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessage",So @TableField annotation will not work!
2025-07-30 11:17:50.230 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessageSend",So @TableField annotation will not work!
2025-07-30 11:17:50.375 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMobileNavigation",So @TableField annotation will not work!
2025-07-30 11:17:51.069 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsBlock",So @TableField annotation will not work!
2025-07-30 11:17:51.172 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsTab",So @TableField annotation will not work!
2025-07-30 11:17:53.780 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxRelpy",So @TableField annotation will not work!
2025-07-30 11:17:53.808 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxQuestion",So @TableField annotation will not work!
2025-07-30 11:17:53.893 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxZxflb",So @TableField annotation will not work!
2025-07-30 11:17:55.159 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskExt",So @TableField annotation will not work!
2025-07-30 11:17:55.193 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCommentTemplate",So @TableField annotation will not work!
2025-07-30 11:18:09.795 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFormField",So @TableField annotation will not work!
2025-07-30 11:18:10.031 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmModelExt",So @TableField annotation will not work!
2025-07-30 11:18:10.988 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.bpm.model.BpmFormVariable",So @TableField will not work!
2025-07-30 11:18:11.127 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCustomColumn",So @TableField annotation will not work!
2025-07-30 11:18:11.756 [main] WARN  org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 11:18:12.736 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFlowListenerParam",So @TableField annotation will not work!
2025-07-30 11:18:12.837 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFlowListener",So @TableField annotation will not work!
2025-07-30 11:18:13.139 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskButton",So @TableField annotation will not work!
2025-07-30 11:18:13.291 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApplyTime",So @TableField annotation will not work!
2025-07-30 11:18:13.419 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourProject",So @TableField annotation will not work!
2025-07-30 11:18:13.570 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourPerson",So @TableField annotation will not work!
2025-07-30 11:18:13.874 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskProject",So @TableField annotation will not work!
2025-07-30 11:18:13.922 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskPerson",So @TableField annotation will not work!
2025-07-30 11:18:14.421 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskFillRecord",So @TableField annotation will not work!
2025-07-30 11:18:14.584 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.dispatchcenter.model.SytSendRecord",So @TableField annotation will not work!
2025-07-30 11:18:17.364 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-07-30 11:18:17.393 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-30 11:24:00.968 [main] WARN  io.netty.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS.
2025-07-30 11:24:04.391 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 11:24:04.603 [main] WARN  org.springframework.data.convert.CustomConversions - Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type! You might want to check your annotation setup at the converter implementation.
2025-07-30 11:24:06.551 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-30 11:24:06.563 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-07-30 11:24:26.119 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionAccount",So @TableField annotation will not work!
2025-07-30 11:24:26.164 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResource",So @TableField annotation will not work!
2025-07-30 11:24:26.182 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytPermissionResourceRole",So @TableField annotation will not work!
2025-07-30 11:24:26.372 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.portal.model.SytSysOrganizationUser".
2025-07-30 11:24:26.373 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.portal.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-30 11:24:26.391 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCategory",So @TableField annotation will not work!
2025-07-30 11:24:26.410 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmForm",So @TableField annotation will not work!
2025-07-30 11:24:26.427 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessDefinitionExt",So @TableField annotation will not work!
2025-07-30 11:24:26.449 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmProcessInstanceExt",So @TableField annotation will not work!
2025-07-30 11:24:26.464 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskAssignRule",So @TableField annotation will not work!
2025-07-30 11:24:26.486 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApply",So @TableField annotation will not work!
2025-07-30 11:24:27.343 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytAddressBook",So @TableField annotation will not work!
2025-07-30 11:24:27.442 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytApplet",So @TableField annotation will not work!
2025-07-30 11:24:28.464 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.model.SytElectronicSeal",So @TableField will not work!
2025-07-30 11:24:28.559 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytFileResource",So @TableField annotation will not work!
2025-07-30 11:24:28.643 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytHallStatistics",So @TableField annotation will not work!
2025-07-30 11:24:28.730 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawAwards",So @TableField annotation will not work!
2025-07-30 11:24:28.812 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDrawParticipator",So @TableField annotation will not work!
2025-07-30 11:24:29.100 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytLuckyDraw",So @TableField annotation will not work!
2025-07-30 11:24:29.187 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessage",So @TableField annotation will not work!
2025-07-30 11:24:29.410 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMessageSend",So @TableField annotation will not work!
2025-07-30 11:24:29.555 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytMobileNavigation",So @TableField annotation will not work!
2025-07-30 11:24:30.222 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsBlock",So @TableField annotation will not work!
2025-07-30 11:24:30.326 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytStatisticsTab",So @TableField annotation will not work!
2025-07-30 11:24:32.855 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxRelpy",So @TableField annotation will not work!
2025-07-30 11:24:32.880 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxQuestion",So @TableField annotation will not work!
2025-07-30 11:24:32.970 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.model.SytZxzxZxflb",So @TableField annotation will not work!
2025-07-30 11:24:34.192 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskExt",So @TableField annotation will not work!
2025-07-30 11:24:34.243 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCommentTemplate",So @TableField annotation will not work!
2025-07-30 11:24:47.096 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFormField",So @TableField annotation will not work!
2025-07-30 11:24:47.343 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmModelExt",So @TableField annotation will not work!
2025-07-30 11:24:48.241 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.sanyth.portal.bpm.model.BpmFormVariable",So @TableField will not work!
2025-07-30 11:24:48.354 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmCustomColumn",So @TableField annotation will not work!
2025-07-30 11:24:48.894 [main] WARN  org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 11:24:49.842 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFlowListenerParam",So @TableField annotation will not work!
2025-07-30 11:24:49.943 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmFlowListener",So @TableField annotation will not work!
2025-07-30 11:24:50.250 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.bpm.model.BpmTaskButton",So @TableField annotation will not work!
2025-07-30 11:24:50.403 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourApplyTime",So @TableField annotation will not work!
2025-07-30 11:24:50.535 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourProject",So @TableField annotation will not work!
2025-07-30 11:24:50.685 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.publicres.model.SytResourPerson",So @TableField annotation will not work!
2025-07-30 11:24:50.990 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskProject",So @TableField annotation will not work!
2025-07-30 11:24:51.032 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskPerson",So @TableField annotation will not work!
2025-07-30 11:24:51.536 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.taskfill.model.SytTaskFillRecord",So @TableField annotation will not work!
2025-07-30 11:24:51.699 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.portal.dispatchcenter.model.SytSendRecord",So @TableField annotation will not work!
2025-07-30 11:24:54.450 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-07-30 11:24:54.480 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-30 11:25:36.964 [http-nio-8181-exec-10] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.sanyth.portal.web.handler.ExceptionHandler#handle(Exception, WebRequest)
java.lang.NullPointerException: null
	at org.springframework.web.util.UrlPathHelper.getSanitizedPath(UrlPathHelper.java:408)
	at org.springframework.web.util.UrlPathHelper.decodeAndCleanUriString(UrlPathHelper.java:551)
	at org.springframework.web.util.UrlPathHelper.getOriginatingRequestUri(UrlPathHelper.java:496)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.addContentDispositionHeader(AbstractMessageConverterMethodProcessor.java:431)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:288)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.sanyth.portal.web.filter.AuthenticationFilter.doFilter(AuthenticationFilter.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.sanyth.portal.web.filter.EncodingFilter.doFilter(EncodingFilter.java:22)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.ssssssss.magicapi.core.config.MagicCorsFilter.doFilter(MagicCorsFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:769)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:25:36.964 [http-nio-8181-exec-9] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.sanyth.portal.web.handler.ExceptionHandler#handle(Exception, WebRequest)
java.lang.NullPointerException: null
	at org.springframework.web.util.UrlPathHelper.getSanitizedPath(UrlPathHelper.java:408)
	at org.springframework.web.util.UrlPathHelper.decodeAndCleanUriString(UrlPathHelper.java:551)
	at org.springframework.web.util.UrlPathHelper.getOriginatingRequestUri(UrlPathHelper.java:496)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.addContentDispositionHeader(AbstractMessageConverterMethodProcessor.java:431)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:288)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.sanyth.portal.web.filter.AuthenticationFilter.doFilter(AuthenticationFilter.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.sanyth.portal.web.filter.EncodingFilter.doFilter(EncodingFilter.java:22)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.ssssssss.magicapi.core.config.MagicCorsFilter.doFilter(MagicCorsFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:769)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:25:36.964 [http-nio-8181-exec-5] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.sanyth.portal.web.handler.ExceptionHandler#handle(Exception, WebRequest)
java.lang.NullPointerException: null
	at org.springframework.web.util.UrlPathHelper.getSanitizedPath(UrlPathHelper.java:408)
	at org.springframework.web.util.UrlPathHelper.decodeAndCleanUriString(UrlPathHelper.java:551)
	at org.springframework.web.util.UrlPathHelper.getOriginatingRequestUri(UrlPathHelper.java:496)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.addContentDispositionHeader(AbstractMessageConverterMethodProcessor.java:431)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:288)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.sanyth.portal.web.filter.AuthenticationFilter.doFilter(AuthenticationFilter.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.sanyth.portal.web.filter.EncodingFilter.doFilter(EncodingFilter.java:22)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.ssssssss.magicapi.core.config.MagicCorsFilter.doFilter(MagicCorsFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:769)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:25:37.098 [http-nio-8181-exec-10] WARN  org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter - Failed to record timer metrics
java.lang.NullPointerException: null
	at java.util.Objects.requireNonNull(Objects.java:203)
	at io.micrometer.core.instrument.ImmutableTag.<init>(ImmutableTag.java:35)
	at io.micrometer.core.instrument.Tag.of(Tag.java:29)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcTags.method(WebMvcTags.java:74)
	at org.springframework.boot.actuate.metrics.web.servlet.DefaultWebMvcTagsProvider.getTags(DefaultWebMvcTagsProvider.java:74)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.getTimer(WebMvcMetricsFilter.java:161)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.lambda$record$0(WebMvcMetricsFilter.java:139)
	at org.springframework.boot.actuate.metrics.AutoTimer.apply(AutoTimer.java:109)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.record(WebMvcMetricsFilter.java:138)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:103)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.ssssssss.magicapi.core.config.MagicCorsFilter.doFilter(MagicCorsFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:769)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:25:37.099 [http-nio-8181-exec-9] WARN  org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter - Failed to record timer metrics
java.lang.NullPointerException: null
	at java.util.Objects.requireNonNull(Objects.java:203)
	at io.micrometer.core.instrument.ImmutableTag.<init>(ImmutableTag.java:35)
	at io.micrometer.core.instrument.Tag.of(Tag.java:29)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcTags.method(WebMvcTags.java:74)
	at org.springframework.boot.actuate.metrics.web.servlet.DefaultWebMvcTagsProvider.getTags(DefaultWebMvcTagsProvider.java:74)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.getTimer(WebMvcMetricsFilter.java:161)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.lambda$record$0(WebMvcMetricsFilter.java:139)
	at org.springframework.boot.actuate.metrics.AutoTimer.apply(AutoTimer.java:109)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.record(WebMvcMetricsFilter.java:138)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:103)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.ssssssss.magicapi.core.config.MagicCorsFilter.doFilter(MagicCorsFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:769)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-30 11:25:37.098 [http-nio-8181-exec-5] WARN  org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter - Failed to record timer metrics
java.lang.NullPointerException: null
	at java.util.Objects.requireNonNull(Objects.java:203)
	at io.micrometer.core.instrument.ImmutableTag.<init>(ImmutableTag.java:35)
	at io.micrometer.core.instrument.Tag.of(Tag.java:29)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcTags.method(WebMvcTags.java:74)
	at org.springframework.boot.actuate.metrics.web.servlet.DefaultWebMvcTagsProvider.getTags(DefaultWebMvcTagsProvider.java:74)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.getTimer(WebMvcMetricsFilter.java:161)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.lambda$record$0(WebMvcMetricsFilter.java:139)
	at org.springframework.boot.actuate.metrics.AutoTimer.apply(AutoTimer.java:109)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.record(WebMvcMetricsFilter.java:138)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:103)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.ssssssss.magicapi.core.config.MagicCorsFilter.doFilter(MagicCorsFilter.java:41)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:769)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

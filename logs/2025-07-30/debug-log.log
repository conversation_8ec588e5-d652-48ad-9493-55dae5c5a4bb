[2m2025-07-30 09:10:47.346[0;39m [32mDEBUG[0;39m [35m17220[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT id,CATEGORY_ID,CREATE_TIME,CREATE_USER_CODE,CREATE_USER_NAME,SORT,NAME,REMARK,START_TIME,END_TIME,PROCESS_DEFINITION_KEY,FORM_ID,STYLE,RUN_STATUS,CRON,IS_STYLE,CRON_RUN,PARENT_ID,REMIND_MESSAGE,ADMIN_USER_CODE,ADMIN_USER_NAME FROM SYT_TASK_PROJECT WHERE (IS_STYLE IN (?) AND CRON_RUN IN (?)) ORDER BY RUN_STATUS DESC,CREATE_TIME DESC
[2m2025-07-30 09:10:47.377[0;39m [32mDEBUG[0;39m [35m17220[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m ==> Parameters: 1(String), 1(String)
[2m2025-07-30 09:10:47.896[0;39m [32mDEBUG[0;39m [35m17220[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 09:10:48.024[0;39m [32mDEBUG[0;39m [35m17220[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID,NAME,DESCRIPTION,ENABLEFLAG,CREATETIME,CREATEUSER,CHECKCRON,CONTENT,PUSHCHANNEL,ROLES,ORGANIZATIONS,DATASOURCE,MORE_URL,USER_CODE,USER_NAME FROM SYT_DISPATCHING_CENTER WHERE (ENABLEFLAG = ?) ORDER BY CREATETIME DESC
[2m2025-07-30 09:10:48.024[0;39m [32mDEBUG[0;39m [35m17220[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-30 09:10:48.255[0;39m [32mDEBUG[0;39m [35m17220[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 11:18:15.043[0;39m [32mDEBUG[0;39m [35m36674[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT id,CATEGORY_ID,CREATE_TIME,CREATE_USER_CODE,CREATE_USER_NAME,SORT,NAME,REMARK,START_TIME,END_TIME,PROCESS_DEFINITION_KEY,FORM_ID,STYLE,RUN_STATUS,CRON,IS_STYLE,CRON_RUN,PARENT_ID,REMIND_MESSAGE,ADMIN_USER_CODE,ADMIN_USER_NAME FROM SYT_TASK_PROJECT WHERE (IS_STYLE IN (?) AND CRON_RUN IN (?)) ORDER BY RUN_STATUS DESC,CREATE_TIME DESC
[2m2025-07-30 11:18:15.067[0;39m [32mDEBUG[0;39m [35m36674[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m ==> Parameters: 1(String), 1(String)
[2m2025-07-30 11:18:15.523[0;39m [32mDEBUG[0;39m [35m36674[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:18:15.643[0;39m [32mDEBUG[0;39m [35m36674[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID,NAME,DESCRIPTION,ENABLEFLAG,CREATETIME,CREATEUSER,CHECKCRON,CONTENT,PUSHCHANNEL,ROLES,ORGANIZATIONS,DATASOURCE,MORE_URL,USER_CODE,USER_NAME FROM SYT_DISPATCHING_CENTER WHERE (ENABLEFLAG = ?) ORDER BY CREATETIME DESC
[2m2025-07-30 11:18:15.643[0;39m [32mDEBUG[0;39m [35m36674[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-30 11:18:15.852[0;39m [32mDEBUG[0;39m [35m36674[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 11:24:52.115[0;39m [32mDEBUG[0;39m [35m37923[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT id,CATEGORY_ID,CREATE_TIME,CREATE_USER_CODE,CREATE_USER_NAME,SORT,NAME,REMARK,START_TIME,END_TIME,PROCESS_DEFINITION_KEY,FORM_ID,STYLE,RUN_STATUS,CRON,IS_STYLE,CRON_RUN,PARENT_ID,REMIND_MESSAGE,ADMIN_USER_CODE,ADMIN_USER_NAME FROM SYT_TASK_PROJECT WHERE (IS_STYLE IN (?) AND CRON_RUN IN (?)) ORDER BY RUN_STATUS DESC,CREATE_TIME DESC
[2m2025-07-30 11:24:52.138[0;39m [32mDEBUG[0;39m [35m37923[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m ==> Parameters: 1(String), 1(String)
[2m2025-07-30 11:24:52.559[0;39m [32mDEBUG[0;39m [35m37923[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.t.m.S.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:24:52.678[0;39m [32mDEBUG[0;39m [35m37923[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID,NAME,DESCRIPTION,ENABLEFLAG,CREATETIME,CREATEUSER,CHECKCRON,CONTENT,PUSHCHANNEL,ROLES,ORGANIZATIONS,DATASOURCE,MORE_URL,USER_CODE,USER_NAME FROM SYT_DISPATCHING_CENTER WHERE (ENABLEFLAG = ?) ORDER BY CREATETIME DESC
[2m2025-07-30 11:24:52.679[0;39m [32mDEBUG[0;39m [35m37923[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m ==> Parameters: 0(String)
[2m2025-07-30 11:24:52.876[0;39m [32mDEBUG[0;39m [35m37923[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.d.m.S.selectList                  [0;39m [2m:[0;39m <==      Total: 0

package com.sanyth.portal.platform.qywx.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.sanyth.portal.platform.qywx.enums.QywxParamKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "PLATFORM_QYWX_PARAM")
@TableName("platform_qywx_param")
public class QywxParam extends Model<QywxParam> {

    @Id
    @TableId(type = IdType.INPUT)
    private QywxParamKey key;
    @Column(name = "ENABLED")
    private Boolean enabled;
    @Column(name = "VALUE")
    private String value;
    @Column(name = "UPDATED")
    private Date updated;
//    private Date created;
    @Column(name = "DESCRIPTION")
    private String description;


}

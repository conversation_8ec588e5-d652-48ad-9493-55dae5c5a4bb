// 基础路径 注意发布之前要先修改这里
const path = require("path");
const IS_PROD = process.env.NODE_ENV === 'production'
let baseUrl = './'
function resolve(dir) {
    return path.join(__dirname, dir)
}
const npmConfigArgv = (process.env.npm_config_argv === undefined) ? null : JSON.parse(process.env.npm_config_argv)
let buildProdFlag = false
if (!!npmConfigArgv) {
    npmConfigArgv.original.forEach(cItem => {
        if (cItem === 'build') {
            buildProdFlag = true
        }
    })
}
module.exports = {
    publicPath: './',
    assetsDir: './',

    /* 开启vue运行时模板编译功能！！ */
    runtimeCompiler: true,

    lintOnSave: false,

    productionSourceMap: false,

    /* 指定node_modules目录中需要做babel转译的依赖库 */
    transpileDependencies: [
        'element-ui', 'vuedraggable',
    ],

    css: {
        loaderOptions: {
            scss: {
                /* 自动引入全局scss文件 */
                additionalData: `
          @import "./src/styles/global.scss";
        `
            }
        }
    },
  publicPath: baseUrl, // 根据你的实际情况更改这里
    lintOnSave: true,
    productionSourceMap: false,
    // configureWebpack: {
    //     // if (process.env.NODE_ENV === 'production') {
    //     //     return {
    //     //         plugins: [
    //     //             new BundleAnalyzerPlugin()
    //     //         ]
    //     //     }
    //     // }
    //     plugins: [
    //         new webpack.ProvidePlugin({
    //             $: 'jquery',
    //             jQuery: 'jquery',
    //             'window.jQuery': 'jquery'
    //         })
    //     ]
    // },
    configureWebpack: (config) => {
        config.devtool = 'source-map'
        config.output.libraryExport = 'default'  /* 解决import UMD打包文件时, 组件install方法执行报错的问题！！ */

        if (IS_PROD && buildProdFlag) { /* 仅生产环境使用 */
            /* CDN打包，需要修改index.html加入CDN资源 */
            config.externals = {
                'vue': 'Vue',
                'vue-router': 'VueRouter',
                'element-ui': 'ELEMENT',
                //'quill': 'Quill',
                stompjs: 'Stomp',
                'sockjs-client': 'SockJS'
            }
        }
    },
    chainWebpack: (config) => {
        config.module
            .rule('svg')
            .exclude.add(resolve('src/icons'))
            .end()
        config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: 'icon-[name]'
            })
            .end()
        //忽略的打包文件
        config.externals({
            'vue': 'Vue',
            'vue-router': 'VueRouter',
            'vuex': 'Vuex',
            'axios': 'axios',
            'element-ui': 'ELEMENT',
        })
        const entry = config.entry('app')
        entry
            .add('babel-polyfill')
            .end()
        entry
            .add('classlist-polyfill')
            .end()
        entry
            .add('@/mock')
            .end()

    },
    devServer: {
        // historyApiFallback: true,
        // hot: true,
        // inline: true,
        // progress: true,
        proxy: {
            "/": {
                // 目标 API 地址
                // target: 'http://*************:8288/',
                target: 'http://localhost:8181/',
                ws: true,
                changeOrigin: true,
                secure: false,
                pathRewrite: {
                    '^/': ''
                }
            },
        }
    }
}

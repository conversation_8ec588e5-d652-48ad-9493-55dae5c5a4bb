const {VantResolver} = require('unplugin-vue-components/resolvers');
const ComponentsPlugin = require('unplugin-vue-components/webpack');

module.exports = {
    publicPath: process.env.NODE_ENV === 'production' ? '/mobile/' : '/',
    outputDir: 'portalMobile',//打包后的项目命名
    productionSourceMap: false,

    configureWebpack: {




        plugins: [
            ComponentsPlugin({
                resolvers: [VantResolver()],
            }),
        ],

        module: {
            rules: [
                {
                    test: /\.mjs$/,
                    include: /node_modules/,
                    type: "javascript/auto"
                },
            ]
        }
    },
    devServer: {
        proxy: {
            "/": {
                // 目标 API 地址
                // target: 'http://**************:9033/',
                target: 'http://*************:8288/',
                // target: 'http://xggl.sanyth.com:8181/',
                // target: 'http://**************:8181/',
                // target: 'http://**************:38181/',
                // target: 'http://*************:8181/',
                ws: true,
                changeOrigin: true,
                secure: false,
                pathRewrite: {
                    '^/': ''
                }
            },
        }
    }
};

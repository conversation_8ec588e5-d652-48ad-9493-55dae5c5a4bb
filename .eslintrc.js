module.exports = {
    root: true,
    extends: ['eslint:recommended', 'plugin:vue/essential'],
    env: {
        node: true,
    },
    rules: {
        // 'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        // 'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off'
        'no-console': 'off',
        "vue/no-v-model-argument": "off",
        "vue/no-multiple-template-root": "off",
        'vue/valid-v-model': 'off'
    },
    parserOptions: {
        parser: 'babel-eslint'
    }
};

server:
  port: 8181
  tomcat:
    basedir: tmp
    max-http-form-post-size: -1
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
#    url: ********************************************
#    username: syt_new_portal
#    password: syt_new_portal
#    username: syt_portal_bddl
#    password: syt_portal_bddl
    url: ************************************
    username: syt_portal
    password: Sanyth_2507#Portal
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 10
      maximum-pool-size: 1000
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  redis:
    host: *************
    database: 8
    port: 6380
    password: 123456
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  data:
    mongodb:
#      uri: *****************************************************************
      uri: ************************************************
  elasticsearch:
    rest:
      uris: http://*************:9200
      username: elastic
      password: sanyth123!
      connection-timeout: 10s
      read-timeout: 30s
  servlet:
    multipart:
      max-request-size: 4096MB
      max-file-size: 4096MB
  thymeleaf:
    prefix: classpath:/web/templates/
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
  web:
    resources:
      static-locations: file:/web/dist
  ldap:
    urls: ldap://**************:389
    base: dc=sanyth,dc=cn
    username: cn=admin,dc=sanyth,dc=cn
    password: 123456

ldap:
  switch: false

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

log:
  level: info
  path: logs/
  maxHistory: 1

logging:
  config: classpath:logback-spring.xml
  file:
    path: /log #这里的路径会以logback.xml为主
    name: portal.log  #这里的文件名会以logback.xml为主
  level:
    com.sanyth.portal.mapper: debug
    com.sanyth.portal.bpm.mapper: debug
    com.sanyth.portal.dispatchcenter.mapper: debug
    com.sanyth.portal.platform.qywx.mapper: debug
    com.sanyth.portal.publicres.mapper: debug
    com.sanyth.portal.taskfill.mapper: debug

#sso:
#  clientId: du7Z21Ej3ts60SE4
#  clientSecret: 39Wo2048u8IS24u03594wn5fR5NWY3J4
#  accessTokenUri: http://218.203.237.146:8003/oauth/token
#  userAuthorizationUri: http://218.203.237.146:8003/oauth/authorize
#  userInfoUri: http://218.203.237.146:8003/user/me
#  logoutUri: http://218.203.237.146:8003/logout?service=
#  tokenLogUri: http://218.203.237.146:8003/tokenLogin

sso:
  clientId: Go4295d5tJZ8X38r
  clientSecret: c8VtiwNp0F9690M8115p1IMICn1793P7
  accessTokenUri: http://cxcy.gdjmcmc.edu.cn:8004/oauth/token
  userAuthorizationUri: http://cxcy.gdjmcmc.edu.cn:8004/oauth/authorize
  userInfoUri: http://cxcy.gdjmcmc.edu.cn:8004/user/me
  logoutUri: http://cxcy.gdjmcmc.edu.cn:8004/logout?service=
  tokenLogUri: http://cxcy.gdjmcmc.edu.cn:8004/tokenLogin

# 工作流 Flowable 配置
flowable:
  # 1. false: 默认值，Flowable 启动时，对比数据库表中保存的版本，如果不匹配。将抛出异常
  # 2. true: 启动时会对数据库中所有表进行更新操作，如果表存在，不做处理，反之，自动创建表
  # 3. create_drop: 启动时自动创建表，关闭时自动删除表
  # 4. drop_create: 启动时，删除旧表，再创建新表
  database-schema-update: false # 设置为 false，可通过 https://github.com/flowable/flowable-sql 初始化
  db-history-used: true # flowable6 默认 true 生成信息表，无需手动设置
  check-process-definitions: false # 设置为 false，禁用 /resources/processes 自动部署 BPMN XML 流程
  history-level: full # full：保存历史数据的最高级别，可保存全部流程相关细节，包括流程流转各节点参数
#  database-schema: SYT_NEW_PORTAL
  async-executor-activate: false

# Thauth认证 IPAcl
thauth:
  appKey: PORTAL:abcd123
  authServerIP: *************
#  authServerIP: *************
  authServerPort: 3335

#cas认证对接
cas:
  casServerUrl: http://cas.cqkjzyxy.com/cas
  proxyValidate: http://cas.cqkjzyxy.com/cas/proxyValidate

# 学校环境改为具体的地址
allowed:
  origins:
